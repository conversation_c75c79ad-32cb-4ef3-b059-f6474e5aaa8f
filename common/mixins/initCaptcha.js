import initNECaptchaWithFallback from '@/utils/yidun-captcha';
import { sendPhoneCode ,sendGetSmsCode} from '@/config/api/register.js';

export default {
  data() {
    return {
      isFlag:false,
      captchaIns: null,
      captchaButton: null,
    };
  },
  mounted() {
    this.initCaptcha();
  },
  beforeUnmount() {
    this.captchaButton = null;
    this.captchaIns.destroyCaptcha()
    // 必须删除相关元素，否则再次mount多次调用 initAliyunCaptcha 会导致多次回调 captchaVerifyCallback
    var mask = document.getElementById('aliyunCaptcha-mask');
    if (mask) {
      mask.remove();
    }
    var popup = document.getElementById('aliyunCaptcha-window-popup');
    if (popup) {
      popup.remove();
    }
  },
  methods: {
    getInstance(instance) {
      this.captchaIns = instance;
    },
    async captchaVerifyCallback(captchaVerifyParam) {
      let captchaResult = false;
      let bizResult = false;
      try {
        const res = await sendGetSmsCode({
          telephone:this.telephone,
          validate:captchaVerifyParam,
        });
        if (res.code == 200) {
          bizResult = true;
          captchaResult = true;
          uni.showToast({
            title: '验证码发送成功！',
            icon: 'none',
          });
          this.key = res.key;
          this.codeCallBack && this.codeCallBack();
        }
      } finally {
        // this.captchaIns.refresh();
      }
      return {
        captchaResult: captchaResult,
        bizResult: bizResult,
      }
    },
    // 验证通过后调用
    onBizResultCallback() {
      this.captchaIns.hide()
      console.log('onBizResultCallback');
      // this.countDown();
      // this.doSendSmsCode();
    },
    initCaptcha() {
      this.captchaButton = document.getElementById('captcha-button');

      window.initAliyunCaptcha({
        SceneId: 'l34lhmeq', // 场景ID。根据步骤二新建验证场景后，您可以在验证码场景列表，获取该场景的场景ID
        prefix: 'b7wjf0', // 身份标。开通阿里云验证码2.0后，您可以在控制台概览页面的实例基本信息卡片区域，获取身份标
        mode: 'popup', // 验证码模式。popup表示要集成的验证码模式为弹出式。无需修改
        element: '#captcha-element', // 页面上预留的渲染验证码的元素，与原代码中预留的页面元素保持一致。
        button: '#captcha-button', // 触发验证码弹窗的元素。button表示单击登录按钮后，触发captchaVerifyCallback函数。您可以根据实际使用的元素修改element的值
        captchaVerifyCallback: this.captchaVerifyCallback, // 业务请求(带验证码校验)回调函数，无需修改
        onBizResultCallback: this.onBizResultCallback, // 业务请求结果回调函数，无需修改
        getInstance: this.getInstance, // 绑定验证码实例函数，无需修改
        slideStyle: {
          width: 320,
          height: 40,
        }, // 滑块验证码样式，支持自定义宽度和高度，单位为px。其中，width最小值为320 px
        language: 'cn', // 验证码语言类型，支持简体中文（cn）、繁体中文（tw）、英文（en）
      });
      // initNECaptchaWithFallback(
      //   {
      //     captchaId: '3455bd8a6484410ea146980a113839aa',
      //     mode: 'popup',
      //     apiVersion: 2,
      //     onVerify: (err, data) => {
      //       if (err) return;
      //       this.doSendSmsCode(data);
      //     },
      //   },
      //   (instance) => {
      //     this.captchaIns = instance;
      //   },
      // );
    },
    /**
     * 获取验证码
     */
    sendCode(telephone, codeCallBack) {
      if (telephone == '') {
        uni.showToast({
          title: '请输入手机号码',
          icon: 'none',
        });
        return;
      }
      if (!/^1[3456789]\d{9}$/.test(telephone)) {
        uni.showToast({
          title: '请输入正确的手机号码',
          icon: 'none',
        });
        return false;
      }

      uni.setStorageSync('LOGIN_PHONE',telephone) // 存储手机号，安卓切换app会刷新页面导致手机号重填

      this.telephone = telephone;
      this.codeCallBack = codeCallBack;
      console.log(this.captchaIns,1112223333);
      
      this.captchaIns && this.captchaIns.show();
    },

    doSendSmsCode(data) {
      uni.showLoading({
        title: '拼命加载中',
      });
      sendPhoneCode({
        telephone: this.telephone,
        validate: data.validate,
      })
        .then((res) => {
          if (res.code == 200) {
            uni.showToast({
              title: '验证码发送成功！',
              icon: 'none',
            });
            this.key = res.key;
            this.codeCallBack && this.codeCallBack();
          }
          uni.hideLoading();
        })
        .finally(() => {
          this.captchaIns.refresh();
        });
    },
  },
};
