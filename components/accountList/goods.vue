<template>
  <view>
    <KKEmpty
      v-if="(!accountShopList || !accountShopList.length) && !isLoading"
    />
    <view
      v-show="accountShopList && accountShopList.length"
      :class="[isBig ? 'big' : 'sm']"
      class="goods_list_wrap"
    >
      <view
        v-for="(item, index) in newAccountList"
        :key="index"
        @click="handelClick(item)"
      >
        <GamePropsCard v-if="goodsType==='vgoods'" :item="item"/>
        <view v-else class="goods_item">
          <view class="item_img">
            <image :src="item.pic" mode="aspectFill"></image>

            <template v-if="!isSimple">
              <!-- 天赏石 -->
              <view
                v-if="item.tssnum"
                :class="['tssnum'+getTssClazz(item.tssnum)]"
                class="tssnum"
              >
                <view class="innernum">{{ item.tssnum }}天赏</view>
              </view>
              <!-- 女娲石 -->
              <view
                v-if="item.nvsNum"
                :class="['nwsnum'+getTssClazz(item.nvsNum)]"
                class="tssnum"
              >
                <view class="innernum">{{ item.nvsNum }}女娲石</view>
              </view>
              <!-- 是否已成交 -->
              <image
                v-if="item.stock == 0 || item.stock == 1"
                src="../../static/old/soled.jpg"
                class="soled_pic"
                style="width: 100%"
                mode="aspectFill"
              ></image>
            </template>
          </view>

          <view v-if="isBig" class="spaceBetween" style="margin-top: 24rpx">
            <!-- 顶级账号等标签 -->
            <view class="spaceStart">
              <!-- <image
              src="../../assets/imgs/account_tag2.svg"
              mode="heightFix"
              style="height:32rpx"
            /> -->
              <image
                v-if="item.isDj && isBig"
                src="../../assets/imgs/account_tag3.svg"
                mode="heightFix"
                style="height: 40rpx; margin-left: 10rpx"
              />
              <image
                v-if="item.gameGoodsFangxin"
                src="../../assets/imgs/account_tag1.svg"
                mode="heightFix"
                style="height: 40rpx; margin-left: 10rpx"
              />
            </view>
          </view>
          <view style="flex: 1">
            <!-- 主内容 -->
            <view class="item_title">
              <rich-text
                v-if="item.subTitle"
                :preview-img="false"
                :show-img-menu="false"
                :lazy-load="false"
                :nodes="tedianFilter(item.subTitle, item)"
                class="text_linTwo"
              />
              <!-- :class="isBig ? 'text_linThree' : 'text_linTwo'" -->
            </view>
            <!-- 区服信息 -->
            <view v-if="!isBig" class="ext_item">
              <text v-if="isSimple" class="brand-txt"
                >{{ productCategoryName}}｜</text
              >
              <!-- item.brandName  -->
              <text>{{ item.gameAccountQufu }}</text>
            </view>

            <!--发布时间 浏览人数 收藏人数 -->
            <template v-if="isBig">
              <view class="spaceBetween">
                <view class="ext_item">{{ item.gameAccountQufu }}</view>
                <!-- <view  style="font-size: 22rpx;">{{ item.productSn }}</view> -->
              </view>

              <view class="item-tedian textOneLine">{{ item.tdTxt }}</view>
              <view class="spaceBetween">
                <view
                  class="item_info spaceStart"
                  style="flex-wrap: wrap; flex: 1"
                >
                  <view class="item_price"
                    ><text style="font-size: 24rpx">￥</text
                    >{{ item.price }}</view
                  >
                  <view style="margin-left: 2rpx;" v-if="item.jjPrice> 0" class="jj-box">
                    <image
                      src="../../assets/imgs/icon-reducePrice2.png"
                      mode="widthFix"
                      style="width: 24rpx"
                    />
                    已降价 ￥{{ item.jjPrice }}
                  </view>
                </view>
                <view class="spaceStart">
                  <view class="item_time">
                    {{ item.publishTime | timeformatday }}
                  </view>
                  <text style="margin-left: 28rpx">
                    {{ item.gameSysinfoReadcount }}人看过</text
                  >
                </view>
              </view>
            </template>

            <template v-if="!isBig">
              <view class="item_info spaceStart">
                <view class="item_time">
                  {{ item.publishTime | timeformatday }}
                </view>
                <text style="margin-left: 28rpx">
                  {{ item.gameSysinfoReadcount }}人看过</text
                >
              </view>
            </template>

            <!--最后一行信息 标签 价格-->
            <view v-if="!isBig" class="item_bottom spaceBetween">
              <!-- 顶级账号标签 -->
              <view class="spaceStart">
                <image
                  v-if="item.isDj"
                  src="../../assets/imgs/account_tag3.svg"
                  mode="widthFix"
                  style="width: 120rpx"
                />
              </view>
              <view class="spaceEnd" style="flex-wrap: wrap">
                <view v-if="item.jjPrice > 0 && !isSimple" class="jj-box">
                  <image
                    src="../../assets/imgs/icon-reducePrice2.png"
                    mode="widthFix"
                    style="width: 24rpx"
                  />
                  已降价 ￥{{ item.jjPrice }}
                </view>
                <view class="item_price">￥{{ item.price }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { computed } from '@/uni_modules/uview-ui/libs/mixin/mixin';
import GamePropsCard from './GamePropsCard.vue';

export default {
  components: {
    GamePropsCard,
  },
  props: {
    goodsType: {
      type: String,
      default: '',
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    accountShopList: {
      type: Array,
      default: () => [],
    },
    isBig: {
      type: Boolean,
      default: false,
    },
    isSimple: {
      type: Boolean,
      default: false,
    },
  },
  computed:{
    newAccountList(){
      return this.accountShopList.map(ele=>{
        return {
          ...ele,
          nvsNum:this.getNWSNum(ele),
          jjPrice:this.getJJPrice(ele),
          isDj:this.hasDj(ele),
          tdTxt:this.getTdTxt(ele)
        }
      })
    },
  },
  created() {
    console.log('aaaa', this.accountShopList);
     
  },
  methods: {
    getTssClazz(num) {
      let l = num.toString().length;
      return `${l}`;
    },
    hasDj(item) {
      const attr =
        item.attrValueList.filter((i) => i.name === '账号专区')[0] || {};
      return attr.values && attr.values.includes('顶级账号');
    },
    getNWSNum(item){
      const result = item.attrValueList.filter(ele=>['未使用女娲石数量','已使用女娲石数量'].includes(ele.name))
      .reduce((accumulator, ele) => accumulator + parseInt(ele.value), 0);
      let l = result.toString().length;
      console.log('女娲石',result,l)
      return result
    },
    getJJPrice(item) {
      const data = JSON.parse(item.priceHistory || '[]');
      data.sort((a, b) => a.changeTime - b.changeTime);
      if (!data.length) {
        return '';
      }
      return data[0].price - data[data.length - 1].price;
    },
    getTdTxt(item) {
      if (item.productCategoryId != 75) {
        // 诛仙世界
        return item.description;
      } else {
        // 逆水寒等
        const str1 = item.attrValueList
          .filter((ele) =>
            ['稀有外观', '天赏祥瑞', '天赏发型'].includes(ele.name),
          )
          .map((ele) => ele.value)
          .join(' ');
        const str2 = item.attrValueList
          .filter(
            (ele) =>
              ['灵韵数量', '天霓染'].includes(ele.name) &&
              ele.value &&
              ele.value !== '0',
          )
          .map((ele) => ele.name + ele.value)
          .join(' ');
        return item.description?item.description:(str1 + ' ' + str2).replace('数量', '').replace(/,/g, ' ');
      }
    },
    tedianFilter(text, item) {
      if (!text) {
        return text;
      }
      // let l = text.length;
      // if (text[l - 1] === '】') {
      //   let lastIndex = text.lastIndexOf('【');
      //   if (lastIndex !== -1) {
      //     // 如果找到了【，则截取到这个【之前的所有字符
      //     text = text.slice(0, lastIndex);
      //   }
      // }
      return text
        .replace(/\[核\]/g, '')
        .replace(/\[绝\]/g, '')
        .replace(/\[钱\]/g, '')
        .replace(/\[<span>核<\/span>\]/g, '')
        .replace(/\[<span>绝<\/span>\]/g, '')
        .replace(/\[<span>钱<\/span>\]/g, '');
    },
    handelClick(item) {
      this.$emit('click', item);
    },
  },
};
</script>

<style lang="scss" scoped>
.goods_list_wrap {
  .goods_item {
    color: #969696;
    font-family: 'PingFang SC';
    font-size: 24rpx;
    font-weight: 400;
    box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.05);
  }

  &.big {
    .goods_item {
      padding: 16rpx;
      margin-bottom: 24rpx;
      background: #fff;
      border-radius: 24rpx;
    }
    .item_img {
      width: 100%;
      height: 364rpx;
    }

    .item_title {
      margin-top: 16rpx;
    }

    .item_info {
      margin: 12rpx 0;
    }
    .item_price {
      font-size: 32rpx;
    }
    .item-tedian {
      font-size: 22rpx;
      font-weight: 500;
      color: #ff720c;
    }
    .ext_item {
      font-size: 26rpx;
      margin: 10rpx 0;
    }
  }
  &.sm {
    .goods_item {
      padding: 12rpx 16rpx;

      //左右排列
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
      overflow: hidden;

      margin-bottom: 16rpx;
      background: #fff;
      border-radius: 16rpx;
      -webkit-border-radius: 16rpx;
      -moz-border-radius: 16rpx;
      -ms-border-radius: 16rpx;
      -o-border-radius: 16rpx;

      .item_img {
        width: 184rpx;
        margin-right: 18rpx;
        height: 170rpx;
      }

      .item_info {
        margin-top: 8rpx;
      }

      .item_bottom {
        margin-top: 8rpx;
      }

      .brand-txt {
        color: #ffb74a;
        position: relative;
      }
      .jj-box {
        margin-right: 0rpx;
        font-size: 20rpx;
      }

      .tssnum {
        width: 180rpx;
        height: 45rpx;
        line-height: 40rpx;
        left: 0;
        .innernum {
          margin-left: 60rpx;
          font-size: 20rpx;
        }
      }
    }
  }

  .item_img {
    position: relative;
    background: #9a9a9a;
    border-radius: 12rpx;
    overflow: hidden;
    -webkit-border-radius: 12rpx;
    -moz-border-radius: 12rpx;
    -ms-border-radius: 12rpx;
    -o-border-radius: 12rpx;

    & > image {
      width: 100%;
      height: 100%;
    }

    .soled_pic {
      position: absolute;
      right: 2rpx;
      bottom: 2rpx;
      z-index: 4;
      width: 80rpx;
    }
  }

  .item_title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
  }

  .jj-box {
    display: flex;
    align-items: center;
    background: #fff2e6;
    margin-left: 0rpx;
    padding: 8rpx 6rpx;
    color: #ff7a05;
    font-size: 20rpx;
    font-weight: 400;
    border-radius: 40rpx;
  }

  .item_price {
    color: #ff720c;
    font-size: 24rpx;
    font-weight: 600;
  }

  .smallIcon_acc {
    display: inline-block;
    vertical-align: bottom;
    margin-right: 4rpx;
  }

  .tssnum {
    position: absolute;
    z-index: 1;
    top: 4rpx;
    left: 4rpx;
    width: 240rpx;
    height: 60rpx;
    line-height: 54rpx;
    text-align: left;

    .innernum {
      margin-left: 84rpx;

      font-size: 24rpx;
      font-weight: 500;
      display: block;
      color: #000;
      font-family: YouSheBiaoTiHei;
      font-style: normal;
      font-weight: 400;
    }
  }

  .tssnum1 {
    background: url('../../assets/imgs/acc_tss_bg1.png') no-repeat top;
    background-size: cover;
  }

  .tssnum2 {
    background: url('../../assets/imgs/acc_tss_bg2.png') no-repeat top;
    background-size: cover;
  }

  .tssnum3 {
    background: url('../../assets/imgs/acc_tss_bg3.png') no-repeat top;
    background-size: cover;
  }

  .tssnum4 {
    background: url('../../assets/imgs/acc_tss_bg3.png') no-repeat top;
    background-size: cover;
  }

  .nwsnum1 {
    background: url('../../assets/imgs/acc_nws_bg1.png') no-repeat top;
    background-size: cover;
  }

  .nwsnum2 {
    background: url('../../assets/imgs/acc_nws_bg2.png') no-repeat top;
    background-size: cover;
  }

  .nwsnum3 {
    background: url('../../assets/imgs/acc_nws_bg3.png') no-repeat top;
    background-size: cover;
  }

  .nwsnum4 {
    background: url('../../assets/imgs/acc_nws_bg3.png') no-repeat top;
    background-size: cover;
  }

  

  .ext_item {
    position: relative;
    letter-spacing: 1px;
    margin-top: 4rpx;
    font-size: 26rpx;

    &::before {
      content: '';
      white-space: pre;
      position: absolute;
      background: #f4f4f4;
      width: 2rpx;
      height: 18rpx;
      top: 50%;
      right: -8rpx;
      transform: translate(0, -50%);
      -webkit-transform: translate(0, -50%);
      -moz-transform: translate(0, -50%);
      -ms-transform: translate(0, -50%);
      -o-transform: translate(0, -50%);
    }

    &:last-child::before {
      display: none;
    }
  }
}
</style>
