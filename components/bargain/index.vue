<template>
    <!--  议价 -->
    <TipPanel :has-cancel="false" :has-confirm="false" sub-title="发起议价" @cancel="handelCancel">
      <view class="spaceStart game-box">
        <view class="game-img">
          <image :src="dataInfo.pic" mode="scaleToFill" style="width: 100%;height: 100%;" />
        </view>
        <view class="game-info">
          <view class="text_linThree">{{ dataInfo.name }}</view>
          <view class="price">商品价格：<text class="c-primary">¥{{ dataInfo.price }}</text></view>
          <view class="top-price"
            >当前最高出价：<text class="c-primary number">{{
              maxPrice ? '¥' + maxPrice : '暂无'
            }}</text></view
          >
          <view v-if="historyTopPrice"
            class="top-price"
            >历史最高出价：<text class="c-primary number">¥{{historyTopPrice}}</text>
          </view>
        </view>
      </view>
      <view class="tip-box">
        <view class="title">注意事项<text style="color:red"></text></view>
        <view> 1.砍价无需支付议价金，砍价金额须高于商品原价格的70%</view>
        <view>2.发起砍价后，若卖家未响应，砍价会在24小时后自动取消</view>
        <view>3.砍价成功后，买家需在12小时内完成支付，超时议价自动取消</view>
        <view>4.砍价成功后支付前，其他买家可继续砍价或购买</view>
        <!-- <view>3.砍价需支付出价金额的5% (最高2000) 的意向金</view>
        <view>4.议价成功24小时内未能成功支付号款，议价金将全额赔付给卖家</view> -->
        <!-- <view class="agree-box spaceStart" @click="checkAgree = !checkAgree">
          <IconFont
            v-show="!checkAgree"
            :size="15"
            icon="unchecked"
            style="margin-right: 10rpx;display: inline-block;vertical-align: bottom;"
          />
          <image
            v-show="checkAgree"
            src="../../assets/imgs/agreement_checked.svg"
            mode="heightFix"
            style="width:15px;height:15px;margin-right: 10rpx"
          />
          我已阅读并同意<text @click.stop="toPage">购买须知</text>
        </view> -->
      </view>
  
      <!-- <view style="margin-top:50rpx" class="foot-box spaceBetween">
        <view class="input-box-border">
          <view class="input-box">
            <view class="title gradient-primary">报价</view>
            <input v-model="price" type="number" style="border: none; flex: 1"  placeholder="请输入报价"/>
          </view>
        </view>
   
      </view> -->
      <view class=“spaceCenter”>
        <view 
          class="input-box-border" 
         
        >
          <view class="input-box"  @mouseenter="inpiutBoxMouseenter" 
          @mouseleave="inpiutBoxMouseleave">
            ¥
            <input 
              v-if="isInput||!price" 
              :value="price" 
              @input="handleInputChange" 
              type="digit" 
              style="border: none; flex: 1" 
              placeholder="请输入报价"
              @focus="inputFocus = true"
              @blur="inputFocus = false"
            />
            <view v-else style="font-size:32rpx">{{price}}</view>
          </view>
        </view>
      </view>
      <view class="spaceCenter">允许出价范围<text class="c-primary">¥{{maxPrice|| Math.ceil(dataInfo.price * 0.7) }}～¥{{ dataInfo.price }}</text></view>
      <view class="" style="width:100%;background:linear-gradient(90deg,#ffedd380,#ffedd380 46%,#ffedd380);height:20px">
        <view>
          <slider class="my-slider-Box" block-size="20"  :value="sliderValue" activeColor="rgba(255,114,12,0)" @change="sliderChange"  @changing="sliderChange" :min="maxPrice|| Math.ceil(dataInfo.price * 0.7)" :max="dataInfo.price" />
        </view>
      </view>
      <view class="spaceCenter">
        <view
          class="kk-btn orange"
          style="width: 190rpx;margin-top:10rpx"
          @click.stop="handelConfirm"
          >提交</view
        >
      </view>
    </TipPanel>
  </template>
  <script>
  export default {
    props: {
      dataInfo: {
        type: Object,
        default: () => {},
      },
      maxPrice: {
        type: Number,
        default: null,
      },
      historyTopPrice: { // 历史最高出价
        type: Number,
        default: null,
      },
    },
    data() {
      return {
        price:  '',
        checkAgree: false,
        sliderValue:this.dataInfo && this.dataInfo.price ? this.dataInfo.price : '',
        isInput:false,
        isOneFlag:true,
      };
    },
    methods: {
      inpiutBoxMouseenter(){
        this.isInput=true
      },
      inpiutBoxMouseleave(){
        this.isInput=false
      },
      sliderChange(e) {
        this.isInput=false
        let value = e.detail.value;
        this.price = value;
        this.sliderValue=value
      },
      handleInputChange(e){
        const val = Number(e.target.value);
        this.price = val;
        const comparePrice = this.maxPrice ? this.maxPrice : Math.ceil(this.dataInfo.price * 0.7);
        console.log(val,comparePrice,this.dataInfo.price,222222)
        if (val >= comparePrice && val <= this.dataInfo.price) {
          this.sliderValue = val;
        }
        // this.price = e.target.value;
        // this.sliderValue=e.target.value;
      },
      toPage(){
        uni.navigateTo({
          url: '/pages/noticeDetail/noticeDetail?id=323&from=helper'
        });
      },
      handelConfirm() {
        // if (!this.checkAgree) {
        //   uni.$u.toast('请先阅读并同意购买须知');
        //   return;
        // } else
         if (!this.price) {
          uni.$u.toast('请填写价格');
          return;
        } else if (this.price > this.dataInfo.price) {
          uni.$u.toast(`报价须小于商品原价${this.dataInfo.price}元`);
          return
        }
        this.$emit('confirm',this.price);
      },
      handelCancel(){
        this.$emit('cancel');
      }
    },
  };
  </script>
  <style lang="scss" scoped>
  .game-box {
    margin-top: 40rpx;
    
    border-radius: 24rpx;
    border: 0.5px solid #ffb74a;
    background: #fff;
  
    padding: 20rpx;
  
    color: #000;
    font-size: 20rpx;
    font-weight: 500;
  
    .game-img {
      width: 192rpx;
      height: 170rpx;
      border-radius: 12rpx;
      overflow: hidden;
  
      margin-right: 20rpx;
    }
  
    .game-info {
      flex: 1;
    }
  
    .price {
      color: #969696;
      font-size: 24rpx;
      font-weight: 400;
  
      margin: 16rpx 0;
    }
    .top-price {
      color: #1b1b1b;
      font-size: 16rpx;
      font-weight: 400;
  
      .number {
        font-size: 32rpx;
        font-weight: 600;
      }
    }
  }
  .tip-box {
    color: #969696;
    font-size: 20rpx;
    font-weight: 400;
    line-height: 17px; /* 170% */
  
    margin-top: 34rpx;
  
    .title {
      color: #1b1b1b;
      font-family: YouSheBiaoTiHei;
      font-size: 24rpx;
      font-weight: 400;
  
      margin-bottom: 8rpx;
    }
  
    .agree-box {
      margin-top: 26rpx;
      margin-bottom: 50rpx;
  
      text {
        color: #ffb74a;
        text-decoration-line: underline;
        text-decoration-style: solid;
        text-decoration-skip-ink: none;
        text-decoration-thickness: auto;
        text-underline-offset: auto;
        text-underline-position: from-font;
      }
    }
  }
  
  .input-box-border{
    // border-radius: 80rpx;
    // box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
    // padding: 4rpx;
    // @include mainColor();
  
    display: flex;
    align-items: center;
    justify-content:center;
    .input-box {
      border-radius: 80rpx;
      // border: 2px solid #ff7a00;
      background: transparent;
    
      display: flex;
      width: 248rpx;
      padding: 16rpx 40rpx 16rpx 24rpx;
      align-items: center;
      // justify-content:center;
      flex-shrink: 0;
    
      color: #ff720c;
      font-family: Inter;
      font-size: 28rpx;
      font-weight: 600;
      line-height: normal;
      margin-left:160rpx;
      // text-align: center;
      .title {
        font-family: YouSheBiaoTiHei;
        font-size: 36rpx;
        font-weight: 400;
        margin-right: 24rpx;
      }
  
      /deep/ .uni-input-input{
        letter-spacing: -0.56px;
        
      }
    }
  }
  .my-slider-Box /deep/ .uni-slider-handle-wrapper{
    height:32rpx;
    margin-top:-10rpx;
    background:linear-gradient(270deg,#ff6a6a,#ffbc6a 49%,#ffe74b)
  }
  </style>
  