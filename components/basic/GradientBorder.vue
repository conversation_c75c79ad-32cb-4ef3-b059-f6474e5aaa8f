<template>
  <view :style="contentStyle" class="gradient-border">
    <view :style="contentStyle" class="content">
      <slot />
    </view>
  </view>
</template>
<script>
export default {
  name: 'GradientBorder',
  props: {
    noTopLeft: {
      type: Boolean,
      default: false,
    },
    noTopRight: {
      type: Boolean,
      default: false,
    },
    color: {
      type: String,
      default: '',
    },
    round: {
      type: Number,
      default: 12,
    },
    borderSize: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      contentStyle: '',
      activeIndex: 0,
    };
  },
  watch: {
    noTopLeft(v) {
      this.setStyle();
    },
    noTopRight(v) {
     this.setStyle();
    },
  },
  mounted(){
    this.setStyle()
  },
  methods: {
    setStyle() {
      let str = `border-radius:${this.round}px;`;
      str += `border-top-left-radius:${this.noTopLeft?0:this.round}px;`;
      str += `border-top-right-radius:${this.noTopRight?0:this.round}px;`;
      str += `padding:${this.borderSize}px;`;
      this.contentStyle = str
    },
  }
};
</script>

<style lang="scss" scoped>
$border-color: rgba(255, 225, 195, 0.8);
.gradient-border {
  position: relative;
  box-sizing: border-box;
  // background: linear-gradient(180deg, #fff 0%, #FFC085 100%);
  // background: linear-gradient(180deg, $border-color 0%, #FFC085 100%);
  box-shadow:(10px 10px 50px rgba(255, 107, 0, 0.10));
  backdrop-filter: blur(50px);

  background: linear-gradient(180deg, #fff, #ffe1c3);
  // background: var(
  //   --Main-color,
  //   radial-gradient(
  //     238.39% 44.19% at 96.59% 31.25%,
  //     rgba(255, 255, 255, 0.1) 0%,
  //     rgba(255, 255, 255, 0) 100%
  //   ),
  //   radial-gradient(
  //     182.56% 55.34% at 5.68% 100%,
  //     rgba(246, 251, 34, 0.31) 0%,
  //     rgba(255, 158, 69, 0) 100%
  //   ),
  //   radial-gradient(
  //     137.51% 118.3% at 32.95% 0%,
  //     rgba(255, 137, 137, 0.83) 21.25%,
  //     rgba(255, 169, 106, 0.51) 88.62%
  //   ),
  //   radial-gradient(
  //     178.09% 220.16% at 94.89% -132.81%,
  //     #ff7a00 67.59%,
  //     rgba(255, 199, 0, 0.38) 100%
  //   ),
  //   #fff500
  // );
  padding: 1px;
  height: auto;

  .content {
    position: relative;
    background: #fff;
    top: 0;
    overflow: hidden;
  }
}
</style>
