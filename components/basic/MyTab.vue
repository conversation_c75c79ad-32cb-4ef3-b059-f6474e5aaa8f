<template>
  <view :style="{ height: bodyHeight }" :class="[theme]" class="tab-wrap">
    <view :class="tabHeadClass" class="tab-head">
      <view
        v-for="(item, index) in tabs"
        :key="item"
        :class="btnClass(index)"
        :style="btnWidth ? `max-width:${btnWidth*2}rpx` : ''"
        class="tab-btn"
        @click="handelTabClick(index)"
      >
        <slot :data="item" :name="`tab-btn-${index}`">
          <span :class="{'gradient-main-primary':theme==='login' && activeIndex === index}"> {{ item }}</span>
        </slot>

        <i
          v-if="index !== tabs.length - 1 && activeIndex === index || tabs.length===1"
          class="right"
        ></i>
        <i v-if="index !== 0 && activeIndex === index" class="left"></i>
      </view>
    </view>
    <!-- <GradientBorder
      :no-top-left="activeIndex === 0"
      :no-top-right="activeIndex === tabs.length - 1 && tabs.length>1"
      :class="borderClass"
      style="flex:1;height:100%"
    > -->
    <view :class="tabBodyClass" class="tab-body">
      <slot :data="activeIndex" />
    </view>
    <!-- </GradientBorder> -->
  </view>
</template>
<script>
export default {
  name: 'MyTab',
  props: {
    tabs: {
      type: Array,
      default: () => [],
    },
    borderClass: {
      type: String,
      default: '',
    },
    btnWidth: {
      type: Number,
      default: 0,
    },
    bodyHeight: {
      type: String,
      default: '100%',
    },
    theme: {
      type: String,
      default: 'center', // orange
    },
  },
  data() {
    return {
      activeIndex: 0,
    };
  },
  computed: {
    tabHeadClass() {
      if (this.activeIndex === 0) {
        return 'pd-left-0';
      } else if (this.activeIndex === this.tabs.length - 1) {
        return `pd-right-0`;
      } else return '';
    },
    tabBodyClass() {
      if (this.activeIndex === 0) {
        return 'no-top-left';
      } else if (
        this.activeIndex === this.tabs.length - 1 &&
        this.tabs.length > 1
      ) {
        return `no-top-right`;
      } else return '';
    },
  },
  methods: {
    btnClass(index) {
      const classArr = [];
      if (this.activeIndex === index) {
        classArr.push('selected');
      }
      return classArr;
    },
    handelTabClick(index) {
      this.activeIndex = index;
      this.$emit('click', index);
    },
  },
};
</script>

<style lang="scss" scoped>
$pd-x: 12rpx;
.tab-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
}
@mixin switch-theme($border-color, $bg-color, $border, $b-radius) {
  .tab-head {
    display: flex;
    justify-items: flex-start;
    align-items: flex-end;
    // position: relative;
    // z-index: 9;
    padding: 0 $pd-x;

    &.pd-left-0 {
      padding-left: 0;
    }
    &.pd-right-0 {
      padding-right: 0;
    }
  }
  .tab-btn {
    position: relative;
    flex: 1;
    cursor: pointer;
    text-align: center;
    color: $uni-color-paragraph;
    padding: 18rpx 0;
    border-radius: $b-radius;
    font-size: 24rpx;
    line-height: 27rpx;
    background: #f5f5f5;
    // z-index: 2;

    &.selected {
      font-family: YouSheBiaoTiHei;
      z-index: 1;
      color: $uni-color-primary;
      font-size: 32rpx;
      background: $bg-color;
      background: #fff;
      padding-top: 20rpx;
      border: solid $border $border-color;
      border-bottom: none;
      border-radius: $b-radius * 1.5;
      border-bottom-right-radius: 0;
      border-bottom-left-radius: 0;
      border-bottom: none;
      z-index: 1;
      bottom: -6rpx;
    }

    .left {
      &::before {
        // 遮盖三角区域
        content: '';
        position: absolute;
        left: -($b-radius);
        bottom: 0;
        width: $b-radius;
        height: $b-radius;
        background: #fff;
      }

      &::after {
        // 画圆角线
        content: '';
        position: absolute;
        left: -($b-radius);
        bottom: ($b-radius)/6;
        width: $b-radius;
        height: $b-radius;
        border-bottom-right-radius: $b-radius;
        background: $border-color;
      }
    }

    .right {
      &::before {
        // 遮盖三角区域
        content: '';
        position: absolute;
        right: -($b-radius);
        bottom: 0;
        width: $b-radius;
        height: $b-radius;
        background: #fff;
      }

      &::after {
        // 画圆角线
        content: '';
        position: absolute;
        right: -($b-radius);
        bottom: ($b-radius)/6;
        width: $b-radius;
        height: $b-radius;
        border-bottom-left-radius: $b-radius;
        background: $border-color;
      }
    }
  }
  .tab-body {
    background: $bg-color;
    border-radius: $b-radius;
    min-height: 100rpx;
    height: 100%;
    position: relative;
    // padding: 2rpx;
    // overflow: hidden;
    box-sizing: border-box;
    border: solid $border $border-color;

    &.no-top-left {
      border-top-left-radius: 0;

      &::before {
        border-top-left-radius: 0;
      }
    }

    &.no-top-right {
      border-top-right-radius: 0;
    }

    // &::before {
    //   content: '';
    //   position: absolute;
    //   z-index: 0;
    //   left: 0rpx;
    //   right: 0rpx;
    //   top: 0rpx;
    //   bottom: 0rpx;
    //   background: red;//linear-gradient(180deg, $border-color 0%, #ffc085 100%);
    //   border-radius: $b-radius;

    // }

    // &::after {
    //   content: '';
    //   position: absolute;
    //   background: $bg-color;
    //   z-index: -1;
    //   left: 2rpx;
    //   right: 2rpx;
    //   top: 2rpx;
    //   bottom: 2rpx;
    //   border-radius: $b-radius;

    // }
  }
}
/* 个人中心 */
.center{
  @include switch-theme(#ffe1c3,#fff,2rpx,24rpx);
  &.tab-wrap {
    .tab-btn{
      margin-right: 8rpx;
      padding-bottom: 28rpx;
      bottom: -20rpx;

      &.selected{
        bottom: -6rpx;
        padding-bottom: 18rpx;
      }

      &:last-child{
        margin-right: 0;
      }

      .left::after,.right::after{
        bottom: 4rpx;
        width: 32rpx;
        height: 32rpx;

        background: transparent;
        // background: blue;
        // display: none;

        
        border: solid 2rpx  #ffe1c3;
      }

      .left::after{
        left: -34rpx;
        border-left: none;
        border-top: none;
        border-bottom-right-radius:36rpx;
      }
      .right::after{
        right: -34rpx;
        border-right: none;
        border-top: none;
        border-bottom-left-radius:36rpx;
        // rotate: (10deg);
      }
      
      .left::before,.right::before{
        bottom: 0px;
        width: 36rpx;
        height: 36rpx;
        // background: red;
        // display:none;
      }
      .left::before{
        left: -32rpx;
        clip-path: polygon(90% 0%,100% 100%, 0% 96%,62% 60% );
      }
      .right::before{
        clip-path: polygon(10% 0%,38% 60%, 100% 96%, 0% 100%);
        right: -32rpx;
      }
    }
  }
}
/* 游戏、商品详情 */
.orange {
  @include switch-theme(
    // rgba(255, 107, 0, 0.4),
    #ffe1c3,
    linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.95) 26.76%,
      rgba(255, 255, 255, 0.76) 53.13%
    ),
    4rpx,
    24rpx
  );

  &.tab-wrap {
    .tab-btn{
      margin-right: 8rpx;
      padding-bottom: 28rpx;
      bottom: -20rpx;

      &.selected{
        bottom: -6rpx;
        padding-bottom: 18rpx;
      }

      &:last-child{
        margin-right: 0;
      }

      .left::after,.right::after{
        bottom: 2rpx;
        width: 34rpx;
        height: 34rpx;

        background: transparent;
        // background: blue;
        // display: none;

        
        border: solid 4rpx  #ffe1c3;
      }

      .left::after{
        left: -38rpx;
        border-left: none;
        border-top: none;
        border-bottom-right-radius:18px;
      }
      .right::after{
        right: -38rpx;
        border-right: none;
        border-top: none;
        border-bottom-left-radius:18px;
      }
      
      .left::before,.right::before{
        bottom: 0px;
        width: 36rpx;
        height: 36rpx;
        // background: red;
        // display:none;
      }
      .left::before{
        left: -32rpx;
        clip-path: polygon(90% 0%,100% 100%, 0% 96%,58% 60% );
      }
      .right::before{
        clip-path: polygon(10% 0%,42% 60%, 100% 96%, 0% 100%);
        right: -32rpx;
      }
    }
  }
}
/* 密码登录 */
.login {
  @include switch-theme(
    #fcfcfc,
    linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.95) 26.76%,
      rgba(255, 255, 255, 0.76) 53.13%
    ),
    2rpx,
    48rpx
  );
  &.tab-wrap {
    // box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
    .tab-btn {
      font-family: YouSheBiaoTiHei;
      font-size: 36rpx;
      height: 80rpx;
      line-height: 50rpx;

      border-bottom-right-radius: 0;
      border-bottom-left-radius: 0;
      z-index: 1;

      &:first-child {
        margin-left: -10rpx;
      }
      &:last-child {
        margin-right: -10rpx;
      }

      &.selected {
        font-size: 36rpx;
        z-index: 2;
        padding-top: 18rpx;
        border-radius: 48rpx;

        &:first-child {
          margin-left: 0;
        }
        &:last-child {
          margin-right: 0;
        }
      }

      .left::before,
      .left::after,
      .right::before,
      .right::after {
        // 遮盖三角区域
        // background: red;
        bottom: 44rpx;
        z-index: -1;
        height: 40rpx;
      }
      .left::before,
      .left::after {
        left: -48rpx;
      }
      .right::before,
      .right::after {
        right: -48rpx;
      }

      .left::after,
      .right::after {
        // 画圆角线
        background: #f5f5f5;
      }
    }
    .tab-body {
      margin-top: -40rpx;
      z-index: 9;
      border-radius: 64rpx;
      border-top: none;

      &.no-top-left {
      border-top-left-radius: 0;

        &::before {
          border-top-left-radius: 0;
        }
      }

      &.no-top-right {
        border-top-right-radius: 0;
      }
    }
  }
}
</style>
