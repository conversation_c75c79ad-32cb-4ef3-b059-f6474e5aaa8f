<template>
  <uni-nav-bar
    :status-bar="true"
    :border="false"
    :title="title"
    :background-color="backgroundColor"
    :class="shadow ? 'shadow' : ''"
    :left-width="leftWidth*2+'rpx'"
    :right-width="rightWidth*2+'rpx'"
    :height="height*2+'rpx'"
    fixed="true"
  >
    <template slot="left">
      <view class="spaceCenter">
        <view class="icon-hot" @click="backFun">
          <IconFont :size="14" icon="back" />
        </view>
        <slot name="left" />
      </view>
    </template>

    <view :class="theme" class="title spaceCenter">
      <slot>{{ title }}</slot>
    </view>

    <template slot="right">
      <view class="spaceCenter">
        <slot name="right" />
      </view>
    </template>
  </uni-nav-bar>
</template>
<script>
export default {
  name: 'PageNav',
  props: {
    title: {
      type: String,
      default: '',
    },
    backgroundColor: {
      type: String,
      default: '#f3f3f3',
    },
    back: {
      type: Function,
      default: null,
    },
    theme: {
      type: String,
      default: 'orange', // black orange
    },
    shadow: {
      type: Boolean,
      default: false,
    },
    height: {
      type: Number,
      default: 54,
    },
    leftWidth: {
      type: Number,
      default: 29,
    },
    rightWidth: {
      type: Number,
      default: 29,
    },
  },
  data() {
    return {
      keyword: '',
    };
  },
  watch: {},
  mounted() {},
  methods: {
    backFun() {
      if (this.back) {
        this.back();
        return;
      }
      uni.navigateBackCustom();
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  font-weight: 400;
  text-align: center;
  width: 100%;

  &.orange {
    font-size: 44rpx;
    font-family: YouSheBiaoTiHei;
    color: $uni-color-primary;
  }

  &.black {
    color: #000;
    font-size: 28rpx;
    font-weight: 500;
  }
  &.themeTitle {
      font-size: 32rpx;
    font-family: YouSheBiaoTiHei;
    color: #FF720C;
  }

  .content-box {
    position: absolute;
    left: 32rpx;
    right: 32rpx;
    top: 0;
    bottom: 0;
    z-index: -1;
  }
}
.shadow {
  /deep/ .uni-navbar__header {
    box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 14;
    padding: 0 32rpx;
  }

  /deep/ .uni-navbar__header-container {
    padding: 0;
  }
  // /deep/ .uni-navbar__header-btns-right,
  // /deep/ .uni-navbar__header-btns-left {
  //   display: none;
  // }
}
</style>
