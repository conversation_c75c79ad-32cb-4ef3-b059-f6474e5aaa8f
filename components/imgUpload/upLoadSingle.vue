<template>
  <view class="uploadList_box">
    <view v-if="urlPic" class="picUpload_wrapSmall">
      <img :src="urlPic" class="picUpload_pic" alt="" @click="preView" />
      <view v-if="shCover" class="upLoadSingleShCoverBox">
        <view class="animationBox">
          </view>
          <view class="text">识别中</view>
      </view>
      <view class="delet_item" @click="deletPic">
        <IconFont :size="18" icon="del" />
      </view>
    </view>
    <view v-else class="picUpload_wrap uni-uploader__input-box" @click="chooseImg()">
    </view>
    <!-- 水印图片 -->
    <img
      ref="waterImg"
      style="width: 0; height: 0"
      src="../../static/old/water_pc.png"
      crossorigin="Anonymous"
    />
  </view>
</template>

<script>
import md5 from 'js-md5';
import ImageCompressor from 'js-image-compressor';
import { ossUpload } from '@/js_sdk/jason-alioss-upload/oss.js';

import { fileByBase64, base64ToFile } from '@/utils/util.js';

export default {
  props: {
    urlPic: {
      type: String,
      default: '',
    },
    nameKey: {
      type: String,
      default: '',
    },
    shCover: {
        type: Boolean,
        default: false,
      },
  },
  data() {
    return {
      isUploading: false, // 单张上传状态
    };
  },
  computed: {},
  mounted() {
  },
  methods: {
    preView() {
      uni.previewImage({
        current: this.urlPic,
        urls: [this.urlPic],
      });
    },
    deletPic() {
      this.$emit('upSuccsessSingle', '', this.nameKey);
    },
    beforeUpload(file) {
      return new Promise((resolve, reject) => {
        // 1.调用方法1： 把文件转换为base64字符串
        fileByBase64(file, async (base64) => {
          try {
            // 2. 调用方法2：把base64转换为Canvas
            let tempCanvas = await this.imgToCanvas(base64);
            //3.调用方法3： 写入水印到Canvas
            const canvas = await this.addWatermark(tempCanvas, '看看账号网');
            //4. 调用方法4：把Canvas转换为image文件
            const img = this.convasToImg(canvas);
            //5.调用方法5：被image转换为File文件(第二个参数为文件名)
            let newFile = base64ToFile(img.src, file.name);
            resolve(newFile);
          } catch (error) {
            reject(error)
          }
        });
      });
    },
    /**
     * Base64转成canvas
     * @param  base64
     */
    async imgToCanvas(base64) {
      // 创建img元素
      const img = document.createElement('img');
      img.setAttribute('src', base64);
      await new Promise((resolve) => (img.onload = resolve));
      // 创建canvas DOM元素，并设置其宽高和图片一样
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      // 坐标(0,0) 表示从此处开始绘制，相当于偏移。
      canvas.getContext('2d').drawImage(img, 0, 0);

      return canvas;
    },
    /**
     * canvas添加水印
     * @param  canvas 对象
     * @param text 水印文字
     */
    addWatermark(canvas, text) {
      const ctx = canvas.getContext('2d');

      return new Promise((resolve, reject) => {
        // 给上传的图片添加-水印图片
        // ctx.drawImage(this.$refs.waterImg, 0, 0)
        const img = new Image();
        img.src = '/static/old/water_pc.png';
        img.onload = function() {
            const pattern = ctx.createPattern(img, 'repeat');
            ctx.fillStyle = pattern;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            resolve(canvas)
        };
      })
  
      // const pattern = ctx.createPattern(this.$refs.waterImg, 'repeat');
      // ctx.fillStyle = pattern;
      // ctx.fillRect(0, 0, canvas.width, canvas.height);
      // return canvas;
    },
    /**
     * canvas转成img
     * @param {canvas对象} canvas
     */
    convasToImg(canvas) {
      // 新建Image对象，可以理解为DOM
      let image = new Image();
      // canvas.toDataURL 返回的是一串Base64编码的URL
      // 指定格式 PNG
      image.src = canvas.toDataURL('image/png');
      return image;
    },

    /**
     * 封面图片上传OSS-单张
     */
    chooseImg() {
      var that = this;
      uni.chooseImage({
        sourceType: ['camera', 'album'],
        sizeType: ['compressed'],
        count: 1,
        success: async (file) => {
          uni.showLoading({
            title: '图片上传中！',
          });
          try {
            // 上传OSS 组装数据
            let blobON = file.tempFiles[0]; // 文件file  file.tempFilePaths[0] 是 base 64
            // // // 图片压缩
            let blobO = await this.compressionImage(blobON);

            const ext = blobO.name.split('.').pop() || '';

            // // 图片 添加水印
            const testDate = await this.beforeUpload(blobO);
            // // 将新生成的图片file文件不转base64（uc 浏览器）
            const base64 = URL.createObjectURL(testDate);
            const { success, data } = await ossUpload(base64, ext);

            uni.hideLoading();
            if (success) {
              let newStr = data;
              that.$emit('upSuccsessSingle', newStr, that.nameKey);
            } else {
              uni.showToast({ icon: 'none', title: data });
            }
            // fileByBase64(testDate, async (base64) => {
            //   const { success, data } = await ossUpload(base64, ext);
            //   uni.hideLoading();
            //   if (success) {
            //     let newStr = data;
            //     that.$emit('upSuccsessSingle', newStr, that.nameKey);
            //   } else {
            //     uni.showToast({ icon: 'none', title: data });
            //   }
            // });
          } catch (error) {
            uni.hideLoading()
            uni.showToast({icon: 'none', title:error.message || '上传失败' })
          }
        },
      });
    },
    // 生成日期：20230508；做文件名用
    getDayStr() {
      var date = new Date();
      var year = date.getFullYear();
      var month = date.getMonth() + 1;
      var day = date.getDate();
      if (month < 10) {
        month = '0' + '' + month;
      }
      if (day < 10) {
        day = '0' + '' + day;
      }
      var str = year + '' + '' + month + '' + day;
      return str;
    },
    async compressionImage(file) {
      return new Promise((resolve, reject) => {
        // eslint-disable-next-line no-new
        new ImageCompressor({
          file: file,
          quality: 0.4,
          convertSize: 100000, // 1MB 的都要压缩
          maxWidth: 1500,
          maxHeight: 1500,
          redressOrientation: false,

          // 压缩前回调
          beforeCompress: function (result) {
            // console.log('压缩之前图片尺寸大小: ', result.size)
            // console.log('mime 类型: ', result.type)
          },
          success: function (result) {
            // console.log('mime 类型: ', result.type)
            // console.log('实际压缩率： ', ((file.size - result.size) / file.size * 100).toFixed(2) + '%')
            resolve(result);
          },
          error(e) {
            reject(e);
          },
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
$width: 144rpx;
$height: 144rpx;

.uploadList_box {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;

  .picUpload_wrap,
  .picUpload_wrapSmall {
    position: relative;

    width: $width;
    height: $height;
    line-height: $height;
    background: #f7f7f7;
    border-radius: 24rpx;
    overflow: hidden;

    text-align: center;
    font-size: 28rpx;
    color: #969696;
  }
  .picUpload_wrap {
    border: 2rpx dashed #969696;

    &:before,
    &:after {
      content: ' ';
      position: absolute;
      top: 50%;
      left: 50%;
      -webkit-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);
      background-color: #969696;
    }

    &:before {
      width: 4rpx;
      height: 52rpx;
    }

    &:after {
      height: 4rpx;
      width: 52rpx;
    }
  }
  .picUpload_wrapSmall {
    border: 2rpx solid #969696;
    margin-right: 20rpx;

    .delet_item {
      position: absolute;
      width: 60rpx;
      height: 60rpx;
      background: rgba(0, 0, 0, 0.5);
      z-index: 10;
      right: 0px;
      top: 0px;
      color: #fff;
      text-align: center;
      line-height: 52rpx;
      font-size: 44rpx;
      cursor: pointer;
    }

    .picUpload_pic {
      position: absolute;
      z-index: 1;
      left: 0px;
      right: 0px;
      top: 0px;
      bottom: 0px;
      width: 100%;
      height: 100%;
    }
  }
}
.upLoadSingleShCoverBox {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  left: 0;
  top: 0;
  font-size: 24rpx;
  color: #fff;
  z-index: 99;

  .text {
    z-index: 100; 
    position: relative;
  }

  .animationBox {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    animation: slideDown 1.5s infinite alternate;
    z-index: 9; 
    pointer-events: none; 
    background:linear-gradient(180deg, #f6a04699, #000);
  }

  @keyframes slideDown {
    0% {
      transform: translateY(0%);
    }
    100% {
      transform: translateY(100%);
    }
  }
}
</style>