<template>
  <uni-popup ref="popup" style="z-index: 200" type="bottom" @change="change">
    <view class="pay_wrap">
      <IconFont :size="20" icon="close" class="close-icon" @click="hidePopup" />
      <view class="pay-head"> {{ title }} </view>

      <view
        v-for="item in list"
        :key="item.value"
        :class="payMethod === item.value ? 'active' : ''"
        class="spaceBetween pay_item"
        @click="handelCheck(item)"
      >
        <view class="spaceStart">
          <view class="pay_pic">
            <IconFont :size="20" :icon="item.logo" />
          </view>

          <view>{{ item.name }}</view>
        </view>
        <IconFont v-if="payMethod === item.value" :size="14" icon="check" />
      </view>

      <view class="spaceBetween" style="margin-top: 72rpx">
        <view v-if="priceNum" class="price-text">￥{{ priceNum }}</view>
        <view
          class="kk-btn orange"
          style="margin-left: 200rpx; width: 180rpx"
          @click="payNowFun"
          >立即支付</view
        >
      </view>
    </view>
  </uni-popup>
</template>

<script>
const IS_OPEN_ZFB_PAY = false; // 是否开放支付宝支付
import { payConfig } from '@/config/api/confirmOrder.js';
export default {
  props: {
    title: {
      type: String,
      default: '选择支付方式',
    },
    price: {
      type: Number,
      default: null,
    },
    orderDetail: {
      type: Object,
      default: () => {},
    },
    type: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      list: [],
      payMethod: 1, // 1 微信支付 2 支付宝支付
      // IS_OPEN_ZFB_PAY ? 2 :
    };
  },
  computed: {
    priceNum() {
      if (this.price) {
        return this.price;
      }
      const { type, payAmount } = this.orderDetail || {};
      if (type != 1) {
        return payAmount;
      }
      return 200000;
    },
  },
  mounted() {
    payConfig().then((res) => {
      let payList = JSON.parse(res.data);
      // this.payMethod = 1;
      this.payMethod = payList[0] === 'alipay' ? 2 : 1;
      let newList = [];
      if (payList.indexOf('alipay') !== -1) {
        newList.push({
          value: 2,
          name: '支付宝支付',
          logo: 'zhifubao',
        });
      }
      if (payList.indexOf('wxpay') !== -1) {
        newList.push({
          value: 1,
          name: '微信支付',
          logo: 'weixin',
        });
      }
      this.list = newList;
      this.showPopup();
    });
  },
  onHide() {
    this.hidePopup();
  },
  methods: {
    handelCheck({ value }) {
      this.payMethod = value;
    },
    change(e) {
      if (!e.show) {
        this.$emit('close');
      }
    },
    payNowFun() {
      this.$emit(
        'payNowFun',
        this.payMethod,
        this.orderDetail && this.orderDetail.id
      );
    },
    showPopup() {
      this.$refs.popup.open('bottom');
    },
    hidePopup() {
      this.$refs.popup.close();
    },
  },
};
</script>
<style lang="scss" scoped>
.pay_wrap {
  position: relative;
  padding: 28rpx 50rpx;
  background: #fff;

  color: #000;
  font-weight: 400;
  font-size: 24rpx;

  .pay-head {
    font-size: 32rpx;
    margin-bottom: 60rpx;
    margin-top: 56rpx;
  }

  .pay_item {
    padding: 8rpx 14px;
    flex-shrink: 0;
    margin-bottom: 20rpx;

    &.active {
      background: #fdf5ed;
    }

    .pay_pic {
      width: 60rpx;
      height: 60rpx;

      border-radius: 50%;
      background: #ff720c;
      color: #fff;

      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .price-text {
    text-align: left;
    color: #ff720c;
    font-family: Inter;
    font-size: 36rpx;
    font-weight: 600;
  }
}
.close-icon {
  position: absolute;
  right: 22rpx;
  top: 26rpx;
}
</style>