<template>
  <view>
    <view v-for="(item, index) in opetionDate" :class="[getClass(item)]" :key="index">
      <view v-if="item.type == 1" class="uni-form-item uni-column">
        <view class="title_tedian spaceStart">
          <view v-if="item.is_required" class="required_red">*</view>{{ item.name }}
          <IconFont
            icon="remind"
            class="c-primary"
            style="margin-top: -4rpx; margin-left: 6rpx"
            v-if="isCustom(item) && isHttpCustom(item)"
            @click="showtnrPlaceholder(getCustom(item))"
          />
          <view v-if="item.name === '天霓染'">
            <IconFont icon="remind" class="c-primary" style="margin-top: -4rpx; margin-left: 6rpx" @click="showtnr" />
          </view>
        </view>
        <input @input="inputValue($event, item, item.input_regex)" v-if="item.name == '游戏账号'" v-model="item.iptVal" :placeholder="item.placeholder || '请输入'"
          class="uni-input submit_ipt_tedian" :maxlength="item.input_maxsize ? item.input_maxsize : undefined"
          type="text" />
        <input @input="inputValue($event, item, item.input_regex)" v-else-if="item.name == '游戏密码' || item.name == '确认密码'" v-model="item.iptVal"
          :placeholder="item.placeholder || '请输入'" class="uni-input submit_ipt_tedian" />
        <input v-else v-model="item.iptVal" :placeholder="item.placeholder ||
          (item.name != '已使用天赏石' && item.name != '未使用天赏石'
            ? '请输入'
            : '不填默认为0,填写请以实际为准。')
          " class="uni-input submit_ipt_tedian" step="1" :maxlength="item.input_maxsize ? item.input_maxsize : 10"
          @input="inputValue($event, item, item.input_regex)" />
        <!-- @input="inputValue($event,item,item.input_regex)" -->
        <text style="font-size: 20rpx; margin-left: 40rpx" class="required_red" v-if="item.input_regex_flag">
          {{ item.input_regexName }}
        </text>
        <!-- type="number" -->
      </view>

      <view v-if="item.type == 2" class="uni-form-item uni-column">
        <view class="title_tedian spaceStart"> {{ item.name }}
          <text v-if="isCustom(item) && !isHttpCustom(item)">
            <uni-tooltip :content="getCustom(item)" placement="center">
              <template v-slot:content>
                <view class="uni-stat-tooltip">
                  {{ getCustom(item) }}
                </view>
              </template>
              <IconFont icon="remind" class="c-primary" style="margin-top: -4rpx; margin-left: 6rpx" />
            </uni-tooltip>
          </text>
          <IconFont icon="remind" class="c-primary" style="margin-top: -4rpx; margin-left: 6rpx"
            v-if="isCustom(item) && isHttpCustom(item)" @click="showtnrPlaceholder(getCustom(item))" />

        </view>
        <view class="uni-input submit_ipt_tedian muti" @click="showPopUp(item)">
          <view class="spaceBetween">
            <view v-if="item.choosedList.length > 0" class="spaceStart" style="flex-wrap: wrap">
              <view v-for="(v, i) in item.choosedList" :key="i" class="tedian_item active spaceCenter">
                {{ fakeName(v.name) }}
                <image v-if="iconFilter(v)" :src="iconFilter(v)" class="tag_tedian_pic" mode=""></image>
              </view>
            </view>
            <view v-else style="line-height: 40rpx" class="placeholder-txt">请选择</view>

            <view style="color: #1b1b1b; flex-shrink: 0">
              <IconFont :size="12" icon="arrow-down" />
            </view>
          </view>
        </view>
      </view>

      <view v-if="item.type == 3" class="uni-form-item uni-column">
        <view class="title_tedian spaceStart">
          <view v-if="item.is_required" class="required_red">*</view>{{ item.name }}
          <text v-if="isCustom(item) && !isHttpCustom(item)">
            <uni-tooltip :content="getCustom(item)" placement="center">
              <template v-slot:content>
                <view class="uni-stat-tooltip">
                  {{ getCustom(item) }}
                </view>
              </template>
              <IconFont icon="remind" class="c-primary" style="margin-top: -4rpx; margin-left: 6rpx" />
            </uni-tooltip>
          </text>
          <IconFont icon="remind" class="c-primary" style="margin-top: -4rpx; margin-left: 6rpx"
            v-if="isCustom(item) && isHttpCustom(item)" @click="showtnrPlaceholder(getCustom(item))" />
        </view>
        <!-- <view class="spaceBetween">
          <view
            v-for="(ele, idx) in item.inputList"
            :key="idx"
            :class="ele === item.value ? 'active-select' : ''"
            class="select-item"
            @click="changeSelectItem(item, ele)"
          >
            {{ ele }}
          </view>
        </view> -->
        <CheckBox :value="item.value" :data-list="item.inputList" @click="(v) => changeSelectItem(item, v)"></CheckBox>
      </view>

      <!-- 级联 -->
      <view v-if="item.type == 4" class="uni-form-item uni-column">
        <view class="title_tedian spaceStart">
          <view v-if="item.is_required" class="required_red">*</view>{{ item.name }}
        </view>
        <view>
          <picker :value="item.indexPro" :range="item.professionDate" @change="
            (e) => {
              bindPickerChangePro(e, item, index);
            }
          ">
            <view class="uni-input submit_ipt spaceBetween">
              <text v-if="item.value">{{ item.value }}</text>
              <text v-else class="placeholder-txt">{{
                `请选择${item.name || ''}`
                }}</text>
              <IconFont :size="12" icon="arrow-down" style="color: #1b1b1b" />
            </view>
          </picker>
        </view>
      </view>
    </view>

    <!-- 弹层出现 -->
    <uni-popup ref="popup" type="bottom" style="z-index: 999">
      <view class="agreeWay_wrap">
        <view class="pay-popup-head spaceBetween">
          <text>{{ popupDateList.name }}</text>
          <view class="closeAgreement" @click="closeAgree">完成</view>
        </view>
        <view class="scroll_agreeBody">
          <!-- 展示出已选择的数据 -->
          <view v-if="
            popupDateList.choosedList && popupDateList.choosedList.length > 0
          " class="tedian_box" style="margin-bottom: 30rpx; flex-wrap: wrap">
            <view class="spaceStart" style="flex-wrap: wrap">
              <view v-for="(v, i) in popupDateList.choosedList" :key="i" class="tedian_item spaceCenter active"
                @click="chooseChoosed(v)">
                {{ fakeName(v.name) }}
                <image v-if="iconFilter(v)" :src="iconFilter(v)" class="tag_tedian_pic" mode=""></image>
              </view>
            </view>
          </view>

          <view class="search-box">
            <view style="position: relative">
              <MySearchBar v-model="popupDateList.iptSearchVal" placeholder="请输入搜索内容或者新游戏物品" icon="show"
                @change="tedianChange" @search="addZidingyi" />
            </view>

            <!-- 搜索结果-数组 -->
            <view v-if="
              popupDateList.iptSearchVal &&
              popupDateList.searchList &&
              popupDateList.searchList.length > 0
            " class="spaceStart searcList_wrap" style="margin-top: 40rpx">
              <view v-for="(v, i) in popupDateList.searchList" :class="v.checked ? 'active' : ''" :key="i"
                class="tedian_item big spaceCenter" @click="chooseItemTedian(v)">
                {{ fakeName(v.name) }}
                <image v-if="iconFilter(v)" :src="iconFilter(v)" class="tag_tedian_pic" mode=""></image>
              </view>
            </view>
            <view v-else-if="popupDateList.iptSearchVal" class="empty-text">暂时没有搜索到您要的数据</view>
            <!-- 搜索无 -->
            <!-- 展示选项-数组 -->
            <view v-if="!popupDateList.iptSearchVal && popupDateList.childList" class="spaceStart"
              style="flex-wrap: wrap; margin-top: 40rpx">
              <view v-for="(v, i) in popupDateList.childList" :class="v.checked ? 'active' : ''" :key="i"
                class="tedian_item big spaceCenter" @click="chooseItemTedian(v)">
                {{ fakeName(v.name) }}
                <image v-if="iconFilter(v)" :src="iconFilter(v)" class="tag_tedian_pic" mode=""></image>
              </view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import uniPopup from '@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue';

export default {
  components: {
    uniPopup,
  },
  props: {
    flagId: {
      type: String,
      default: '',
    },
    // 编辑带过来的详情数据
    detailOptions: {
      type: Array,
      default() {
        return [];
      },
    },
    opetionDate: {
      type: Array,
      default() {
        return [];
      },
    },
    pushType: {
      type: Number,
      default: 2,
    },
  },
  data() {
    return {
      popupDateList: {}, // 弹出层展示的数据
    };
  },
  watch: {
    flagId(newVal, oldVal) {
      this.initOpetionList();
    },
    detailOptions(newVal, oldVal) {
      this.initOpetionList();
    },
  },
  created() {
    if (this.flagId) {
      this.initOpetionList();
    }
  },
  methods: {
    isHttpCustom(item) {
      if (item && item.custom) {
        let custom = JSON.parse(item.custom)
        if (custom.placeholder && custom.placeholder.includes('http')) {
          return true
        } else {
          return false
        }
      }
    },
    isCustom(item) {
      if (item && item.custom) {
        let custom = JSON.parse(item.custom)
        if (custom.placeholder) {
          return true
        }
        return false
      }
      return false
    },
    getCustom(item) {
      if (item && item.custom) {
        let custom = JSON.parse(item.custom)
        if (custom.placeholder) {
          return custom.placeholder
        }
      }
    },
    inputValue(e, item, input_regex) {
      if (input_regex) {
        setTimeout(() => {
          const matchedItemIndex = this.opetionDate.findIndex(
            (opetion) => opetion.name === item.name,
          );
          if (matchedItemIndex !== -1) {
            const inputValue = e.target.value; // 获取输入框的值
            const regex = new RegExp(`^${input_regex}$`); // 创建正则表达式
            if (!regex.test(inputValue)) {
              this.opetionDate[matchedItemIndex].input_regex_flag = true; // 如果不符合正则，清空输入框的值
            } else {
              this.opetionDate[matchedItemIndex].input_regex_flag = false; // 更新输入框的值
            }
            this.$set(this.opetionDate, matchedItemIndex, {
              ...this.opetionDate[matchedItemIndex],
            }); // 确保视图更新
          }
        });
      }
    },
    getClass(item) {
      if (this.pushType == 1) {
        let { custom } = item;
        custom = JSON.parse(custom);
        if (custom.pushType1 == 'show') {
          return 'show';
        } else {
          return 'hide';
        }
      } else {
        return 'show';
      }
    },
    showtnr() {
      uni.previewImage({
        current: 0,
        urls: ['https://images2.kkzhw.com/mall/images/20240618/tianniran.webp'],
      });
    },
    showtnrPlaceholder(item) {
      uni.previewImage({
        current: 0,
        urls: [item],
      });
    },
    bindPickerChangePro(e, item, index) {
      item.indexPro = e.detail.value;
      item.professionDate.forEach((ele, indexS) => {
        if (item.indexPro == indexS) {
          item.value = ele;
        }
      });
      if (item.saveInputList) {
        // 如果有是级联 第一级
        const findIt = item.saveInputList.find((ele) => {
          return ele.parent_name === item.value;
        });
        this.opetionDate[index + 1].professionDate = findIt.childList;
        this.opetionDate[index + 1].value = '';
      }
    },
    changeSelectItem(item, ele) {
      this.$set(item, 'value', ele);
      // this.item.value = ele
    },
    // 添加自定义
    addZidingyi() {
      // !this.popupDateList.searchList.length &&
      if (!this.popupDateList.iptSearchVal) {
        uni.showToast({
          title: '请输入搜索内容或者新游戏物品',
          icon: 'none',
        });
        return;
      }
      var isTianjia = true;
      this.popupDateList.choosedList.forEach((v, i) => {
        if (v.name == this.popupDateList.iptSearchVal) {
          uni.showToast({
            title: '您已添加此数据，无需重复添加',
            icon: 'none',
          });
          isTianjia = false;
        }
      });
      if (isTianjia) {
        var json = {
          name: this.popupDateList.iptSearchVal,
        };
        this.popupDateList.choosedList.push(json);
        this.popupDateList.iptSearchVal = '';
        // this.$set(this.popupDateList,'iptSearchVal','')
        this.popupDateList.searchList = [];
      }
    },
    fakeName(name) {
      return name.replace(/\[[^\]]*\]/, '');
    },
    iconFilter(v) {
      if (v.name.indexOf('[绝]') !== -1) {
        return '../../static/old/jue.png';
      } else if (v.name.indexOf('[钱]') !== -1) {
        return '../../static/old/price.png';
      } else if (v.name.indexOf('[核]') !== -1) {
        return '../../static/old/he.png';
      }
    },
    initOpetionList() {
      if (this.detailOptions && this.detailOptions.length) {
        // 编辑回填
        this.detailOptions.forEach((item, index) => {
          this.opetionDate.forEach((v, i) => {
            if (v.type == 1 && item.title == v.name) {
              v.iptVal = item.value;
            }
            if (v.type == 2 && item.title == v.name) {
              if (item.value) {
                v.choosedList = item.value.split(',').map((ele) => {
                  return { name: ele };
                });
              } else {
                v.choosedList = [];
              }
            }
            if (v.type == 3 && item.title == v.name) {
              v.value = item.value;
            }
            if (v.type == 4 && item.title == v.name) {
              const value = v.professionDate.findIndex((it) => {
                return it === item.value;
              });
              v.indexPro = value;
              v.value = v.professionDate[v.indexPro];
            }
          });
        });
        // 给下拉展示的数据加上选中
        this.opetionDate.forEach((v, i) => {
          if (v.type == 2 && v.choosedList.length > 0) {
            v.choosedList.forEach((c, indexC) => {
              v.childList.forEach((child, indexChild) => {
                if (c.name == child.name) {
                  child.checked = true;
                }
              });
            });
          }
        });
      }
    },
    // 处理已经添加的数据
    chooseChoosed(date) {
      this.popupDateList.choosedList.forEach((v, i) => {
        if (v.name == date.name) {
          this.popupDateList.choosedList.splice(i, 1);
        }
      });
      this.popupDateList.childList.forEach((v, i) => {
        if (v.name == date.name) {
          v.checked = false;
        }
      });
    },
    // 选择-选中的再次点击就是取消
    chooseItemTedian(date) {
      if (!date.checked) {
        date.checked = true;
        this.popupDateList.choosedList.push(date);
      } else {
        date.checked = false;
        this.popupDateList.choosedList.forEach((v, i) => {
          if (v.name == date.name) {
            this.popupDateList.choosedList.splice(i, 1);
          }
        });
      }
      this.changeOpetions();
    },
    // 下拉框里面搜索-有数据在搜索没有就不搜索
    tedianChange() {
      let str = this.popupDateList.iptSearchVal;
      if (str) {
        let reg = new RegExp(str);
        this.popupDateList.searchList = this.popupDateList.childList.filter(
          (item) => reg.test(item.name),
        );
      } else {
        this.popupDateList.searchList = [];
      }
      // this.changeOpetions();
    },
    closeAgree() {
      this.popupDateList.iptSearchVal = '';
      this.$refs.popup.close('bottom');
    },
    showPopUp(date) {
      this.popupDateList = date;
      this.$refs.popup.open('bottom');
    },
    // 传递数据改变
    changeOpetions() {
      this.$emit('getopetion', this.opetionDate);
    },
  },
};
</script>

<style scoped>
.uni-form-item {
  margin-bottom: 16px;
}

.title_tedian {
  color: #1b1b1b;
  font-size: 28rpx;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.56px;

  padding-bottom: 16rpx;
}

.tnr {
  width: 20px;
  height: 20px;
  margin: 4px 0 0 4px;
  object-fit: cover;
}

.submit_ipt,
.select-item,
.submit_ipt_tedian {
  background-color: #fff;
  height: 70rpx;
  line-height: 70rpx;
  box-sizing: border-box;
  padding: 0 48rpx;
  border-radius: 40rpx;
  border: 2rpx solid rgba(0, 0, 0, 0.1);

  color: #1b1b1b;
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.48px;
}

.select-item {
  display: flex;
  align-items: center;
  justify-content: center;

  line-height: 30rpx;
  padding: 0 10rpx;
}

.placeholder-txt,
.select-item {
  color: rgba(0, 0, 0, 0.4);
}

.submit_ipt_tedian.muti {
  padding: 16rpx 48rpx;
  min-height: 70rpx;
  height: auto;
  line-height: normal;
}

.select-item {
  flex: 1;
  margin-right: 20rpx;
  text-align: center;
}

.active-select,
.tedian_item.active {
  border: 1px solid #ff7a00;
  background: #ff720c;
  color: #fff;
}

.tedian_box {
  position: relative;
  min-height: 94rpx;
  width: 100%;
  box-sizing: border-box;

  padding: 24rpx;
  border-radius: 40rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  background: var(--White, #fff);
}

.jiantou_arr {
  position: absolute;
  right: 30rpx;
  top: 20rpx;
  width: 50rpx;
  height: 50rpx;
  z-index: 1;
}

.tedian_item {
  padding: 16rpx 44rpx;
  border-radius: 6rpx;
  word-break: keep-all;
  margin-right: 10rpx;
  margin-bottom: 10rpx;

  border-radius: 40rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  background: #fff;

  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.48px;
}

.big {
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.agreeWay_wrap {
  z-index: 999999;
  width: 100%;
  height: 80vh;
  box-sizing: border-box;
  background-color: #fff;
}

.closeAgreement {
  color: #ff720c;
  font-family: 'PingFang SC';
  font-size: 32rpx;
  font-style: normal;
  font-weight: 400;
  line-height: normal;

  width: 80rpx;
  text-align: right;
}

.scroll_agreeBody {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 40rpx;
  top: 148rpx;
  overflow-y: scroll;
  box-sizing: border-box;
  padding: 0rpx 30rpx 0;
}

.scroll_agreeBody::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}

.pay-popup-head {
  color: #1b1b1b;
  font-family: YouSheBiaoTiHei;
  font-size: 36rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  /* 111.111% */
  letter-spacing: 0.36px;

  padding: 30rpx 40rpx;
  box-sizing: border-box;

  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  margin-bottom: 40rpx;
}

.searcList_wrap {
  flex-wrap: wrap;
  /* padding-top: 30rpx; */
}

.searchNoDate {
  padding-top: 30rpx;
  color: #666;
  font-size: 26rpx;
}

.tag_tedian_pic {
  width: 30rpx;
  height: 30rpx;
  margin-left: 6rpx;
}

.addN_style_zi {
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  right: 20rpx;
  top: 50%;
  z-index: 10;
  transform: translateY(-50%);
}

.hide {
  display: none;
}

.search-box {
  border-radius: 20px;
  background: #f6f6f6;
  padding: 40rpx 24rpx;
}

.empty-text {
  color: #ff720c;
  font-size: 20rpx;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.2px;
  padding-left: 40rpx;
  margin-top: 10rpx;
}

.uni-stat-tooltip {
  /* margin-left: 00rpx; */
  width: 400rpx;
}
</style>
<style>
.uni-textarea-textarea {
  color: #1b1b1b;
}
</style>
