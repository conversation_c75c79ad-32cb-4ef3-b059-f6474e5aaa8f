<template>
  <view :class="min ? 'min' : ''" class="right-fix-nav">
    <view v-if="!hideChat" class="chat" @click="goNews2">
      <view>
        <image class="chatImg" src="../../assets/imgs/menu_msg.svg" mode="scaleToFill" />
      </view>
      <view>消息</view>
      <view v-if="storeUnReadCountTx > 0" class="unread">{{
        Math.min(storeUnReadCountTx,99)
      }}</view>
    </view>
    <view class="home" @click="goHome">
      <view>
        <image class="chatImg" src="../../assets/imgs/menu_home.svg" mode="scaleToFill" />
      </view>
      <view>返回首页</view>
    </view>
    <view class="home" @click="goTop">
      <view>
        <image class="chatImg" src="../../assets/imgs/menu_top.svg" mode="scaleToFill" />
      </view>
      <view>返回顶部</view>
    </view>
    <view class="spaceCenter arrow">
      <view @click="hideBtn">
        <IconFont icon="arrow-left" class="chatImg2"/>
        <view>{{ hideTxt }}</view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex';
import isLogin from '@/common/mixins/isLogin';
export default {
  data(){
    return {
      min: false,
      hideChat: false,
    };
  },
  computed: {
    ...mapGetters(['storeUnReadCountTx']),
    hideTxt() {
      return this.min ? '' : '收起';
    },
  },
  mounted() {
    if (!isLogin()) {
      this.hideChat = true;
    } else {
      this.hideChat = false;
    }
  },
  methods: {
    goHome() {
      uni.switchTab({
        url: '/pages/tabBar/home/<USER>',
      });
    },
    goNews2() {
      uni.navigateTo({
        url: '/pages/news2/news2',
      });
    },
    hideBtn() {
      this.min = !this.min;
    },
    goTop() {
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 300,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.right-fix-nav {
  color: rgba(0, 0, 0, 0.40);
  position: fixed;
  bottom: 400rpx;
  right: 0;
  background: #fff;
  border-radius: 32rpx;
  padding: 12rpx 10rpx;
  z-index: 99;
  font-size: 20rpx;
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);

  .arrow {
    // padding-bottom: 20rpx;
  }
  .chat {
    text-align: center;
    margin-bottom: 40rpx;
    .unread {
      position: absolute;
      top: 10rpx;
      right: 6rpx;
      width: 40rpx;
      height: 40rpx;
      text-align: center;
      border-radius: 40rpx;
      line-height: 40rpx;
      color: #fff;
      font-family: Inter;
      font-size: 10px;
      font-style: normal;
      font-weight: 700;
      background: linear-gradient(87deg, #FF002E 3.31%, #FFC0C0 142.11%);
      box-shadow: 0px 0px 5px 0px rgba(255, 255, 255, 0.6) inset;
    }
  }
  .home {
    text-align: center;
    margin-bottom: 30rpx;
  }
}
.chatImg {
  width: 60rpx;
  height: 60rpx;
}
.chatImg2 {
  width: 20rpx;
  height: 20rpx;
  margin-left: 10rpx;
}
.min {
  .chatImg2 {
    width: 20rpx;
    height: 20rpx;
    transform: rotate(180deg);
    margin-left: 0rpx;
    padding:10rpx
  }
  .chat {
    display: none;
  }
  .home {
    display: none;
  }
}
</style>
