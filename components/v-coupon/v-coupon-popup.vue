<template>
    <uni-popup ref="popupRef" type="bottom">
      <view class="v-coupon-popup-content">
        <view class="title">代金券</view>
        <img
          class="close-icon"
          src="@/assets/imgs/coupon/close-icon.png"
          alt=""
          srcset=""
          @click="close"
        />
        <view class="tabs">
          <view
            v-for="(item, index) in COUPON_STATUS"
            :key="index"
            :class="{ 'tabs-item-actived': item.value === status }"
            class="tabs-item"
            @click="changeStatus(item)"
            >{{ item.label }}</view
          >
        </view>
        <view class="scroll">
          <VCouponItem
            v-for="item in displayCouponList"
            :key="item.id"
            :item="item"
            :select-item="selectItem"
            type="check"
            @check="checkItme"
            :status="status"
          ></VCouponItem>
          <!-- <view class="info"> - 到底啦 - </view> -->
        </view>
        <view class="submitBoxView">
            <view class="clearSelect" @click="clearSelect"> 确定 </view>
        </view>
      </view>
    </uni-popup>
  </template>
  <script>
//   import ScrollDownRefresh from '@/components/scrollDownRefresh/scrollDownRefresh.vue';
  import VCouponItem from '@/pages/coupon/modules/coupon-item.vue';
  export default {
    name: 'VCouponPopup',
    components: {
    //   ScrollDownRefresh,
      VCouponItem,
    },
    props: {
      canUseCouponList: {
        type: Array,
        default: () => [],
      },
      notCanUseCouponList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        COUPON_STATUS: [
          {
            value: 1,
            label: '可用',
          },
          {
            value: 0,
            label: '不可用',
          },
        ],
        status: 1,
        selectItem: {},
      };
    },
    computed: {
      displayCouponList() {
        if (this.status ===1) {
          return this.canUseCouponList;
        } else {
          return this.notCanUseCouponList;
        }
      },
    },
  
    methods: {
      open() {
        this.status = 1;
        this.$nextTick(() => {
          this.$refs.popupRef.open();
        });
      },
      close() {
        this.$refs.popupRef.close();
      },
      changeStatus(item) {
        this.status = item.value;
      },
      checkItme(item) {
        if(this.status==0){
            return
        }
        if(this.selectItem&&this.selectItem.id==item.id){
            this.selectItem = {};
        }else{
            this.selectItem = item;
        }
        // this.$emit('check', item);
        // this.close();
      },
      clearSelect() {
        // this.selectItem = {};
        this.$emit('check', this.selectItem);
        this.close();
      },
    },
    exports: [open, close],
  };
  </script>
  <style lang="scss" scoped>
  .v-coupon-popup-content {
    height: 80vh;
    background: #fff;
    border-radius: 32rpx 32rpx 0 0;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    padding-bottom: 36rpx;
    .title {
      height: 120rpx;
      font-size: 34rpx;
      color: #1f1f1f;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
      border-bottom: 1px solid #f4f4f4;
    }
    .close-icon {
      display: block;
      width: 32rpx;
      height: 32rpx;
      position: absolute;
      top: 44rpx;
      right: 30rpx;
    }
    .tabs {
      height: 100rpx;
      display: flex;
      align-items: center;
      justify-content: space-around;
      border-top: 1px solid #f4f4f4;
      .tabs-item {
        font-size: 28rpx;
        color: #9a9a9a;
        position: relative;
      }
      .tabs-item-actived {
        font-size: 32rpx;
        color: #1f1f1f;
        font-weight: 600;
        &::after {
          content: '';
          position: absolute;
          bottom: -10rpx;
          left: 50%;
          transform: translateX(-50%);
          width: 36rpx;
          height: 6rpx;
          background: #ff720c;
          border-radius: 10rpx;
        }
      }
    }
    .scroll {
      padding: 20rpx 24rpx 36rpx;
      height:100%;
      overflow:auto;
      .info {
        height: 136rpx;
        line-height: 136rpx;
        font-size: 24rpx;
        color: #d7d6db;
        text-align: center;
      }
    }
    .submitBoxView{
        height:120rpx;
        background:#fff;
        padding-top:10px;
        .clearSelect {
      width: calc(100% - 48rpx);
      height: 102rpx;
      margin: 0 auto;
      border-radius: 16rpx;
      background: #ff720c;
      font-size: 34rpx;
      color: #1f1f1f;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    }
    
  }
  </style>
  