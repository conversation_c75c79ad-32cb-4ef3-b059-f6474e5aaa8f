<template>
    <view class="v-coupon" @click="openCouponPopup">
      <view class="label">代金券</view>
      <view class="value">
        <view v-if="!selectItem.id" class="text">
          <text v-if="canUseCouponList.length"
            >{{ canUseCouponList.length }}张可用</text
          >
          <text v-else>暂无可用代金券</text>
        </view>
        <view v-else class="text">减￥{{ selectItem.amount }}</view>
        <img class="icon" src="@/assets/imgs/rt.png" alt="" srcset="" />
      </view>
      <VCouponPopup
        ref="popupRef"
        :can-use-coupon-list="canUseCouponList"
        :not-can-use-coupon-list="notCanUseCouponList"
        @check="check"
      ></VCouponPopup>
    </view>
  </template>
  <script>
  import {
    getCouponCanUseByProduct,
    getCouponCantUseByProduct,
  } from '@/config/api/coupon.js';
  import VCouponPopup from './v-coupon-popup';
  export default {
    name: 'VCoupon',
    components: {
      VCouponPopup,
    },
    props: {
      productId: {
        type: String | undefined,
        default: '',
      },
    },
    data() {
      return {
        canUseCouponList: [],
        notCanUseCouponList: [],
        selectItem: {},
      };
    },
    async mounted() {
      if (!this.productId) return;
      await this.getCouponCanUseByProduct();
      await this.getCouponCantUseByProduct();
    },
    methods: {
      async getCouponCanUseByProduct() {
        await getCouponCanUseByProduct(this.productId).then((res) => {
          if (res.code === 200) {
            this.canUseCouponList = res.data || [];
          }
        });
      },
      async getCouponCantUseByProduct() {
        await getCouponCantUseByProduct(this.productId).then((res) => {
          if (res.code === 200) {
            this.notCanUseCouponList = res.data || [];
          }
        });
      },
      check(item) {
        this.selectItem = item;
        this.$emit('check', item);
      },
      openCouponPopup() {
        // if (!this.canUseCouponList.length) return;
        this.$refs.popupRef.open();
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  .v-coupon {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .label {
      font-size: 30rpx;
      color: #1f1f1f;
      font-weight: 600;
    }
    .value {
      display: flex;
      align-items: center;
      .text {
        font-size: 26rpx;
        color: #ff5500;
      }
      .icon {
        display: block;
        width: 20rpx;
        height: 28rpx;
        margin-left: 12rpx;
      }
    }
  }
  </style>
  