import request from '../../common/request.js';
import localConfig from '../config.js';
const webUrl = localConfig.HTTP_REQUEST_URL_KK;

export function getHomeContentAll(oprtForm='') {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/home/<USER>',{}, {
      oprtForm: oprtForm,
    }).then((res) => {
      reslove(res);
    });
  });
}

export function getNewProductList(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/home/<USER>', params).then((res) => {
      reslove(res);
    });
  });
}

export function getRecommendProductList(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/home/<USER>', params).then((res) => {
      reslove(res);
    });
  });
}

export function getHotProductList(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/home/<USER>', params).then((res) => {
      reslove(res);
    });
  });
}

export function getGameBrandList(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/home/<USER>', params).then((res) => {
      reslove(res);
    });
  });
}

export function getGameList(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/home/<USER>', params).then((res) => {
      reslove(res);
    });
  });
}

export function getSubjectProductList(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/home/<USER>', params).then((res) => {
      reslove(res);
    });
  });
}

export function getGuess(params) {
  return new Promise((reslove, reject) => {
    request
      .get(
        webUrl.replace('mall-portal', 'mall-search') + '/kkSearch/recommend',
        params,
      )
      .then((res) => {
        reslove(res);
      });
  });
}

export function getZbList(data) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/home/<USER>/list', data).then((res) => {
      reslove(res);
    });
  });
}

export function topsListApi(id) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/home/<USER>' + id).then((res) => {
      reslove(res);
    });
  });
}

export function newProduct(id) {
  return new Promise((reslove, reject) => {
    request
      .get(
        webUrl.replace('mall-portal', 'mall-search') +
          '/kkSearch/newProduct?categoryId=' +
          id,
      )
      .then((res) => {
        reslove(res);
      });
  });
}

export function detectProduct() {
  return new Promise((reslove, reject) => {
    request
      .get(
        webUrl.replace('mall-portal', 'mall-search') +
          '/kkSearch/detectProduct',
      )
      .then((res) => {
        reslove(res);
      });
  });
}

export function topProduct() {
  return new Promise((reslove, reject) => {
    request
      .get(
        webUrl.replace('mall-portal', 'mall-search') + '/kkSearch/topProduct',
      )
      .then((res) => {
        reslove(res);
      });
  });
}

export function getShopDetail(id) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + `/shop/detail/${id}`).then((res) => {
      reslove(res);
    });
  });
}

export function searchProductListBmc(id,params, data = {}) {
  return new Promise((reslove, reject) => {
    request
      .post(
        webUrl.replace('mall-portal', 'mall-search') + `/bmc-search/list/${id}`,
        data,
        { ext: params },
      )
      .then((res) => {
        reslove(res);
      });
  });
}