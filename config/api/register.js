import request from '../../common/request.js';
import localConfig from '../config.js';
const webUrl = localConfig.HTTP_REQUEST_URL_KK;

export function registerApi(params, data = {}) {
  return new Promise((reslove, reject) => {
    request
      .post(webUrl + '/sso/register', data, {
        ext: params,
      })
      .then((res) => {
        reslove(res);
      });
  });
}

export function sendPhoneCode(params) {
  return new Promise((reslove, reject) => {
    request.get(webUrl + '/sso/getAuthCode', params).then((res) => {
      reslove(res);
    });
  });
}


export function sendGetSmsCode(data) {
  return new Promise((reslove, reject) => {
    request.post(webUrl + '/sso/getSmsCode', data).then((res) => {
      reslove(res);
    });
  });
}
