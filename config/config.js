var config = {
  // 请求域名 格式： https://您的域名
  // 测试开发
  // HTTP_REQUEST_URL: 'https://api.fukuaxiaotuandui.com',
  // 正式服部署
  // HTTP_REQUEST_URL_KK: 'https://api2.kkzhw.com/mall-portal',
  HTTP_REQUEST_URL_KK: 'https://api2.kkzhw.com/mall-portal',
  HTTP_H5_URL: 'https://m.kkzhw.com',
  HTTP_PC_URL: 'https://kkzhw.com',
  // 测试环境
  // HTTP_REQUEST_URL_KK: 'https://api.kkzhw.com/mall-portal',
  // HTTP_H5_URL: 'http://192.168.2.86:8201',
  // HTTP_PC_URL: 'http://192.168.2.86:8201',
  // 请求头
  HEADER: {
    'content-type': 'application/json',
  },
  // Socket调试模式
  SERVER_DEBUG: true,
  // 心跳间隔
  PINGINTERVAL: 3000,
  // 回话密钥名称 请勿修改此配置
  TOKENNAME: 'Authori-zation',
  // 端来源
  DEVICE_SOURCE:'H5'
};

module.exports = config;
