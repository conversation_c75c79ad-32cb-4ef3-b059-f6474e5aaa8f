<template>
  <span :style="{ color: color, fontSize: fontSize*2 + 'rpx' }" class="appellation">
    {{ appellation }}
    <span v-if="online" class="online"></span>
  </span>
</template>

<script lang="ts" setup>
import { autorun } from 'mobx';
import { onUnmounted, ref } from '../utils/transformVue';

import { deepClone } from '../utils';

const appellation = ref('');
const { account, teamId, ignoreAlias, nickFromMsg } = defineProps({
  online: {
    type: Boolean,
    default: false,
  },
  account: {
    type: String,
    required: true,
  },
  teamId: {
    type: String,
    default: null,
  },
  ignoreAlias: {
    type: Boolean,
    default: false,
  },
  nickFromMsg: {
    type: String,
    default: null,
  },
  color: {
    type: String,
    default: '#1B1B1B',
  },
  fontSize: {
    type: Number,
    default: 14,
  },
});

const uninstallAppellationWatch = autorun(() => {
  appellation.value = deepClone(
    // @ts-ignore
    uni.$UIKitStore?.uiStore?.getAppellation({
      account,
      teamId,
      ignoreAlias,
      nickFromMsg,
    }),
  );
});
onUnmounted(() => {
  uninstallAppellationWatch();
});
</script>

<style scoped lang="scss">
.online {
  margin-left: 12rpx;

  height: 16rpx;
  width: 16rpx;
  display: inline-block;
  vertical-align:top;
  margin-top: 6rpx;

  border-radius: 48rpx;
  background: linear-gradient(90deg, #28d386 11.95%, #94eaa7 91.25%);
  box-shadow: 0px 0px 10rpx 0px rgba(255, 255, 255, 0.6) inset;
}
.appellation {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
