<template>
  <uni-popup
    ref="popup"
    background-color="#fff"
    style="z-index: 999"
    @change="change"
  >
    <view class="order_box">
      <view class="title_box">
        <view class="order_title">
          选择客服
          <IconFont :size="16" icon="close" class="close_btn" @click="hide" />
        </view>
        <view class="spaceBetween tabs">
          <view
            :class="type === 1 ? 'active' : ''"
            class="typelist_item"
            @click="changeType(1)"
            >售前客服</view
          ><view
            :class="type === 2 ? 'active' : ''"
            class="typelist_item"
            @click="changeType(2)"
            >售后客服</view
          >
        </view>
      </view>
      <scroll-view class="scroll_box g-bg" scroll-y>
        <view style="padding-bottom: 300rpx">
          <view v-if="list.length === 0" class="empty_box">暂时还没有数据</view>
          <view
            v-for="item in list"
            v-else
            :key="item.id"
            class="collect_item spaceBetween"
          >
            <view class="spaceStart">
              <image
                :src="item.pic"
                style="width: 120rpx; height: 120rpx; margin-right: 24rpx"
              ></image>
              <view>{{ item.name }}</view>
            </view>
            <view class="kk-btn line" @click.stop="goChat(item)"
              ><span>咨询客服</span>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </uni-popup>
</template>

<script>
import { changeKfList } from '@/config/api/kf';
export default {
  props: {
    changeKfModal: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      type: 1,
      list: [],
    };
  },
  watch: {
    changeKfModal(val) {
      if (val) {
        this.show();
      } else {
        this.hide();
      }
    },
  },
  methods: {
    goChat(item) {
      this.hide();
      const imcode = item.IM;
      let sessionId = `p2p-${imcode}`;
      if (uni.$UIKitStore.sessionStore.sessions.get(sessionId)) {
        uni.$UIKitStore.uiStore.selectSession(sessionId);
      } else {
        uni.$UIKitStore.sessionStore.insertSessionActive('p2p', imcode);
      }
      uni.redirectTo({
        url: `/pages/NEUIKit/pages/Chat/index?&sessionId=${sessionId}`,
      });
      setTimeout(() => {
        // redirectTo后，设置正确的sessionId
        if (uni.$UIKitStore.sessionStore.sessions.get(sessionId)) {
          uni.$UIKitStore.uiStore.selectSession(sessionId);
        } else {
          uni.$UIKitStore.sessionStore.insertSessionActive('p2p', imcode);
        }
      }, 100);
    },
    show() {
      this.$refs.popup.open('bottom');
      this.getList();
    },
    hide() {
      this.$refs.popup.close();
    },
    change(item) {
      const { show } = item;
      this.$emit('change', show);
    },
    changeType(type) {
      this.type = type;
      this.getList();
    },
    getList() {
      const selectedSession = uni.$UIKitStore.uiStore.selectedSession;
      const tempList = selectedSession.split('-');
      const scene = tempList.shift();
      const to = tempList.join('-');
      let obj = {
        currentKferIM: to,
        type: this.type == 1 ? '咨询客服' : '售后客服',
      };
      changeKfList(obj).then((res) => {
        if (res.code == 200) {
          this.list = res.data;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.empty_box {
  border-radius: 40rpx;
  background: #f9f6f3;
  height: 34px;
  line-height: 34px;

  color: #1b1b1b;
  text-align: center;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  letter-spacing: 0.48px;
}
.order_box {
  height: 660rpx;
  background: #fff;
  overflow: hidden;

  .scroll_box {
    height: 780rpx;
    overflow: auto;

    padding: 40rpx 40rpx 80rpx 40rpx;
    box-sizing: border-box;
  }
  .title_box {
    position: sticky;
    top: 0;
    z-index: 90;
  }
  .order_title {
    background: #fff;
    text-align: center;
    padding: 40rpx 0;

    color: #000;
    font-size: 30rpx;
    font-weight: 500;

    position: relative;

    .close_btn {
      position: absolute;
      right: 22rpx;
      top: 14px;
    }
  }
  .tabs {
    justify-content: space-between;
    padding: 16px 22px;
    background: #fff;
    box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);

    .typelist_item {
      position: relative;
      width: 50%;

      text-align: center;
      color: rgba(0, 0, 0, 0.4);
      font-family: YouSheBiaoTiHei;
      font-size: 36rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      letter-spacing: 0.36px;

      &:first-child::after {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        width: 1rpx;
        height: 8px;
        background: rgba(0, 0, 0, 0.4);
        transform: translateY(-50%);
      }
    }
    .typelist_item.active {
      background: linear-gradient(180deg, #ffb74a 0%, #ff7a00 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .collect_item {
    margin-bottom: 10rpx;

    padding: 10px 16px;
    overflow: hidden;
    border-radius: 12px;
    background: #fff;
    box-shadow: 1px 2px 6px 0px rgba(0, 0, 0, 0.05);
    box-sizing: border-box;

    color: #000;
    font-size: 24rpx;
    font-weight: 500;
  }

}
</style>
