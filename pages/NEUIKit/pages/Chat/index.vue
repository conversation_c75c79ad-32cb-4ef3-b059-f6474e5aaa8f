<template>
  <!-- 处理滚动穿透  此为官方推荐做法 https://uniapp.dcloud.net.cn/component/uniui/uni-popup.html#%E4%BB%8B%E7%BB%8D -->
  <page-meta
    :page-style="'overflow:' + (moveThrough ? 'hidden' : 'visible')"
  ></page-meta>

  <div
    :class="[
      isH5 ? 'msg-page-wrapper-h5 new-body' : 'msg-page-wrapper', // 根据 isH5 切换类
      { 'shake': isShaking }, // 根据 isShaking 添加 shake 类
    ]"
  >
    <div v-if="needCheckFace && needFace" class="faceBtn">
      <div class="facenote">
        <div class="note">交易前请先完成人脸认证，未认证前无法进行交易</div>
        <div class="bar_item active" @click="goFace">前往人脸认证</div>
        <div class="bar_item active" @click="doGetCertDetail2">刷新</div>
      </div>
    </div>
    <NavBar :to="to" :online="online" :title="title" :showLeft="true">
      <template v-slot:left>
        <div class="spaceStart">
          <div @click="backToConversation">
            <!-- <Icon type="icon-zuojiantou" :size="22"></Icon> -->
            <view class="icon-hot spaceCenter" style="line-height: 34rpx;">
               <IconFont icon="back" :size="14" style="display: inline-block;height: 30rpx;"/>
             </view>
          </div>
          <!-- #ifdef APP-PLUS | H5 -->
          <BackHomeNav class="backHome_box">返回首页</BackHomeNav>
          <!-- #endif -->
        </div>
      </template>
      <template v-slot:right>
        <!-- #ifdef APP-PLUS | H5 -->
        <div v-if="scene == 'team'" @click="showTeamUsers">
          <uni-icons type="bars" size="18" color="#000"></uni-icons>
        </div>
        <!-- #endif -->
      </template>
    </NavBar>
    <step
      v-if="currentStepName"
      :currentStepName="currentStepName"
      :mainSteps="mainSteps"
    ></step>
    <div v-if="initOk" class="msg-alert">
      <NetworkAlert />
    </div>
    <div :class="isH5 ? 'msg-wrapper-h5' : 'msg-wrapper'">
      <MessageList
        :flowState="flowState"
        :scene="scene"
        :to="to"
        :msgs="msgs"
        :sessionId2="sessionId2"
        :loading-more="loadingMore"
        :no-more="noMore"
        :reply-msgs-map="replyMsgsMap"
      />
    </div>

    <!-- #ifdef MP-->
    <uniLoadMore  v-if="showMsgLoading" status="loading" icon-type="snow" color="#969696"/>
    <!-- #endif -->

    <div style="height: 'auto'">
      <MessageInput
        :flowState="flowState"
        :reply-msgs-map="replyMsgsMap"
        :scene="scene"
        :to="to"
        ref="childRef"
      />
    </div>
    <myOrder
      :myorderModal="myorderModal"
      @changeMyorderModal="changeMyorderModal"
      class="myorder_box"
    ></myOrder>

    <changeKf :changeKfModal="changeKfModal" @change="changeModal"> </changeKf>

    <productCard
      v-if="needShowProductCard"
      :product-detail="productDetail"
      @sendLink="sendLink"
    ></productCard>
    <orderCard
      v-if="needShowOrderCard"
      :product-detail="orderDetailProduct"
      :order-detail="orderDetail"
      @sendLink="sendOrder"
    ></orderCard>

    <uni-popup ref="uploadImgPopup" class="uploadImgPopup" type="dialog">
      <div class="uploadImg">
        <div class="tit">上传图片：</div>
        <div class="note">请根据群内指引上传图片</div>
        <div>
          <upLoadList
            :url-pic="uploadImgs"
            name-key="uploadImgs"
            @upSuccsessList="uploadImgSuc"
            @deletPicList="deletPic"
          />
        </div>
        <div class="spaceEnd">
          <div class="btn_cancel" @click="onCancel">取消</div>
          <div class="btn_ok" @click="onOk">确认</div>
        </div>
      </div>
    </uni-popup>

    <uni-popup ref="accountFormPopup" class="accountFormPopup" type="dialog">
      <div class="accountForm">
        <div class="tit">卖家提供交易账号：</div>
        <div class="note">请提供账号与密码供买家验号</div>
        <div class="spaceStart uni_input_box">
          <view class="spaceStart label"
            ><view class="required_red">*</view>{{ idLabel }}</view
          >
          <input
            v-model="accountForm.uid"
            class="uni-input"
            type="text"
            :placeholder="idPlaceHolder"
          />
        </div>
        <div class="spaceStart uni_input_box">
          <view class="spaceStart label"
            ><view class="required_red">*</view>账号</view
          >
          <input
            v-model="accountForm.username"
            class="uni-input"
            type="text"
            placeholder="请输入账号"
          />
        </div>
        <div class="spaceStart uni_input_box">
          <view class="spaceStart label"
            ><view class="required_red">*</view>密码</view
          >
          <input
            v-model="accountForm.password"
            class="uni-input"
            type="text"
            placeholder="请输入密码，如是手机账号请输入1"
          />
        </div>
        <div class="spaceEnd">
          <div class="btn_cancel" @click="onCancelAccountForm">取消</div>
          <div class="btn_ok" @click="onOkAccountForm">确认</div>
        </div>
      </div>
    </uni-popup>

    <uni-popup ref="buyerUpPhonePopup" class="buyerUpPhonePopup" type="dialog">
      <div class="buyerUpPhoneForm">
        <div class="tit">买家提供手机号：</div>
        <div class="note">请买家提供手机号进行换绑</div>
        <div class="spaceStart uni_input_box">
          <view class="spaceStart label"
            ><view class="required_red">*</view>手机号</view
          >
          <input
            v-model="buyerUpPhoneForm.mobile"
            maxlength="11"
            class="uni-input"
            type="number"
            placeholder="请输入手机号"
          />
        </div>
        <div class="spaceEnd">
          <div class="btn_cancel" @click="onCancelbuyerUpPhoneForm">取消</div>
          <div class="btn_ok" @click="onOkbuyerUpPhoneForm">确认</div>
        </div>
      </div>
    </uni-popup>

    <uni-popup ref="showSpzxPopup" class="showSpzxPopup" type="dialog">
      <spzx :actionObj="spzxItem" @close="onHideSpzx"></spzx>
    </uni-popup>

    <uni-popup
      ref="showSellerDrawPopup"
      class="showSellerDrawPopup"
      type="dialog"
    >
      <sellerDraw
        :actionData="showSellerDrawData"
        :actionObj="showSellerDrawItem"
        @close="onHideSellerDraw"
      ></sellerDraw>
    </uni-popup>

    <uni-popup ref="showScorePopup" class="showScorePopup" type="dialog">
      <score
        :actionData="showScoreData"
        :actionObj="showScoreItem"
        @close="onHideScore"
      ></score>
    </uni-popup>
  </div>
</template>

<script lang="ts" setup>
import uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue';

import _ from 'lodash';
// #ifdef APP-PLUS | H5
import BackHomeNav from '@/components/toolbar/BackHomeNav.vue';
// #endif
import score from './score.vue';
import { isAndroidApp } from '../../utils';
import sellerDraw from './sellerDraw.vue';
import spzx from './spzx.vue';
import upLoadList from '@/components/imgUpload/upLoadList.vue';
import { m2kfSendProduct, m2kfSendOrder, getFlowState } from '@/config/api/kf';
import myOrder from './myOrder.vue';
import changeKf from './changeKf.vue';
import productCard from './productCard.vue';
import orderCard from './orderCard.vue';
import { onHide, onShow } from '@dcloudio/uni-app';
import { events } from '../../utils/constants';
import { trackInit } from '../../utils/reporter';
import { autorun } from 'mobx';
import {
  ref,
  onMounted,
  onUnmounted,
  reactive,
  computed,
} from '../../utils/transformVue';
import { parseSessionId } from '../../utils/msg';
import type { TMsgScene } from '@xkit-yx/im-store';
import { deepClone, getUniPlatform } from '../../utils';
import { onLoad, onUnload } from '@dcloudio/uni-app';
import {
  customNavigateBack,
  customSwitchTab,
} from '../../utils/customNavigate';
import NetworkAlert from '../../components/NetworkAlert.vue';
import NavBar from './message/nav-bar.vue';
import Icon from '../../components/Icon.vue';
import MessageList from './message/message-list.vue';
import MessageInput from './message/message-input.vue';
import type { IMMessage } from 'nim-web-sdk-ng/dist/NIM_MINIAPP_SDK/MsgServiceInterface';
import { HISTORY_LIMIT, MSG_ID_FLAG } from '../../utils/constants';
import { t } from '../../utils/i18n';

import { getDetail } from '@/config/api/accountDetail.js';
import { getOrderDetail,teamAtKfer } from '@/config/api/confirmOrder.js';

import util from '@/utils/util';
import request from '@/common/request.js';
import step from './step.vue';
import { getUserinforApi } from '@/config/api/index.js';

import { getCertDetail } from '@/config/api/safeCenter.js';

const isShaking = ref('');
let userInfo = ref({});
let faceStatus = ref('');
let showMsgLoading = ref(false);

const showTeamUsers = () => {
  // 查看群成员
  uni.navigateTo({ url: `/pages/group/group?teamId=${to.value}` });
};
const getUserInfo = () => {
  getUserinforApi({}).then((res) => {
    if (res.code == 200) {
      userInfo.value = res.data;
    }
  });
};

getUserInfo();

const needFace = computed(() => {
  return (
    scene.value == 'team' &&
    userInfo.value.type == 0 &&
    flowState.value.type == 0 &&
    userInfo.value.imaccount == flowState.value.sellerim &&
    faceStatus.value !== '' &&
    faceStatus.value != 2
  );
});

const needCheckFace = computed(() => {
  let needCheck =
    scene.value == 'team' &&
    userInfo.value.type == 0 &&
    flowState.value.type == 0 &&
    userInfo.value.imaccount == flowState.value.sellerim;
  if (needCheck) {
    doGetCertDetail();
  }
  return needCheck;
});

const goFace = () => {
  const sessionId = uni.$UIKitStore?.uiStore.selectedSession;
  uni.navigateTo({
    url: `/pages/identify/faceIdentify?sessionId=${sessionId}`,
  });
};

const doGetCertDetail = () => {
  getCertDetail().then((res) => {
    if (res.code == 200) {
      const status = res.data.faceStatus;
      faceStatus.value = status;
    }
  });
};

const doGetCertDetail2 = () => {
  uni.showLoading({
    title: '拼命加载中',
  });
  getCertDetail().then((res) => {
    if (res.code == 200) {
      uni.hideLoading();
      const status = res.data.faceStatus;
      if (status != 2) {
        uni.showToast({
          title: '请先完成人脸认证',
          icon: 'none',
        });
      }
      faceStatus.value = status;
    }
  });
};

// 添加节流函数
const throttle = (fn: Function, delay: number) => {
  let lastTime = 0;
  return function (...args: any[]) {
    const now = Date.now();
    if (now - lastTime >= delay) {
      fn.apply(this, args);
      lastTime = now;
    }
  };
};

// 使用节流包装activeKf函数
const activeKf = throttle(() => {
  const selectedSession = uni.$UIKitStore.uiStore.selectedSession;
  const tempList = selectedSession.split('-');
  const scene = tempList.shift();
  const to = tempList.join('-');
  let txtMsg = `我有问题急需处理1`;
  let msgBody = {
    scene,
    to,
    body: txtMsg,
  };
  isShaking.value = true;
  setTimeout(() => {
    isShaking.value = false;
  }, 1000);
  teamAtKfer(to).then((res: any) => {
    console.log(res);

  }).catch(err => {
    console.error(err);
    return Promise.reject(err);
  });
  return
  if (scene === 'team') {
    uni.$UIKitNIM.nim.team
      .getTeamMembers({
        teamId: to,
      })
      .then((res) => {
     
        const findIt = res.find((ele: any) => ele.account.indexOf('kkzhw') === 0);
        if(!findIt){
              teamAtKfer(to).then((res: any) => {
                console.log(res);
                return Promise.reject('没有找到客服');
              }).catch(err => {
                console.error(err);
                return Promise.reject(err);
              });
              return;
        }
        const name = uni.$UIKitStore.uiStore.getAppellation({
          account: findIt.account,
        });
        txtMsg = `@${name} 我有问题急需处理`;
        const selectedAtMembers = {
          account: findIt.account,
          appellation: name,
        };
        const ext = util.onAtMembersExtHandler(txtMsg, [selectedAtMembers]);
        msgBody = {
          scene,
          to,
          body: txtMsg,
          ext,
        };
        uni.$UIKitStore.msgStore.sendTextMsgActive(msgBody).finally(() => {
          if (isAndroidApp) {
            setTimeout(() => {
              uni.$emit(events.ON_SCROLL_BOTTOM);
            }, 300);
          } else {
            uni.$emit(events.ON_SCROLL_BOTTOM);
          }
        });
      });
  } else {
    uni.$UIKitStore.msgStore.sendTextMsgActive(msgBody).finally(() => {
      if (isAndroidApp) {
        setTimeout(() => {
          uni.$emit(events.ON_SCROLL_BOTTOM);
        }, 300);
      } else {
        uni.$emit(events.ON_SCROLL_BOTTOM);
      }
    });
  }

  isShaking.value = true;
  setTimeout(() => {
    isShaking.value = false;
  }, 1000);
}, 1000); // 设置1秒的节流时间

const title = ref('');

const uploadImgs = ref([]);
const uploadImgPopup = ref(null);
const uploadImgUrl = ref('');
const showUploadImg = (options) => {
  uploadImgUrl.value = options.url;
  uploadImgPopup.value.open('bottom');
};
const onOk = () => {
  const images = uploadImgs.value;
  if (images.length == 0) {
    uni.showToast({
      title: '请选择图片上传',
      icon: 'error',
    });
    return;
  }
  let obj = util.getUrlParams(uploadImgUrl.value);
  let action = obj.action;
  let url = uploadImgUrl.value.split('?')[0];
  const data = {
    images,
    action,
  };
  uni.showLoading({
    title: '加载中',
    mask: true,
  });
  request
    .post(url, data)
    .then((res) => {
      if (res.code == 200) {
        uni.showToast({
          title: res.message,
          icon: 'none',
        });
        uploadImgs.value = [];
        uploadImgPopup.value.close();
      }
    })
    .finally(() => {
      uni.hideLoading();
    });
};
const onCancel = () => {
  uploadImgs.value = [];
  uploadImgPopup.value.close();
};
const uploadImgSuc = (url) => {
  uploadImgs.value.push(url);
};
const deletPic = (index) => {
  uploadImgs.value.splice(index, 1);
};

const buyerUpPhoneForm = ref({
  mobile: '',
});
const buyerUpPhonePopup = ref(null);
const buyerUpPhoneUrl = ref('');
const showBuyerUpPhone = (options) => {
  buyerUpPhoneUrl.value = options.url;
  let data = options.data || {};
  const { type, value } = data;
  if (type == 'mobile') {
    buyerUpPhoneForm.value.mobile = value;
  }
  buyerUpPhonePopup.value.open('bottom');
};

const onCancelbuyerUpPhoneForm = () => {
  buyerUpPhoneForm.value = {
    mobile: '',
  };
  buyerUpPhonePopup.value.close();
};

const onOkbuyerUpPhoneForm = () => {
  if (!buyerUpPhoneForm.value.mobile) {
    uni.showToast({
      title: '请输入手机号',
      icon: 'none',
    });
    return;
  }
  const mobileRegex = /^1\d{10}$/;
  if (!mobileRegex.test(buyerUpPhoneForm.value.mobile)) {
    uni.showToast({
      title: '手机号格式不正确，请输入11位手机号',
      icon: 'none',
    });
    return;
  }
  let obj = util.getUrlParams(buyerUpPhoneUrl.value);
  let action = obj.action;
  let url = buyerUpPhoneUrl.value.split('?')[0];
  const data = Object.assign({}, buyerUpPhoneForm.value, {
    action,
  });
  uni.showLoading({
    title: '加载中',
    mask: true,
  });
  request
    .post(url, data)
    .then((res) => {
      if (res.code == 200) {
        uni.showToast({
          title: res.message,
          icon: 'none',
        });
        buyerUpPhoneForm.value = {
          mobile: '',
        };
        buyerUpPhonePopup.value.close();
      }
    })
    .finally(() => {
      uni.hideLoading();
    });
};

const showSellerDrawPopup = ref(null);
const showSellerDrawItem = ref({});
const showSellerDrawData = ref({});
const onHideSellerDraw = () => {
  showSellerDrawPopup.value.close();
};
const showSellerDraw = (obj) => {
  showSellerDrawData.value = obj.data;
  showSellerDrawItem.value = obj.item;
  showSellerDrawPopup.value.open('center');
};

const showScorePopup = ref(null);
const showScoreItem = ref({});
const showScoreData = ref({});
const onHideScore = () => {
  showScorePopup.value.close();
};
const showScore = (obj) => {
  showScoreData.value = obj.data;
  showScoreItem.value = obj.item;
  showScorePopup.value.open('center');
};

const showSpzxPopup = ref(null);
const spzxItem = ref({});

const onHideSpzx = () => {
  showSpzxPopup.value.close();
};
const showSpzx = (obj) => {
  spzxItem.value = obj.item;
  showSpzxPopup.value.open('center');
};

const accountForm = ref({
  uid: '',
  username: '',
  password: '',
});
const onCancelAccountForm = () => {
  accountForm.value = {
    uid: '',
    username: '',
    password: '',
  };
  accountFormPopup.value.close();
};
const onOkAccountForm = () => {
  const regex = /^[\x00-\x7F]*$/;
  if (!accountForm.value.uid) {
    uni.showToast({
      title: '请输入uid',
      icon: 'none',
    });
    return;
  } else if (!regex.test(accountForm.value.uid)) {
    uni.showToast({
      title: 'uid格式错误',
      icon: 'none',
    });
    return;
  }
  if (accountForm.value.password && /[\u200B-\u200D\uFEFF]/.test(accountForm.value.password)) {
    accountForm.value.password = accountForm.value.password.replace(/[\u200B-\u200D\uFEFF]/g, '');
  }
  if (accountForm.value.username && /[\u200B-\u200D\uFEFF]/.test(accountForm.value.username)) {
    accountForm.value.username = accountForm.value.username.replace(/[\u200B-\u200D\uFEFF]/g, '');
  }
  if (!accountForm.value.username) {
    uni.showToast({
      title: '请输入账号',
      icon: 'none',
    });
    return;
  } else if (!regex.test(accountForm.value.username)) {
    uni.showToast({
      title: '账号格式错误',
      icon: 'none',
    });
    return;
  }
  if (!accountForm.value.password) {
    uni.showToast({
      title: '请输入密码',
      icon: 'none',
    });
    return;
  } else if (!regex.test(accountForm.value.password)) {
    uni.showToast({
      title: '密码格式错误',
      icon: 'none',
    });
    return;
  }
  let obj = util.getUrlParams(accountFormUrl.value);
  let action = obj.action;
  let url = accountFormUrl.value.split('?')[0];
  const data = Object.assign({}, accountForm.value, {
    action,
  });
  uni.showLoading({
    title: '加载中',
    mask: true,
  });
  request
    .post(url, data)
    .then((res) => {
      if (res.code == 200) {
        uni.showToast({
          title: res.message,
          icon: 'none',
        });
        accountForm.value = {
          uid: '',
          username: '',
          password: '',
        };
        accountFormPopup.value.close();
      }
    })
    .finally(() => {
      uni.hideLoading();
    });
};

const accountFormPopup = ref(null);
const accountFormUrl = ref('');
const idPlaceHolder = ref('');
const idLabel = ref('');
const showAccountForm = (options) => {
  accountFormUrl.value = options.url;
  if (options.iswz) {
    idPlaceHolder.value = '请输入营地id';
    idLabel.value = '营地id';
  } else {
    idPlaceHolder.value = '请输入uid,没有输入1';
    idLabel.value = 'UID';
  }
  accountFormPopup.value.open('bottom');
};

let uninstallHistoryWatch;
let uninstallMsgsWatch;
let myAccount = '';
let sessionId = '';
let scene = ref('');
let to = ref('');
const doInit = () => {
  // @ts-ignore
  trackInit('ChatUIKit');
  sessionId = uni.$UIKitStore.uiStore.selectedSession;
  const obj = parseSessionId(sessionId);
  scene.value = obj.scene;
  to.value = obj.to;
  // @ts-ignore
  myAccount = uni.$UIKitStore.userStore.myUserInfo.account;
};
let myorderModal = ref(false);

const showMyOrder = () => {
  myorderModal.value = true;
};

const changeMyorderModal = (val) => {
  myorderModal.value = val;
};

const changeKfModal = ref(false);
const showChangeKF = () => {
  
  changeKfModal.value = true;
};

const changeModal = (val) => {
  changeKfModal.value = val;
};

const isH5 = getUniPlatform() === 'web';

// 处理uni-popup 引起的滚动穿透
const moveThrough = ref(false);

const backToConversation = () => {
  customNavigateBack();
};

let isMounted = false;

const loadingMore = ref(false);
const noMore = ref(false);
let initOk = ref(false);

const msgs = ref<IMMessage[]>([]);

// 回复
const replyMsgsMap = ref<Record<string, IMMessage>>();

const sendLink = () => {
  const content = `<div class="spaceBetween msg-flexstart">
      <img src="${productDetail.value.pic}" class="msg-productImg" />
      <div>
        <div class="twoLine">${productDetail.value.subTitle.substring(0,30)}</div>
        <div class="msg-red">￥${productDetail.value.price || ''}</div>
      </div>
    </div>`;
  let type4List = [];
  productAttributeList.value.forEach((ele) => {
    if (ele.type == 4) {
      type4List.push(ele.name);
    }
  });
  if (
    productDetail.value.stock == 0 ||
    productDetail.value.publishStatus == -2
  ) {
    type4List = [];
  }
  const attach = {
    data: {
      type: 'product',
      productSn: productDetail.value.productSn,
      productId: productDetail.value.id,
      productCategoryId: productDetail.value.productCategoryId,
    },
    body: {
      title: productDetail.value.subTitle.substring(0,30),
      content,
      type4List,
    },
    type: 'kk_product_msg_fed',
  };
  m2kfSendProduct({
    productId: productDetail.value.id,
    kfIM: to.value,
  });
  uni.$UIKitStore.msgStore
    .sendCustomMsgActive({
      scene: scene.value,
      from: myAccount,
      to: to.value,
      attach: JSON.stringify(attach),
    })
    .finally(() => {
      if (isAndroidApp) {
        setTimeout(() => {
          uni.$emit(events.ON_SCROLL_BOTTOM);
        }, 300);
      } else {
        uni.$emit(events.ON_SCROLL_BOTTOM);
      }
    })
    .catch((err) => {
      console.log('发送失败', err);
    });
};
const handleDismissTeam = (data: any) => {
  if (data.teamId === to.value) {
    uni.showModal({
      content: t('onDismissTeamText'),
      showCancel: false,
      success(data) {
        if (data.confirm) {
          backToConversation();
        }
      },
    });
  }
};

const handleRemoveTeamMembers = (data: any) => {
  if (data.team.teamId === to.value && data.accounts.includes(myAccount)) {
    uni.showModal({
      content: t('onRemoveTeamText'),
      showCancel: false,
      success(data) {
        if (data.confirm) {
          backToConversation();
        }
      },
    });
  }
};

const throttledGetDetailForCard = _.throttle((teamId) => {
  doGetFlowState(teamId);
}, 2000);

const handleMsg = (msg: IMMessage) => {

  if (childRef.value) {
    childRef.value.getNegotiaDetailData(); // 调用子组件暴露的方法
  }
  const { to, scene } = msg;
  if (scene == 'team') {
    // 选中的群有消息
    throttledGetDetailForCard(to);
  }
  uni.$emit('addUnreadCount');
  // uni.$emit(events.ON_SCROLL_BOTTOM, msg);
};

const handleSyncOfflineMsgs = () => {
  const timer = setTimeout(() => {
    uni.$emit(events.ON_SCROLL_BOTTOM);
    clearTimeout(timer);
  }, 1000);
};

const getHistory = async (endTime: number, lastMsgId?: string) => {
  try {
    if (noMore.value) {
      return [];
    }
    if (loadingMore.value) {
      return [];
    }
    loadingMore.value = true;
    if (sessionId) {
      // @ts-ignore
      const historyMsgs = await uni.$UIKitStore.msgStore.getHistoryMsgActive({
        sessionId,
        endTime,
        lastMsgId,
        limit: HISTORY_LIMIT,
      });
      loadingMore.value = false;
      if (historyMsgs.length < HISTORY_LIMIT) {
        noMore.value = true;
      }
      return historyMsgs;
    }
  } catch (error) {
    loadingMore.value = false;
    throw error;
  }
};

const handleLoadMore = async (lastMsg: IMMessage) => {
  const res = await getHistory(lastMsg.time, lastMsg.idServer);
  return res;
};

const online = ref(false);

let currentStepName = ref('');
let mainSteps = ref([]);
let flowState = ref({});
const doGetFlowState = (teamId: any) => {
  getFlowState({ teamId }).then((res) => {
    if (res.code == 200) {
      const result = res.data;
      flowState.value = result;
      const processFlow = JSON.parse(result.processFlow);
      const mainStepsObj = processFlow.mainSteps;
      let findIt;
      let tempList = [];
      if (mainStepsObj) {
        Object.keys(mainStepsObj).forEach((key) => {
          let item = mainStepsObj[key];
          if (item.current == 1) {
            findIt = item;
          }
          tempList.push(item);
        });
        tempList.sort((a, b) => {
          return a.step - b.step;
        });
        if (findIt) {
          currentStepName.value = findIt.stepName;
          mainSteps.value = tempList;
        }
      }
    }
  });
};
let needShowProductCard = ref(false);

let sessionId2 = ref('');
onLoad((e) => {
  sessionId2.value = e.sessionId;
  if (uni.$UIKitStore && uni?.$UIKitStore?.uiStore?.selectedSession) {
    doInit();
    afterInit(e);
  } else {
    // h5 可能用户会刷新。要重新初始化 im。延迟 1s 是因为要拿群名称
    // 刷新页面不存在未激活的情况，不用考虑active
    autorun(() => {
      IMok.value = uni.$IMok?.imok;
      if (IMok.value && !uni.$UIKitStore?.uiStore?.selectedSession) {
        if (addAutoRunIsOk.value) {
          return;
        }
        addAutoRunIsOk.value = true;
        uni.$UIKitStore.uiStore.selectSession(e.sessionId);
        doInit();
        afterInit(e);
        // 如果是刷新的执行一下 mounted 里的
        addAutoRun();
      }
    });
  }
});
let IMok = ref(false);

const afterInit = (e) => {
  initOk.value = true;
  removeEvent();
  // @ts-ignore
  uni.$currentAudioContext = '';
  doMounted();
  if (e.productId) {
    needShowProductCard.value = true;
    getProductDetail(e.productId);
  }
  if (e.orderId) {
    getOrder(e.orderId);
  }
};
let uninstallOnline;
onShow(function () {
  const timer = setTimeout(() => {
    uni.$emit(events.ON_SCROLL_BOTTOM);
    clearTimeout(timer);
  }, 300);
  uninstallOnline = autorun(() => {
    if (scene.value === 'p2p') {
      // @ts-ignore
      title.value = deepClone(
        // @ts-ignore
        uni.$UIKitStore.uiStore.getAppellation({ account: to.value }),
      );
      const stateMap = deepClone(uni.$UIKitStore.eventStore.stateMap);
      online.value = stateMap.get(to.value);
    } else if (scene.value === 'team') {
      online.value = false;
      // @ts-ignore
      const team = uni.$UIKitStore.teamStore.teams.get(to.value);
      const subTitle = `(${team?.memberNum || 0})`;
      title.value = (team?.name || '') + subTitle;
    }
  });
});
const doMounted = () => {
  // 异步加事件，保证最后事件能加上
  setTimeout(() => {
    // @ts-ignore
    uni.$on(events.HANDLE_MOVE_THROUGH, (flag) => {
      moveThrough.value = flag;
    });
    // 加事件
    // @ts-ignore
    uni.$on('activeKf', activeKf);
    // @ts-ignore
    uni.$on('showUploadImg', showUploadImg);
    // @ts-ignore
    uni.$on('showBuyerUpPhone', showBuyerUpPhone);
    // @ts-ignore
    uni.$on('showSpzx', showSpzx);
    // @ts-ignore
    uni.$on('showSellerDraw', showSellerDraw);

    uni.$on('showScore', showScore);
    // @ts-ignore
    uni.$on('showAccountForm', showAccountForm);
    // @ts-ignore
    uni.$on('showMyOrder', showMyOrder);
    // @ts-ignore
    uni.$on('showChangeKF', showChangeKF);

    // 保证H5 刷新有值，不能在onMounted 执行
    const timer = setTimeout(() => {
      uni.$emit(events.ON_SCROLL_BOTTOM);
      clearTimeout(timer);
    }, 300);

    // @ts-ignore
    uni.$UIKitNIM.on('msg', handleMsg);
    // @ts-ignore
    uni.$UIKitNIM.on('dismissTeam', handleDismissTeam);
    // @ts-ignore
    uni.$UIKitNIM.on('removeTeamMembers', handleRemoveTeamMembers);
    // @ts-ignore
    uni.$UIKitNIM.on('syncOfflineMsgs', handleSyncOfflineMsgs);

    uni.$on(events.GET_HISTORY_MSG, (msg: IMMessage) => {
      handleLoadMore(msg)
        .then((res: IMMessage[]) => {
          if (res?.[0]) {
            // uni.pageScrollTo 微信小程序指定滚动位置不起作用 https://ask.dcloud.net.cn/question/173874?item_id=258278&rf=false
            setTimeout(() => {
              uni.pageScrollTo({
                selector: `#${MSG_ID_FLAG + res[0].idClient}`,
                scrollTop: 0,
                duration: 0,
                fail: (error) => {
                  console.log('error', error);
                },
              });
            }, 300);
          }
        })
        .finally(() => {
          // uni.stopPullDownRefresh();
        });
    });

    // 获取在线状态
    if (scene.value === 'p2p') {
      // @ts-ignore
      title.value = deepClone(
        // @ts-ignore
        uni.$UIKitStore.uiStore.getAppellation({ account: to.value }),
      );
      // @ts-ignore
      const stateMap = deepClone(uni.$UIKitStore.eventStore.stateMap);
      online.value = stateMap.get(to.value);
    } else if (scene.value === 'team') {
      online.value = false;
      // @ts-ignore
      const team = uni.$UIKitStore.teamStore.teams.get(to.value);
      const subTitle = `(${team?.memberNum || 0})`;
      title.value = (team?.name || '') + subTitle;
      const { teamId } = team;
      doGetFlowState(teamId);
    }
  }, 34);
};
let orderDetailProduct = ref({});
let orderDetail = ref({});
let orderId = ref('');
let needShowOrderCard = ref(false);
const getOrder = (id) => {
  orderId.value = id;
  getOrderDetail(id).then((res) => {
    orderDetail.value = res.data;
    const { orderItemList } = orderDetail.value;
    const findProduct = orderItemList.find((ele) => {
      return ele.itemType === 0;
    });
    orderDetailProduct.value = findProduct;
    needShowOrderCard.value = true;
  });
};

const sendOrder = () => {
  needShowOrderCard.value = false;
  const status = util.getStatus(orderDetail.value.orderStatus);
  const createTime = util.formatTime(
    orderDetail.value.createTime,
    'YYYY-MM-DD HH:mm:ss',
  );
  const content = `
    <div>
      <div class="spaceBetween msg-flexstart">
        <img src="${orderDetailProduct.value.productPic}" class="msg-productImg" />
        <div>
          <div class="twoLine">${orderDetailProduct.value.productName}</div>
          <div class="msg-red">￥${orderDetail.value.payAmount}</div>
        </div>
      </div>
      <div class="spaceBetween">
          <div>订单状态：</div>
          <div>${status}</div>
      </div>
      <div class="spaceBetween">
          <div>订单号：</div>
          <div>${orderDetail.value.orderSn}</div>
      </div>
      <div class="spaceBetween">
          <div>订单时间：</div>
          <div>${createTime}</div>
      </div>
    </div>`;
  const attach = {
    data: {
      type: 'order',
      orderId: orderId.value,
      orderSn: orderDetail.value.orderSn,
      productId: orderDetailProduct.value.productId,
      productSn: orderDetailProduct.value.productSn,
    },
    body: {
      title: '我要咨询这笔订单',
      content,
    },
    type: 'kk_order_msg_fed',
  };
  m2kfSendOrder({
    orderId: orderId.value,
    kfIM: to.value,
  });
  uni.$UIKitStore.msgStore
    .sendCustomMsgActive({
      scene: scene.value,
      from: myAccount,
      to: to.value,
      attach: JSON.stringify(attach),
    })
    .finally(() => {
      if (isAndroidApp) {
        setTimeout(() => {
          uni.$emit(events.ON_SCROLL_BOTTOM);
        }, 300);
      } else {
        uni.$emit(events.ON_SCROLL_BOTTOM);
      }
    })
    .catch((err) => {
      console.log('发送失败', err);
    });
};

let productDetail = ref({});
let productAttributeList = ref({});
const getProductDetail = (productId) => {
  getDetail(productId).then((res) => {
    productDetail.value = res.data.product;
    productAttributeList.value = res.data.productAttributeList;
  });
};

let addAutoRunIsOk = ref(false);
const addAutoRun = () => {
  uninstallHistoryWatch = autorun(() => {
    // @ts-ignore
    if (uni.$UIKitStore.connectStore.connectState === 'connected') {
      getHistory(Date.now()).then(() => {
        if (!isMounted) {
          uni.$emit(events.ON_SCROLL_BOTTOM);
          isMounted = true;
        }
      });
    }
  });
  // 动态更新消息
  uninstallMsgsWatch = autorun(() => {
    // @ts-ignore
    msgs.value = deepClone(uni.$UIKitStore.msgStore.getMsg(sessionId));

    // 遍历所有消息，找出被回复消息，储存在map中
    if (msgs.value.length !== 0) {
      const _replyMsgsMap: any = {};
      const reqMsgs: Array<{
        scene: 'p2p' | 'team';
        from: string;
        to: string;
        idServer: string;
        time: number;
      }> = [];
      const idClients: Record<string, string> = {};
      msgs.value.forEach((msg) => {
        const { scene, idServer, from, to } = msg;
        try {
          if (from !== uni.$UIKitStore.userStore.myUserInfo.account) {
            if (scene == 'team' && idServer) {
              uni.$UIKitNIM.sendTeamMsgReceipt({
                teamMsgReceipts: [
                  {
                    idClient: msg.idClient,
                    idServer: msg.idServer,
                    teamId: msg.to,
                  },
                ],
              });
            } else {
              uni.$UIKitNIM.sendMsgReceipt({
                msg,
              });
            }
          }
        } catch (e) {
          console.log(e);
        }

        if (msg.ext) {
          try {
            // yxReplyMsg 存储着被回复消息的相关消息
            const { yxReplyMsg } = JSON.parse(msg.ext);
            if (yxReplyMsg) {
              // 从消息列表中找到被回复消息，replyMsg 为被回复的消息
              const replyMsg = msgs.value.find(
                (item) => item.idClient === yxReplyMsg.idClient,
              );
              // 如果直接找到，存储在map中
              if (replyMsg) {
                _replyMsgsMap[msg.idClient] = replyMsg;
                // 如果没找到，说明被回复的消息可能有三种情况：1.被删除 2.被撤回 3.不在当前消息列表中（一次性没拉到，在之前的消息中）
              } else {
                _replyMsgsMap[msg.idClient] = { idClient: 'noFind' };
                const { scene, from, to, idServer, time } = yxReplyMsg;
                if (scene && from && to && idServer && time) {
                  reqMsgs.push({ scene, from, to, idServer, time });
                  idClients[idServer] = msg.idClient;
                }
              }
            }
          } catch {}
        }
      });

      if (reqMsgs.length > 0) {
        // 从服务器拉取被回复消息, 但是有频率控制
        // @ts-ignore
        uni.$UIKitStore.msgStore
          .getMsgByIdServerActive({ reqMsgs })
          .then((res: IMMessage[]) => {
            if (res?.length > 0) {
              res.forEach((item: IMMessage) => {
                if (item.idServer) {
                  _replyMsgsMap[idClients[item.idServer]] = item;
                }
              });
            }
            replyMsgsMap.value = { ..._replyMsgsMap };
          })
          .catch(() => {
            replyMsgsMap.value = { ..._replyMsgsMap };
          });
      } else {
        replyMsgsMap.value = { ..._replyMsgsMap };
      }
    }

    // 当聊天消息小于6条时，由于页面被键盘撑起，导致已经发出的消息不可见，所以需要隐藏键盘
    if (msgs.value.length < 6) {
      uni.hideKeyboard();
    }
  });
};
const childRef = ref(null);
onMounted(() => {
  // childRef.value.getnegoDetail();
  // #ifdef MP
  showMsgLoading.value = true
  setTimeout(()=>{
    showMsgLoading.value = false
   
  },1500)
  // #endif

  if (uni.$UIKitStore && uni?.$UIKitStore?.uiStore?.selectedSession) {
    addAutoRun();
  }

  document.body.style.height='100%'
});
const removeEvent = () => {
  // @ts-ignore
  uni.$off('activeKf');
  // @ts-ignore
  uni.$off('showUploadImg');
  // @ts-ignore
  uni.$off('showBuyerUpPhone');
  // @ts-ignore
  uni.$off('showSpzx');
  // @ts-ignore
  uni.$off('showSellerDraw');
  // @ts-ignore
  uni.$off('showScore');
  // @ts-ignore
  uni.$off('showAccountForm');
  // @ts-ignore
  uni.$off('showMyOrder');
  // @ts-ignore
  uni.$off('showChangeKF');
  // @ts-ignore
  uni.$off(events.CONFIRM_FORWARD_MSG);
  // @ts-ignore
  uni.$off(events.CANCEL_FORWARD_MSG);

  uni.$UIKitNIM.off('dismissTeam', handleDismissTeam);
  // @ts-ignore
  uni.$UIKitNIM.off('removeTeamMembers', handleRemoveTeamMembers);
  // @ts-ignore
  uni.$UIKitNIM.off('msg', handleMsg);
  // @ts-ignore
  uni.$UIKitNIM.off('syncOfflineMsgs', handleSyncOfflineMsgs);

  uni.$off(events.GET_HISTORY_MSG);
};
onUnload(() => {
  // 移除事件，避免重复执行
  removeEvent();
});

onUnmounted(() => {
  document.body.style.height=''
  // @ts-ignore
  uni.$UIKitNIM.off('dismissTeam', handleDismissTeam);
  // @ts-ignore
  uni.$UIKitNIM.off('removeTeamMembers', handleRemoveTeamMembers);
  // @ts-ignore
  uni.$UIKitNIM.off('msg', handleMsg);
  // @ts-ignore
  uni.$UIKitNIM.off('syncOfflineMsgs', handleSyncOfflineMsgs);

  uni.$off(events.GET_HISTORY_MSG);

  uninstallHistoryWatch();
  uninstallMsgsWatch();
  uninstallOnline();
  uni.$UIKitStore.uiStore.selectSession('');
});
</script>

<style scoped lang="scss">

.backHome_box {
}
.uploadImgPopup {
  position: relative;
  z-index: 1100;
  .tit {
    font-weight: 700;
  }
  .note {
    color: #fc6116;
  }
  .uploadImg {
    padding: 40rpx 40rpx 80rpx 40rpx;
    background: #fff;
    .btn_cancel {
      margin-right: 20rpx;
    }
    .note {
      margin: 20rpx 0;
    }
  }
}
.accountFormPopup {
  position: relative;
  z-index: 1100;
  .tit {
    font-weight: 700;
  }
  .note {
    color: #fc6116;
  }
  .uni-input {
    background-color: #f3f3f3;
    height: 94rpx;
    line-height: 94rpx;
    box-sizing: border-box;
    padding: 0 20rpx;
    border-radius: 6rpx;
    flex: 1;
  }
  .accountForm {
    padding: 40rpx 40rpx 80rpx 40rpx;
    background: #fff;
    .label {
      width: 120rpx;
    }
    .uni_input_box {
      margin: 20rpx 0;
    }
  }
  .btn_cancel {
    margin-right: 20rpx;
  }
  .note {
    margin: 20rpx 0;
  }
}
.showSpzxPopup {
  position: relative;
  z-index: 100;
}

.buyerUpPhonePopup {
  position: relative;
  z-index: 1100;
  .tit {
    font-weight: 700;
  }
  .note {
    color: #fc6116;
  }
  .uni-input {
    background-color: #f3f3f3;
    height: 94rpx;
    line-height: 94rpx;
    box-sizing: border-box;
    padding: 0 20rpx;
    border-radius: 6rpx;
    flex: 1;
  }
  .buyerUpPhoneForm {
    padding: 40rpx;
    background: #fff;
    .label {
      width: 120rpx;
    }
    .uni_input_box {
      margin: 20rpx 0;
    }
  }
  .btn_cancel {
    margin-right: 20rpx;
  }
  .note {
    margin: 20rpx 0;
  }
}

.btn_ok {
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: 400;
  padding: 18rpx 30rpx;
  box-sizing: border-box;
  background: #409eff;
  border-color: #409eff;
  color: #fff;
}
.btn_cancel {
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: 400;
  padding: 18rpx 30rpx;
  box-sizing: border-box;
  color: #409eff;
  background: #fff;
  border: 2rpx solid #b3d8ff;
}
.myorder_box {
  z-index: 9999;
}
.productPic {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
}
page {
  height: 100%;
  overflow: hidden;
}

.msg-page-wrapper {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.msg-page-wrapper-h5 {
  width: 100%;
  // height: 100%;
  height: 100vh;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;
}

.msg-alert {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: auto;
  z-index: 1;
}

.msg-wrapper {
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;
  flex: 1;
}

.msg-wrapper-h5 {
  width: 100%;
  height: 100%;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;
}

.msg-wrapper > message-list {
  height: 100%;
}
.faceBtn {
  position: fixed;
  background: rgba(2, 2, 2, 0.3);
  width: 100%;
  z-index: 9999;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-right: 14rpx;
  justify-content: center;
  align-items: center;
  left: 0;
  top: 80rpx;
  .facenote {
    background: #fff;
    display: flex;
    flex-direction: column;
    padding-right: 14rpx;
    justify-content: center;
    align-items: center;
    padding: 60rpx 30rpx;
  }
  .note {
    color: #666;
    font-size: 28rpx;
    font-weight: 700;
    padding-bottom: 30rpx;
  }
  .bar_item {
    margin-top: 20rpx;
    font-size: 12px;
    height: 28px;
    line-height: 28px;
    background: #fff;
    border-radius: 14px;
    padding: 0 14px;
    border: 1px solid #fff;
    text-align: center;
  }
  .active {
    border-color: #e60f0f;
    color: #e60f0f;
  }
}
@keyframes shake {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  50% {
    transform: translateX(5px);
  }
  75% {
    transform: translateX(-3px);
  }
  100% {
    transform: translateX(0);
  }
}

/* 触发 shake 动画的类 */
.shake {
  animation-duration: 0.5s;
  animation-fill-mode: both;
  animation-name: shake;
}

/deep/ .imcard {
  .spaceStart {
    display: flex !important;
  }
  .imline {
    border-bottom: 2rpx dashed #ccc;
    margin: 20rpx 0;
  }
  .avatarImg {
    width: 60rpx !important;
    height: 60rpx !important;
    border-radius: 60rpx;
  }
  .name {
    margin-left: 20rpx;
  }
  .imtxt {
    margin-right: 20rpx;
  }
  .ima {
    cursor: pointer;
    color: #409eff;
  }
}
</style>
