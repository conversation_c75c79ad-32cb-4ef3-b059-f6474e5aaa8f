<template>
  <div class="input-root">
    <!-- 以下这个 div 用于确保 vue2 的 ref 会更新 -->
    <div style="display: none">
      <div>{{ teamMute ? '禁言' : '不禁言' }}</div>
      <div>{{ isTeamMute ? '禁言' : '不禁言' }}</div>
    </div>
    <div class="msg-input-wrapper">
      <!-- 键盘上要显示的回复消息内容 -->
      <div v-if="isReplyMsg" class="reply-message-wrapper">
        <div class="reply-message-close" @tap="removeReplyMsg">
          <Icon
            color="#929299"
            :iconStyle="{ fontWeight: '200' }"
            :size="13"
            type="icon-guanbi"
          />
        </div>
        ｜
        <div class="reply-title">{{ t('replyText') }}</div>
        <div
          class="reply-noFind"
          v-if="replyMsg && replyMsg.idClient == 'noFind'"
        >
          {{ t('replyNotFindText') }}
        </div>
        <div class="reply-message" v-else>
          <div class="reply-to">
            <Appellation
              :account="replyMsg.from"
              :team-id="scene === 'team' ? to : ''"
              color="#929299"
              :fontSize="13"
            >
            </Appellation>
          </div>
          :
          <div v-if="replyMsg.type === 'text'" class="reply-msg-content">
            <message-one-line :text="replyMsg.body"></message-one-line>
          </div>
          <div v-else>{{ '[' + REPLY_MSG_TYPE_MAP[replyMsg.type] + ']' }}</div>
        </div>
      </div>
      <!-- 输入框上按钮组 -->
      <div class="msg-button-group spaceStart">
        <div class="tool_bar" v-if="!negoId&&props.to&&!props.to.startsWith('kkzhwzx')">
          <div @click="activeKf" class="kk-btn line"><span>提醒客服</span></div>
        </div>
        <div class="tool_bar" v-if="customTTP==0&&cardIdentity=='purchaser'">
          <div @click="applyCard" class="kk-btn line"><span>便捷办卡</span></div>
        </div>
        <div v-if="scene === 'p2p'" class="tool_bar">
          <div @click="showMyOrder" class="kk-btn line"><span>我要咨询</span></div>
        </div>
        <div v-if="scene === 'p2p'" class="tool_bar">
          <div @click="showChangeKF" class="kk-btn line"><span>{{props.to&&props.to.startsWith('kkzhwzx')?'人工客服':'换个客服'}}</span></div>
        </div>
        <div v-if="ishaosan" class="tool_bar">
          <div @click="startCallKf" class="kk-btn line"><span>开始交易</span></div>
        </div>
        <view  style="display:flex;align-items: center;" v-if="negoId&&identities=='seller'">
          <view
              v-for="btn in getBtns(negoData)"
              :key="btn.name"
              :class="btn.class"
              style="margin-left: 20rpx;display:flex"
              @click.stop="btn.func(negoData)"
              ><span>{{ btn.name }}</span> </view
        >
          <view v-if="negoData.status == 2 && negoData.sellerOfferPrice > 0" class="nogoStatusText">
            等待买家答复
          </view>
          <view v-if="negoData.status == 3" class="nogoStatusText">
            议价已取消
          </view>
          <view v-if="negoData.status == 1" class="nogoStatusText">
            等待买家购买
          </view>
        </view>
        <view v-if="negoId&&identities=='purchaser'" style="display:flex;align-items: center;" >
          <div v-if="negoId&&(negoData.status == 1 || (negoData.status == 2 && negoData.sellerOfferPrice))" @click="palyPage(negoData)" class="kk-btn line"><span>立即购买</span></div>
          <div v-if="negoId&&negoData.status == 2 && negoData.sellerOfferPrice" @click="openHjPop" style="margin-left: 20rpx;" class="kk-btn line"><span>还价</span></div>
          <view v-if="negoData.status == 0" class="nogoStatusText">
            等待卖家答复
          </view>
          <view v-if="negoData.status == 3" class="nogoStatusText">
            议价已取消
          </view>
        </view>
       
        <div v-if="!isWeb" class="msg-input-button" @tap="handleAudioVisible">
          <!-- <Icon
            v-if="audioPanelVisible"
            :size="20"
            type="audio-btn-selected"
            key="audio-btn-selected"
          />
          <Icon v-else :size="20" type="icon-audio" key="icon-audio" /> -->
        </div>
        <div class="spaceEnd" style="margin-left: auto">
          <div class="msg-input-button" @tap="handleEmojiVisible">
            <Icon :size="20" type="icon-biaoqing" />
          </div>
          <div
            class="msg-input-button"
            style="margin-left: 10rpx"
            @tap="handleSendImageMsg"
          >
            <div>
              <Icon :size="20" type="icon-tupian" />
            </div>
          </div>
        </div>

        <!-- <div class="msg-input-button" @tap="handleSetting">
          <Icon type="icon-shezhi" :size="20" />
        </div>
        <div class="msg-input-button" @tap="handleSendMoreVisible">
          <Icon type="send-more" :size="20" />
        </div> -->
      </div>
      <div v-if="inputVisible" class="msg-input">
        <!-- 当从表情面板切换到文字输入时，直接唤起键盘，会导致input框滚动消失，故此处需要用EmojiInput兼容下，保证先隐藏表情面板，再弹出键盘 -->
        <div
          v-show="showEmojiInput"
          @click="onClickEmojiInput"
          class="input-emoji"
        >
          <div v-if="inputText" class="input-text">{{ inputText }}</div>
          <div v-else class="input-placeholder">
            {{
              isTeamMute ? t('teamMutePlaceholder') : t('chatInputPlaceHolder')
            }}
          </div>
        </div>
        <input
          v-show="!showEmojiInput"
          :focus="isFocus"
          class="msg-input-input"
          :maxlength="-1"
          :placeholder="
            isTeamMute ? t('teamMutePlaceholder') : t('chatInputPlaceHolder')
          "
          v-model="inputText"
          type="text"
          :disabled="isTeamMute"
          :confirm-hold="true"
          cursor-spacing="20"
          adjust-position="true"
          confirm-type="send"
          @confirm="handleSendTextMsg"
          @blur="handleInputBlur"
          @input="handleInput"
          @focus="handleInputFocus"
          id="msg-input"
        />
      </div>
      <!-- 表情面板 -->
      <div v-if="emojiVisible" class="msg-emoji-panel" @click.stop="() => {}">
        <Face
          @emojiClick="handleEmoji"
          @emojiDelete="handleEmojiDelete"
          @emojiSend="handleSendTextMsg"
        />
      </div>
      <!-- 发送语音消息面板 -->
      <div
        v-if="audioPanelVisible"
        class="msg-audio-panel"
        @click.stop="() => {}"
      >
        <VoicePanel @handleSendAudioMsg="handleSendAudioMsg"></VoicePanel>
      </div>
      <!-- 发送更多面板 -->
      <div
        v-if="sendMoreVisible"
        class="send-more-panel"
        @click.stop="() => {}"
      >
        <div class="send-more-panel-item-wrapper">
          <div
            class="send-more-panel-item"
            @tap="
              (
                // @ts-ignore
                event,
              ) => handleSendVideoMsg('camera', event)
            "
          >
            <Icon type="icon-paishe" :size="30"></Icon>
          </div>
          <div class="icon-text">{{ t('shootText') }}</div>
        </div>
        <div class="send-more-panel-item-wrapper">
          <div
            class="send-more-panel-item"
            @tap="
              (
                // @ts-ignore
                event,
              ) => handleSendVideoMsg('album', event)
            "
          >
            <Icon type="icon-shipin2" :size="30"></Icon>
          </div>
          <div class="icon-text">{{ t('albumText') }}</div>
        </div>
        <div
          class="send-more-panel-item-wrapper"
          v-if="isApp && scene !== 'team'"
        >
          <div class="send-more-panel-item" @tap="handleCall(1)">
            <Icon type="icon-audio-call" :size="30"></Icon>
          </div>
          <div class="icon-text">{{ t('voiceCallText') }}</div>
        </div>
        <div
          class="send-more-panel-item-wrapper"
          v-if="isApp && scene !== 'team'"
        >
          <div class="send-more-panel-item" @tap="() => handleCall(2)">
            <Icon type="icon-video-call" :size="30"></Icon>
          </div>
          <div class="icon-text">{{ t('videoCallText') }}</div>
        </div>
      </div>
    </div>
    <!-- @消息相关 popup -->
    <UniPopup
      ref="popupRef"
      background-color="#ffffff"
      type="bottom"
      mask-background-color="rgba(0,0,0,0.4)"
      @change="onPopupChange"
    >
      <MentionMemberList :team-id="to"></MentionMemberList>
    </UniPopup>
       <!-- 二次询问弹窗 -->
       <TipPanel
      v-if="showConfirmPop"
      @cancel="showConfirmPop = false"
      @confirm="active"
    >
      <view class="second-tip-panel-txt">{{ confirmPopInfo.text }} </view>
    </TipPanel>
        <!-- 还价 -->
        <TipPanel
      v-if="showHjPop"
      confirm-text="提交"
      @cancel="showHjPop = false"
      @confirm="submitHjForm"
    >
      <view class="hj_title">还价</view>
      <view class="hj_tip">如{{identities=='seller'?'卖家':'买家'}}同意还价金额，不能无责取消</view>
      <MyForm
        ref="hjForm"
        :label-width="60"
        :data-json="formDataJson"
        :has-submit-btn="false"
        @submit="counterPriceSure"
         @change="counterPriceSureChange"
      >
      </MyForm>
      <view v-if="identities=='seller'" style="margin-top: 20rpx;margin-left: 48rpx;">
        <view style="font-size: 28rpx">
          <span style="color: red;">*</span> 是否同步修改商品售价为  ¥{{ counterPrice }}?
        </view>
        <view class="agreeWithOfferRadioBox">
          <radio-group @change="counterRadioChange">
            <label class="uni-list-cell uni-list-cell-pd" v-for="(item, index) in radioItems" :key="item.value">
              <view style="display: flex;align-items: center;">
                <view>
                <radio style="transform: scale(0.7);"  activeBackgroundColor="#ff720c" :value="item.value" :checked="item.value === currentPrice" />
              </view>
              <view>{{item.name}}</view>
              </view>
            </label>
          </radio-group>
        </view>
     </view>
    </TipPanel>
    <!-- 议价设置 -->
    <TipPanel
      v-if="yijiaVisible"
      @cancel="yijiaVisible = false"
      @confirm="yijiaConfigSubmit"
      confirmText="保存"
    >
      <view style="padding-bottom: 20rpx;">
        <view style="margin-top: 20rpx;" class="hj_tip ">温馨提示：该设置仅对本商品生效，若此商品价格发生变更，需重新开启次功能</view>
        <view v-if="configLastUpdated" class="yijiaConfigTime">上次更新时间：{{configLastUpdated | timeformat}}</view>
        <view class="spaceBetween">
          <view class="spaceStart"><text class="yijiaConfigLabel">是否开启自动同意报价功能？</text><view class="yijiaConfigTag spaceCenter">便捷出售</view></view><switch @change="agreeConfigClick" style="transform:scale(0.5)" :checked="yijiaConfigValue.agree" />
        </view>
        <view class="yijiaConfigTitle"><text class="yijiaConfigRed">大于 </text>  该金额的报价，系统将自动为您同意砍价</view>
        <view class="yajiaConfigInput" v-if="yijiaConfigValue.agree">
          <input  v-model="yijiaConfigValue.agreePrice" style="height: 60rpx;" type="number" placeholder="请输入自动同意报价金额" />
          
        </view>
        <view class="yijiaConfigTitle" style="margin-top: 10px;" v-if="yijiaConfigValue.agreePrice">预估到手价<text class="yijiaConfigRed">¥{{shouyiPrice}}</text>（代售费 ¥{{retePriceValue}} ）</view>
        <view class="spaceBetween">
          <view class="spaceStart"><text class="yijiaConfigLabel">是否开启报价防骚扰功能？</text></view><switch @change="preventConfigClick" style="transform:scale(0.5)" :checked="yijiaConfigValue.prevent" />
        </view>
        <view class="yijiaConfigTitle"><text class="yijiaConfigRed">小于 </text>  该金额的报价，系统将自动为您拒绝该议价</view>
        <view  class="yajiaConfigInput" v-if="yijiaConfigValue.prevent">
          <input @input="preventPriceInput" v-model="yijiaConfigValue.preventPrice" style="height: 60rpx;" type="number" :placeholder="'可输入金额：0-'+negoData.offerPrice" />
        </view>
      </view>
    </TipPanel>
     <!-- 同意报价 -->
     <TipPanel
      v-if="agreeWithVisible"
      confirm-text="同意报价"
      cancelText="再考虑一下"
      @cancel="agreeWithVisible = false"
      @confirm="agreeWithClick"
    >
      <view class="hj_title">同意报价</view>
      <view style="color: #000;font-size: 28rpx;" class="text_linTwo">{{confirmItem && confirmItem.productName}}</view>
      <view style="margin: 20rpx 0rpx 20rpx 0rpx;font-size: 24rpx;color: #9a9a9a;">{{confirmItem && confirmItem.productCategoryName}}</view>
      <view class="agreeWithOfferPriceBox">
        <view class="title">买家当前报价:</view>
        <view class="offerPrice">¥{{confirmItem.offerPrice}}</view>
      </view>
      <view style="margin-top: 20rpx;">
        <view style="font-size: 28rpx">
          <span style="color: red;">*</span> 是否同步修改商品售价为  ¥{{confirmItem.offerPrice}}?
        </view>
        <view class="agreeWithOfferRadioBox">
          <radio-group @change="radioChange">
            <label class="uni-list-cell uni-list-cell-pd" v-for="(item, index) in radioItems" :key="item.value">
              <view style="display: flex;align-items: center;">
                <view>
                <radio style="transform: scale(0.7);"  activeBackgroundColor="#ff720c" :value="item.value" :checked="item.value === current" />
              </view>
              <view>{{item.name}}</view>
              </view>
            </label>
          </radio-group>
          <!-- <el-radio v-model="radio" label="1">是的，我要同步<el-tag style="margin-left: 5px;" size="mini" type="danger"
          >更快出售</el-tag
        ></el-radio>
          <el-radio style="margin-top: 5px;" v-model="radio" label="0">暂不同步</el-radio> -->
        </view>
      </view>
    </TipPanel>
  </div>
</template>

<script lang="ts" setup>
import type { TMsgScene } from 'nim-web-sdk-ng/dist/NIM_MINIAPP_SDK/MsgServiceInterface';
import Face from './face.vue';
import VoicePanel from './voice-panel.vue';
import Icon from '../../../components/Icon.vue';
import {
  ref,
  reactive,
  getCurrentInstance,
  computed,
  onUnmounted,
  onMounted,
  defineProps,
  watch
} from '../../../utils/transformVue';
import {
  ALLOW_AT,
  events,
  REPLY_MSG_TYPE_MAP,
  STORAGE_KEY,
} from '../../../utils/constants';
import { emojiMap } from '../../../utils/emoji';
import { t } from '../../../utils/i18n';
import { handleNoPermission } from '../../../utils/permission';
import { customNavigateTo } from '../../../utils/customNavigate';
import type { IMMessage } from '@xkit-yx/im-store';
import MessageOneLine from '../../../components/MessageOneLine.vue';
import {
  isAndroidApp,
  stopAllAudio,
  isIosWeb,
  isWeb,
  isWxApp,
  startCall,
  isApp,
} from '../../../utils';
// @ts-ignore
import UniPopup from '../../../components/uni-components/uni-popup/components/uni-popup/uni-popup.vue';
import MentionMemberList from './mention-member-list.vue';
import { autorun, set } from 'mobx';
import Appellation from '../../../components/Appellation.vue';
import { AT_ALL_ACCOUNT } from '../../../utils/constants';
import type { Team, TeamMember } from '@xkit-yx/im-store';
import { deepClone } from '../../../utils';
import store from '../../../../../store';
import util from '@/utils/util';
import { detailBySn ,negotiaSellerNegoSet} from '@/config/api/kf.js';
import { getProductCategory } from '@/config/api/submitAccount.js';
import {getDetail} from '@/config/api/accountDetail.js'
import request from '@/common/request.js';
import {
  getBuyerList,
  negotiaCancel,
  offerPrice,
  getNegotiaDetail,
  getSellerList,
  sellerDo,
  sellerOfferPrice,
} from '@/config/api/myAssess.js';

type MentionedMember = { account: string; appellation: string };
const props = defineProps({
  flowState: {
    type: Object,
    default() {
      return {};
    },
  },
  scene: {
    type: String,
    required: true,
  },
  to: {
    type: String,
    required: true,
  },
  replyMsgsMap: {
    type: Object,
    default: undefined,
  },
});
watch(() => props.to, (newVal, oldVal) => {  
  getnegoDetail()
})
const inputText = ref('');
const extVisible = ref(false);
// 音频面板flag
const audioPanelVisible = ref(false);
// 发送更多面板flag
const sendMoreVisible = ref(false);
// 表情面板flag
const emojiVisible = ref(false);
// input框flag
const inputVisible = computed(() => {
  if (audioPanelVisible.value || sendMoreVisible.value) {
    return false;
  } else {
    return true;
  }
});
const ishaosan = computed(() => {
  return store?.state?.userInfo?.type == 1 && props.flowState?.type == 1;
});

const startCallKf = () => {
  
  let txtMsg = '已沟通完成请拉人工客服进群进行交易';
  const scene = props.scene;
  const to = props.to;
  let msgBody = {
    scene,
    to,
    body: txtMsg,
  };
  uni.$UIKitNIM.nim.team
    .getTeamMembers({
      teamId: to,
    })
    .then((res) => {
      let findIt = res.find((ele) => ele.type == 'owner');
      const name = uni.$UIKitStore.uiStore.getAppellation({
        account: findIt.account,
      });
      let newTxt = `@${name} ${txtMsg}`;
      const selectedAtMembers = {
        account: findIt.account,
        appellation: name,
      };
      const ext = util.onAtMembersExtHandler(newTxt, [selectedAtMembers]);
      ext.cmdCode = 'recovery_step_1';
      ext.teamId = to;
      msgBody = {
        scene,
        to,
        body: newTxt,
        ext,
      };
      uni.$UIKitStore.msgStore.sendTextMsgActive(msgBody).finally(() => {
        if (isAndroidApp) {
          setTimeout(() => {
            uni.$emit(events.ON_SCROLL_BOTTOM);
          }, 300);
        } else {
          uni.$emit(events.ON_SCROLL_BOTTOM);
        }
      });
    });
};

// 发起呼叫，type: 1 音频呼叫，2 视频呼叫
const handleCall = (type: number) => {
  // @ts-ignore
  const myAccount = uni.$UIKitStore.userStore.myUserInfo.account;
  // @ts-ignore
  const remoteShowName = uni.$UIKitStore.uiStore.getAppellation({
    account: props.to,
  });
  if (myAccount) {
    // @ts-ignore
    startCall({
      remoteUserAccid: props.to,
      currentUserAccid: myAccount,
      type: type,
      remoteShowName: remoteShowName,
    });
  } else {
    uni.showToast({
      title: t('callFailedText'),
      icon: 'none',
    });
  }
};
// 用于解决表情面板和键盘冲突，导致输入框滚动消失问题
const showEmojiInput = ref(false);

const showChangeKF = () => {
  uni.$emit('showChangeKF');
};

const activeKf = () => {
  uni.$emit('activeKf');
};
const applyCard=()=>{
  window.open('https://txwk.10010.com/kcardorder/gotoUid?state=KCARD_WKPEN_COMMOM_ORDERLAND&pageKey=d2d3bbfa7f&channel=02-5546-bq91-9999&ADTAG=02-5546-bq91-9999');
}
const showMyOrder = () => {
  uni.$emit('showMyOrder');
};
// 回复消息相关
const isReplyMsg = ref(false);
const isFocus = ref(false);
const replyMsg = ref<IMMessage>({
  idClient: '',
  from: '',
  // @ts-ignore
  type: '',
  text: '',
  body: '',
  sessionId: '',
});
// @消息相关
const ctx = getCurrentInstance();
const popupRef = ref(null);
const selectedAtMembers = ref<MentionedMember[]>([]);

// 群相关
const team = ref<Team>();
const teamMembers = ref<TeamMember[]>([]);
const teamMute = ref(false);

const isGroupOwner = ref(false);
const isGroupManager = ref(false);

// 是否允许@所有人
const allowAtAll = computed(() => {
  let ext: any = {};
  try {
    ext = JSON.parse((team.value || {}).ext || '{}');
  } catch (error) {
    //
  }
  if (ext[ALLOW_AT] === 'manager') {
    return isGroupOwner.value || isGroupManager.value;
  }
  return true;
});

// 是否是群禁言
// const isTeamMute = computed(() => {
//   console.log(
//     'isGroupOwner, isGroupManager',
//     isGroupOwner.value,
//     isGroupManager.value
//   )

//   if (!teamMute.value) {
//     return false
//   }
//   // 群主或者群管理员在群禁言时，可以发送消息
//   if (isGroupOwner.value || isGroupManager.value) {
//     return false
//   }
//   return true
// })

const isTeamMute = ref(false);

const updateTeamMute = () => {
  if (!teamMute.value) {
    isTeamMute.value = false;
    return;
  }
  // 群主或者群管理员在群禁言时，可以发送消息
  if (isGroupOwner.value || isGroupManager.value) {
    isTeamMute.value = false;
    return;
  }
  isTeamMute.value = true;
  return;
};

const onPopupChange = (e: any) => {
  uni.$emit(events.HANDLE_MOVE_THROUGH, e.value);
};

// 点击@群成员
const handleMentionItemClick = (member: MentionedMember) => {
  // @ts-ignore
  ctx.refs.popupRef.close();
  uni.$emit(events.HANDLE_MOVE_THROUGH, false);
  const nickInTeam = member.appellation;
  selectedAtMembers.value = [
    ...selectedAtMembers.value.filter(
      (item) => item.account !== member.account,
    ),
    member,
  ];
  const newInputText = inputText.value + nickInTeam + ' ';
  // 更新input框的内容
  inputText.value = newInputText;
};

const closePopup = () => {
  // @ts-ignore
  ctx.refs.popupRef.close();
};

// 点击表情输入框，隐藏表情面板，显示键盘
const onClickEmojiInput = () => {
  showEmojiInput.value = false;
  extVisible.value = false;
  emojiVisible.value = false;
  if (isIosWeb) {
    isFocus.value = true;
  } else {
    setTimeout(() => {
      isFocus.value = true;
    }, 500);
  }
};

const handleInputFocus = () => {
  emojiVisible.value = false;
  isFocus.value = true;
  if (isAndroidApp) {
    setTimeout(() => {
      isFocus.value = true;
      uni.$emit(events.ON_SCROLL_BOTTOM);
    }, 300);
  }
  if (isIosWeb) {
    const inputRoot = document.getElementById('msg-input');
    setTimeout(() => {
      inputRoot?.scrollIntoView();
      uni.$emit(events.ON_SCROLL_BOTTOM);
    }, 300);
  }
};

const handleInputBlur = () => {
  isFocus.value = false;
};

// 滚动到底部
const scrollBottom = () => {
  if (isAndroidApp || isWxApp) {
    setTimeout(() => {
      uni.$emit(events.ON_SCROLL_BOTTOM);
    }, 300);
  } else {
    uni.$emit(events.ON_SCROLL_BOTTOM);
  }
};

// 输入框输入事件
const handleInput = (event: any) => {
  const text = event?.detail?.value;
  const isAit = text.endsWith('@') || text.endsWith('@\n');
  if (props.scene == 'team') {
    if (isAit) {
      // 当前输入的是@
      uni.hideKeyboard();
      // @ts-ignore
      ctx.refs.popupRef.open('bottom');
      isFocus.value = false;
      uni.$emit(events.HANDLE_MOVE_THROUGH, true);
    }
  }
};

// 发送文本消息
const handleSendTextMsg = async () => {
  const realText = inputText.value.trim();
  if (realText === '') return;
  const ext = onAtMembersExtHandler();
  // if (props.scene !== 'team' && /^[a-zA-Z][a-zA-Z0-9]*[0-9]$/.test(realText)) {
    if (props.scene ! !== 'team' && /^[0-9a-zA-Z]+$/.test(realText)) {
    const params = {
      productSn: realText,
      noerrorshow: 1,
    };
    const res = await detailBySn(params);
    if (res && res.code == 200 && res.data) {
      const data = res.data;
      const attach = util.createProdcutAttach(data);
      const myAccount = uni.$UIKitStore.userStore.myUserInfo.account;
      uni.$UIKitStore.msgStore
        .sendCustomMsgActive({
          scene: props.scene as TMsgScene,
          to: props.to,
          from: myAccount,
          attach: JSON.stringify(attach),
        })
        .finally(() => {
          if (isAndroidApp) {
            setTimeout(() => {
              uni.$emit(events.ON_SCROLL_BOTTOM);
            }, 300);
          } else {
            uni.$emit(events.ON_SCROLL_BOTTOM);
          }
        });
    } else {
      sendText(inputText, ext);
    }
  } else {
    sendText(inputText, ext);
  }
  inputText.value = '';
  isReplyMsg.value = false;
  selectedAtMembers.value = [];
};

const sendText = (inputText, ext) => {
  // @ts-ignore
  uni.$UIKitStore.msgStore
    .sendTextMsgActive({
      scene: props.scene as TMsgScene,
      to: props.to,
      body: inputText.value,
      ext: selectedAtMembers.value.length && ext,
    })
    .finally(() => {
      if (isAndroidApp) {
        setTimeout(() => {
          uni.$emit(events.ON_SCROLL_BOTTOM);
        }, 300);
      } else {
        uni.$emit(events.ON_SCROLL_BOTTOM);
      }
    });
};

// 移除回复消息
const removeReplyMsg = () => {
  // @ts-ignore
  uni.$UIKitStore.msgStore.removeReplyMsgActive(
    replyMsg?.value?.sessionId as string,
  );
  isReplyMsg.value = false;
};

// 显示表情面板
const handleEmojiVisible = () => {
  if (isTeamMute.value) return;
  if (isWxApp || isAndroidApp) {
    setTimeout(() => {
      emojiVisible.value = true;
      extVisible.value = true;
      uni.$emit(events.ON_SCROLL_BOTTOM);
    }, 300);
  } else {
    emojiVisible.value = true;
    extVisible.value = true;
  }
  showEmojiInput.value = true;
  audioPanelVisible.value = false;
  sendMoreVisible.value = false;
  uni.$emit(events.ON_SCROLL_BOTTOM);
};

// 显示发送更多"+"面板
const handleSendMoreVisible = () => {
  if (isTeamMute.value) return;
  audioPanelVisible.value = false;
  emojiVisible.value = false;
  sendMoreVisible.value = !sendMoreVisible.value;
  setTimeout(() => {
    uni.$emit(events.ON_SCROLL_BOTTOM);
  }, 300);
};

// 点击表情
const handleEmoji = (emoji: { key: string; type: string }) => {
  inputText.value += emoji.key;
};

// 删除表情
const handleEmojiDelete = () => {
  let target = '';
  const isEmojiEnd = Object.keys(emojiMap).reduce((prev, cur) => {
    const isEnd = inputText.value.endsWith(cur);
    if (isEnd) {
      target = cur;
    }
    return prev || isEnd;
  }, false);
  if (isEmojiEnd && target) {
    inputText.value = inputText.value.replace(target, '');
  } else {
    inputText.value = inputText.value.slice(0, -1);
  }
};

// 显示语音面板
const handleAudioVisible = () => {
  if (isTeamMute.value) return;
  audioPanelVisible.value = !audioPanelVisible.value;
  emojiVisible.value = false;
  setTimeout(() => {
    uni.$emit(events.ON_SCROLL_BOTTOM);
  }, 300);
};

// 发送图片消息
const handleSendImageMsg = () => {
  if (isTeamMute.value) return;
  stopAllAudio();
  uni.chooseImage({
    count: 6,
    sizeType: ['compressed'],
    success: (res) => {
      const promises = res.tempFilePaths.map((item) => {
        return uni.$UIKitStore.msgStore
          .sendImageMsgActive({
            scene: props.scene as TMsgScene,
            to: props.to,
            filePath: item,
          })
          .then(() => {
            // 这里不需要在每个成功时都调用 scrollBottom()，因为我们会在所有完成后调用
          })
          .catch((error) => {
            // 可以在这里处理每个发送失败的错误，但不影响其他图片的发送
            uni.showToast({
              icon: 'error',
              title: t('sendImageFailedText'),
            });
          });
      });
      Promise.all(promises)
        .then(() => {
          // 所有图片发送完成后调用 scrollBottom()
          scrollBottom();
        })
        .catch((error) => {
          scrollBottom();
        });
    },
    //没有开启权限时，提示开启权限
    complete: handleNoPermission,
  });
};

// 发送视频消息（使用相机或者从相册选择）
const handleSendVideoMsg = (type: string, event: any) => {
  if (isTeamMute.value) return;
  stopAllAudio();
  // 这里做一层拦截的原因是，微信小程序在input聚焦的时候点击+号按钮，会触发此函数执行，阻止冒泡也无法解决该问题，疑为uniapp编译问题
  if (isWxApp && event?.type == 'blur') {
    return;
  }

  uni.chooseVideo({
    sourceType: [type],
    compressed: true,
    maxDuration: 60,
    success: (res) => {
      // @ts-ignore
      uni.$UIKitStore.msgStore
        .sendVideoMsgActive({
          scene: props.scene as TMsgScene,
          to: props.to,
          filePath: res.tempFilePath,
          onUploadStart: () => {
            scrollBottom();
          },
        })
        .then(() => {
          scrollBottom();
        })
        .catch(() => {
          scrollBottom();
          uni.showToast({
            icon: 'error',
            title: t('sendVideoFailedText'),
          });
        });
    },
    //没有开启权限时，提示开启权限
    complete: handleNoPermission,
  });
};

// 发送语音消息
const handleSendAudioMsg = (filePath: string, duration: number) => {
  // @ts-ignore
  uni.$UIKitStore.msgStore
    .sendAudioMsgActive({
      scene: props.scene,
      to: props.to,
      filePath,
      duration,
      onUploadStart: () => {
        scrollBottom();
      },
    })
    .catch(() => {
      uni.showToast({
        icon: 'error',
        title: t('sendAudioFailedText'),
      });
      scrollBottom();
    });
};

// 跳转设置页
const handleSetting = () => {
  if (props.scene === 'p2p') {
    customNavigateTo({
      url: `/pages/Chat/message/p2p-set?id=${props.to}`,
    });
  } else if (props.scene === 'team') {
    customNavigateTo({
      url: `/pages/Group/group-set/index?id=${props.to}`,
    });
  }
};

let uninstallTeamWatch = () => {};
const identities=ref('')
const customTTP=ref('')
const cardIdentity=ref('')
const negoId=ref('')
const negoData=ref({})
const showHjPop=ref(false)
const current=ref('0')
const currentPrice=ref('1')
const counterPrice=ref('')
const radioItems=ref([{ value: '1',
                    name: '是的，我要同步',
                  },
                  { value: '0',
                    name: '暂不同步',
                    checked: 'true'
                  }])
const agreeWithVisible=ref(false)
const formDataJson= ref([
        {
          // 还价表单配置
          name: 'price',
          type: 'input',
          placeholder: '请输入价格',
          label: '价格',
          required: true,
          itemProps: {
            type: 'number',
          },
          rules: [
            {
              required: true,
              errorMessage: '请输入新还价',
            },
          ],
        },
      ])
const radioChange=(e)=>{
      current.value=e.detail.value
}
const counterPriceSureChange=(e,v)=>{
  counterPrice.value=v
}
const getnegoDetail=()=>{
  
 
    try {
        const team = uni.$UIKitStore.teamStore.teams.get(props.to);
        const myUser = uni.$UIKitStore.userStore.myUserInfo;
        if (!team) {
          throw new Error('Team not found');
        }
        const { serverExt = '{}' } = team;
        const serverCustomJson = JSON.parse(serverExt);  

        const { sim, bim } =serverCustomJson
        const account= myUser.account
        if(serverCustomJson){
          customTTP.value=serverCustomJson.ttp
        }
        if (account == sim) {
            cardIdentity.value='seller'
          } else if (account == bim) {
            cardIdentity.value = 'purchaser';
          }
        if (serverCustomJson.ttp && serverCustomJson.ttp == 7) {
          if (account == sim) {
            identities.value='seller'
          } else if (account == bim) {
            identities.value = 'purchaser';
          }
          getNegotiaDetailData()
        } else {
          identities.value=''
         negoId.value = '';
        }
      } catch (error) {
        identities.value=''
        negoId.value = '';
      }

}

onMounted(() => {
  
  uninstallTeamWatch = autorun(() => {

    if (props.scene === 'team') {
     
      // @ts-ignore
      const team = deepClone(uni.$UIKitStore.teamStore.teams.get(props.to));
      teamMembers.value = deepClone(
        // @ts-ignore
        uni.$UIKitStore.teamMemberStore.getTeamMember(props.to),
      );
      // @ts-ignore
      const myUser = uni.$UIKitStore.userStore.myUserInfo;
      isGroupOwner.value = team?.owner == myUser.account;
      getnegoDetail()
      
 

      isGroupManager.value = teamMembers.value
        .filter((item) => item.type === 'manager')
        .some((member) => member.account === (myUser ? myUser.account : ''));
      team.value = team;
      teamMute.value = team.mute;
      updateTeamMute();
    }
  });

  // 撤回后，重新编辑消息
  uni.$on(events.ON_REEDIT_MSG, (msg: IMMessage) => {
    const _replyMsg = props.replyMsgsMap?.[msg.idClient];
    // 如果重新编辑的是回复消息，则需要将回复消息展示在输入框上方
    if (_replyMsg?.idClient) {
      // @ts-ignore
      _replyMsg && uni.$UIKitStore.msgStore.replyMsgActive(_replyMsg);
      replyMsg.value = _replyMsg;
      isReplyMsg.value = true;
    }
    // 如果重新编辑的是@消息，则需要将被@的成员重新加入selectedAtMembers
    if (msg.ext) {
      const extObj = JSON.parse(msg.ext);
      const yxAitMsg = extObj.yxAitMsg;
      if (yxAitMsg) {
        const _mentionedMembers: MentionedMember[] = [];
        Object.keys(yxAitMsg).forEach((key) => {
          if (key == AT_ALL_ACCOUNT) {
            _mentionedMembers.push({
              account: key,
              appellation: '所有人',
            });
          } else {
            _mentionedMembers.push({
              account: key,
              // @ts-ignore
              appellation: uni.$UIKitStore.uiStore.getAppellation({
                account: key,
                teamId: props.to,
                ignoreAlias: true,
              }),
            });
          }
        });
        selectedAtMembers.value = _mentionedMembers;
      }
    }
    inputText.value = msg?.attach?.oldBody;
    isFocus.value = true;
  });

  uni.$on(events.REPLY_MSG, (msg: IMMessage) => {
    isReplyMsg.value = true;
    isFocus.value = true;
    replyMsg.value = msg;
  });

  uni.$on(events.AIT_TEAM_MEMBER, (member) => {
    selectedAtMembers.value = [
      ...selectedAtMembers.value.filter(
        (item) => item.account !== member.account,
      ),
      member,
    ];
    const newInputText = inputText.value + '@' + member.appellation + ' ';
    // 更新input框的内容
    inputText.value = newInputText;
  });

  // 关闭表情、语音、发送更多面板
  uni.$on(events.CLOSE_PANEL, () => {
    emojiVisible.value = false;
    extVisible.value = false;
    audioPanelVisible.value = false;
    sendMoreVisible.value = false;
  });

  // @消息 @群成员
  uni.$on(events.HANDLE_AIT_MEMBER, (member) => {
    handleMentionItemClick(member);
  });

  // 关闭@群成员面板
  uni.$on(events.CLOSE_AIT_POPUP, () => {
    closePopup();
  });

  // 表情点击
  uni.$on(events.EMOJI_CLICK, (emoji) => {
    handleEmoji(emoji);
  });

  // 表情删除
  uni.$on(events.EMOJI_DELETE, () => {
    handleEmojiDelete();
  });

  // 表情发送
  uni.$on(events.EMOJI_SEND, () => {
    emojiVisible.value = false;
    extVisible.value = false;
    handleSendTextMsg();
  });

  if (uni.onKeyboardHeightChange) {
    uni.onKeyboardHeightChange((res) => {
      const isAndroidWxapp =
        uni.getSystemInfoSync().platform == 'android' && isWxApp;
      // 此处是为了点击安卓键盘上的收起按钮时，表情面板需要隐藏
      if (
        (res.height === 0 && isAndroidApp) ||
        (res.height === 0 && isAndroidWxapp)
      ) {
        emojiVisible.value = false;
        extVisible.value = false;
      }
    });
  }
});

const hjForm = ref(null);
const showConfirmPop =ref(false)
const confirmPopInfo=ref({})
const confirmItem=ref({})
const confirmActive=reactive({
        'cancelConfirm': {
          name: '取消议价',
          func: (i) => doCancel(i),
          text: '您确定取消吗？取消后不可撤销',
        },
        'agreeAssessConfirm': {
          name: '确认报价',
          func: (i) => agreeAssessFun(i),
          text: '您确定同意当前报价吗？确定后不可撤销',
        },
        'disAgreeAssessConfirm': {
          name: '拒绝报价',
          func: (i) => disAgreeAssessFun(i),
          text: '您确定拒绝当前报价吗？确定后不可撤销',
        },
      })
const submitHjForm=()=> {
  if (hjForm.value) {
    hjForm.value.submit();
  }
}
 // 立即购买
 const palyPage=(date)=> {
      uni.navigateTo({
        url: `/pages/confirmOrder/confirmOrder?negoId=${date.id}&from=myAssess&productCategoryId=${date.productCategoryId}`,
      });
    }
  const  counterRadioChange=(e)=>{
      currentPrice.value=e.detail.value
    }
// 确认还价
const counterPriceSure=(formData)=>{
      uni.showLoading({
        title: '拼命加载中',
      });
      if(identities.value=='seller'){
        sellerOfferPrice({
        negoId: negoId.value,
        ...formData,
        changePrice:currentPrice.value=='1'?1:0,
      }).then((res) => {
        uni.hideLoading();
        
        if (res.code == 200) {
          showHjPop.value = false;
          getNegotiaDetailData()
          uni.showToast({
            title: '议价回复成功',
            icon: 'none',
          });
        }
      });
      }else{

      offerPrice({
        negoId: negoId.value,
        ...formData
      }).then((res) => {
        uni.hideLoading();
        if (res.code == 200) {
          showHjPop.value = false;
          getNegotiaDetailData()
          uni.showToast({
            title: '议价回复成功',
            icon: 'none',
          });
        }
      })
      }
     
    }
const getBtns=(item) =>{
      const { status, sellerOfferPrice } = item;
      const btns = [
        {
          name: '取消议价',
          func: (i) => openConfirm('cancelConfirm', i),
          show: status == 1 || (status == 2 && sellerOfferPrice > 0),
          class: 'kk-btn line',
        },
        {
          name: '拒绝',
          func: (i) => openConfirm('disAgreeAssessConfirm', i),
          show: status == 0,
          class: 'kk-btn line',
        },
        {
          name: '同意',
          func: (i) => openConfirm('agreeAssessConfirm', i,'agreeWith'),
          show: status == 0,
          class: 'kk-btn line',
        },
        {
          name: '还价',
          func: (i) => openHjPop(i),
          show: status == 0,
          class: 'kk-btn line',
        },
        {
          name: '议价设置',
          func: (i) => yijiaConfigClick(i),
          show: status != 3,
          class: 'kk-btn line',
        }
      ];
      // return btns
      return btns.filter((item) => item.show);
    }
    const yijiaVisible= ref(false)
    const retePriceValue=ref('')
    const configLastUpdated=ref('')
    const productPrice=ref('')
    const shouyiPrice=ref('')
    const feeRate=reactive({})
    const yijiaConfigValue=reactive({
        agree: false,
        agreePrice: '',
        prevent: false,
        preventPrice: '',
      },)
    const preventPriceInput=(e)=>{
    if(e.detail.value>negoData.value.offerPrice){
        setTimeout(()=>{
          yijiaConfigValue.preventPrice=negoData.value.offerPrice
        })
      }
    }
    watch(
      () => yijiaConfigValue.agreePrice, // 用函数返回要监听的值
      (newVal, oldVal) => {
        ratePrice(yijiaConfigValue.agreePrice)
      }
    );
    
    const ratePrice=(price)=>{
      console.log(price);
      
      retePriceValue.value = util.times(price, feeRate.rate);
      if (retePriceValue.value < feeRate.minPrice) {
          retePriceValue.value = feeRate.minPrice
      }
      shouyiPrice.value = util.minus(price, retePriceValue.value);
    }
    const agreeConfigClick=(e)=>{
      if (!e.detail.value) {
        yijiaConfigValue.agreePrice = '';
      }
      yijiaConfigValue.agree=e.detail.value
    }
    const preventConfigClick=(e)=>{
      if (!e.detail.value) {
        yijiaConfigValue.preventPrice = '';
      }
      yijiaConfigValue.prevent=e.detail.value
    }
    const yijiaConfigClick= async ()=>{
      // getProductCategory(negoData.value.productCategoryId).then(res=>{
      //   if(res.code==200){
      //     let custom = JSON.parse(res.data.custom);
      //     Object.assign(feeRate, custom.feeRate)
      //     yijiaVisible.value=true
      //   }
      // })
      const [feeRateRes, productDetail] = await Promise.all([
        getProductCategory(negoData.value.productCategoryId),
        getDetail(negoData.value.productId)
      ]);
      if(feeRateRes){
        let custom = JSON.parse(feeRateRes.data.custom);
        Object.assign(feeRate, custom.feeRate)
        yijiaVisible.value=true
      }
      if(productDetail){
        const {product,productAttributeValueList}=productDetail.data
        productPrice.value=product.price
        let config=productAttributeValueList.filter(item=>{
          return item.productAttributeName=='议价黑名单'
        })
        if(config){
          let configValue=JSON.parse(config[0].value)

          const {antiHarassment,autoAccept,lastUpdated}=configValue
          if(lastUpdated){
            configLastUpdated.value=lastUpdated
          }
         let obj= {
            agree: autoAccept.enable,
            agreePrice: autoAccept.thresholdAmount,
            prevent: antiHarassment.enable,
            preventPrice:antiHarassment.blockThresholdAmount
          }
          Object.assign(yijiaConfigValue, obj)
          console.log(yijiaConfigValue,898989);
          
        }else{
          configLastUpdated.value = '';
          let obj= {
              agree: false,
              agreePrice: '',
              prevent: false,
              preventPrice: ''
          };
          Object.assign(yijiaConfigValue, obj)
        }
       
        
      }
    }
    const yijiaConfigSubmit=()=>{
      const {agree,agreePrice,prevent,preventPrice}=yijiaConfigValue
      if (agreePrice >productPrice.value) {
        uni.showToast({
              title: `自动同意金额须低于${productPrice.value}元`,
              icon: 'none',
            });
        return
      }
      if((agree&&!agreePrice)||(prevent&&!preventPrice)){
        uni.showToast({
              title: '请完善报价配置',
              icon: 'none',
        });
        return
      }
      let obj={
          autoAccept:{
            enable:agree,
            thresholdAmount:agreePrice,
          },
          antiHarassment:{
            enable:prevent,
            blockThresholdAmount:preventPrice,
          }
          
      }
      let params={productId:negoData.value.productId,quoteSettings:JSON.stringify(obj)}
      negotiaSellerNegoSet(params).then(res=>{
        if(res.code==200){
          yijiaVisible.value=false
          uni.showToast({
              title: '设置成功！',
              icon: 'none',
            });
        }
      })  
    }
    const openHjPop=(date)=> {
      counterPrice.value=''
      if(identities.value=='seller'){
        currentPrice.value='1'
      }else{
        currentPrice.value='0'
      }
     
      showHjPop.value = true;
    }
       // 打开二次弹窗
    const openConfirm=(flag, item,agreeWith)=> {
      confirmItem.value = item;
      if(agreeWith=='agreeWith'){
        current.value='0'
        agreeWithVisible.value=true
        return
      }
      confirmPopInfo.value =confirmActive[flag];
      
     
      showConfirmPop.value = true;
    }
     // 二次弹窗确认操作
    const active=()=> {
      showConfirmPop.value = false;
      confirmPopInfo.value.func && confirmPopInfo.value.func(confirmItem.value);
    }
    const doCancel=(item)=> {
      agreeResult(item, 2, 2);
    }
       // 确认报价
    const agreeAssessFun=(date)=> {
      agreeResult(date, 1, 1);
    }
   const agreeWithClick=()=>{
      agreeResult(confirmItem.value, 1, 1,current.value==1?true:false);
    }
    // 拒绝报价
    const disAgreeAssessFun=(date)=>{
      agreeResult(date, 2, 0);
    }
   const agreeResult=(date, num, rejectedState,agreeWithFlag)=> {
      uni.showLoading({
        title: '拼命加载中',
      });
      let data = {
        negoId: date.id,
        status: num,
      };
      if(agreeWithFlag){
        data.changePrice=1
      }
      if (rejectedState) {
        data.rejectedState = rejectedState;
      }
      sellerDo(data).then((res) => {
        uni.hideLoading();
        if (res.code == 200) {
          agreeWithVisible.value=false
          getNegotiaDetailData();
        }
      });
    }

const getNegotiaDetailData=async ()=>{
  if(identities.value){
    const res=await getNegotiaDetail(props.to)
    
    if(res.code===200){
      negoId.value=res.data.id
      negoData.value=res.data
    }
  }

}
defineExpose({
  getNegotiaDetailData,
  getnegoDetail
});
const onAtMembersExtHandler = () => {
  let ext: any;
  if (selectedAtMembers.value.length) {
    selectedAtMembers.value
      .filter((member) => {
        if (!allowAtAll.value && member.account === AT_ALL_ACCOUNT) {
          return false;
        }
        return true;
      })
      .forEach((member) => {
        // @ts-ignore
        const substr = `@${member.appellation}`;
        const positions: number[] = [];
        let pos = inputText.value?.indexOf(substr);
        while (pos !== -1) {
          positions.push(pos);
          pos = inputText.value?.indexOf(substr, pos + 1);
        }
        if (positions.length) {
          if (!ext) {
            ext = {
              yxAitMsg: {
                [member.account]: {
                  text: substr,
                  segments: [],
                },
              },
            };
          } else {
            ext.yxAitMsg[member.account] = {
              text: substr,
              segments: [],
            };
          }
          positions.forEach((position) => {
            const start = position;
            ext.yxAitMsg[member.account].segments.push({
              start,
              end: start + substr.length,
              broken: false,
            });
          });
        }
      });
  }
  return ext;
};

onUnmounted(() => {
  uni.$off(events.REPLY_MSG);
  uni.$off(events.ON_REEDIT_MSG);
  uni.$off(events.REPLY_MSG);
  uni.$off(events.AIT_TEAM_MEMBER);
  // 关闭表情面板
  uni.$off(events.CLOSE_PANEL);
  // @消息 @群成员
  uni.$off(events.HANDLE_AIT_MEMBER);
  // 关闭@群成员面板
  uni.$off(events.CLOSE_AIT_POPUP);
  // 表情点击
  uni.$off(events.EMOJI_CLICK);
  // 表情删除
  uni.$off(events.EMOJI_DELETE);
  // 表情发送
  uni.$off(events.EMOJI_SEND);
  removeReplyMsg();
  uninstallTeamWatch();
});
</script>

<style scoped lang="scss">
@import '../../styles/common.scss';

.tool_bar {
  margin-right: 3px;
  .bar_item {
    font-size: 12px;
    height: 28px;
    line-height: 28px;
    background: #fff;
    border-radius: 14px;
    padding: 0 7px;
    border: 1px solid #fff;
  }
  .active {
    border-color: #e60f0f;
    color: #e60f0f;
  }
}

.input-root {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: auto;
  background-color: #f6f6f6;
  max-height: 300px;
}

.input-root-h5 {
  height: auto;
  position: relative;
  order: 1;
}

.msg-input-wrapper {
  width: 100%;
  height: 100%;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  z-index: 88;
}

.msg-input {
  overflow-x: hidden;
  padding:0 20px 20px;
  background-color: #fcfcfc;

  &-input {
    height: 40px;
    font-size: 14px;
    padding: 0 20px;
    margin-bottom: 5px;

    border-radius: 20px;
    background: #F2F2F2;
    box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);

    &::placeholder {
      padding: 0 20px;
    }
  }
}

.msg-button-group {
  padding: 10px 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  background: #fff;
}

.msg-input-button {
  flex: 1;
  text-align: right;
  // &:not(:last-child) {
  //   margin-right: 60px;
  // }

  &.msg-input-loading {
    animation: loadingCircle 1s infinite linear;
    z-index: 1;
    width: 20px;
    height: 20px;
    margin-top: 4px;

    .loading {
      width: 100%;
      height: 100%;
    }
  }
}

.msg-ext {
  overflow-y: auto;
  width: 100%;
  height: 300px;
  background-color: #eff1f3;
  z-index: 1;
}

.msg-emoji-panel {
  overflow-y: auto;
  width: 100%;
  height: 246px;
  background-color: #eff1f3;
  z-index: 1;
}

.msg-audio-panel {
  overflow-y: hidden;
  width: 100%;
  height: 300px;
  background-color: #eff1f3;
  z-index: 1;
}

.send-more-panel {
  display: flex;
  padding: 15px;
  overflow-y: hidden;
  width: 100%;
  height: 300px;
  background-color: #eff1f3;
  z-index: 1;
}

.reply-message-wrapper {
  display: flex;
  font-size: 13px;
  background-color: #eff1f2;
  height: 25px;
  padding-top: 6px;
  align-items: center;
  color: #929299;

  .reply-noFind {
    width: fit-content;
  }

  .reply-message-close {
    flex-basis: 14px;
    margin-left: 10px;
    display: flex;
    align-items: center;
  }

  .reply-message {
    display: flex;
    align-items: center;
    flex-basis: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .reply-title {
    flex-basis: 30px;
    white-space: nowrap;
    margin-right: 5px;
  }

  .reply-to {
    flex-basis: content;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 13px;
  }
}

.input-emoji {
  background-color: #fff;
  height: 40px;
  line-height: 40px;
  font-size: 16px;
  padding: 0 12px;
  border-radius: 6px;
}

.input-text {
  white-space: nowrap;
}

.input-placeholder {
  background-color: transparent;
  height: 40px;
  line-height: 40px;
  box-sizing: border-box;

  color: #C0C0C0;
  font-family: "Noto Sans CJK SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.48px;
}

.send-more-panel-item-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  .send-more-panel-item {
    background-color: #fff;
    border-radius: 8px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    margin: 0 12px;
    justify-content: center;
  }
  .icon-text {
    font-size: 12px;
    color: #747475;
    margin-top: 8px;
  }
}
.hj_title {
  color: #1b1b1b;
  font-family: YouSheBiaoTiHei;
  font-size: 44rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 32rpx;
  text-align: center;

  margin: 36rpx 0 26rpx;
}
.hj_tip {
  margin: 0 10rpx 34rpx 10rpx;

  color: #1b1b1b;
  font-size: 20rpx;
  font-weight: 400;
  line-height: 36rpx;

  padding: 8rpx 56rpx;
  text-align: center;
  border-radius: 16rpx;
  background: linear-gradient(
    190deg,
    #fff5ed 3.42%,
    #ffe1c6 52.35%,
    #ffebd9 78.22%,
    #ffebd9 114.78%
  );
}
.nogoStatusText{
  background: #ecf5ff;height: 50rpx;display: flex;align-items: center;padding: 0px 20rpx;border-radius: 12rpx;margin-left: 10rpx;
  color: #409EFF;
  font-size: 24rpx;
}
.yijiaConfigTag{
  width: 104rpx;
  height: 36rpx;
  border: 2rpx solid red;
  border-radius: 10rpx;
  font-size: 24rpx;
  color: red;
}
.yijiaConfigLabel{
  color:#333;
  font-size: 28rpx;
  font-weight: 500;
}
.yijiaConfigTitle{
  font-size: 20rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.yajiaConfigInput{
  background: #f7f7f7;border-radius: 20rpx;height: 60rpx;
  /deep/.uni-input-input{
    font-size: 24rpx;
    text-indent: 10rpx;
  }
  /deep/ .input-placeholder{
    font-size: 24rpx;
    text-indent: 10rpx;
  }
}
.yijiaConfigRed{
  color: red;
}
.yijiaConfigTime{
  margin-top: -10rpx;font-size: 24rpx;
}
.agreeWithOfferPriceBox{
  width: 100%;
  height: 80rpx;
  border-radius: 20rpx;
  background: #f7f7f7;
  display: flex;
  align-items: center;
  font-family: PingFang Sc;
  font-size: 28rpx;
  padding-left: 20rpx;
  color: #000;
  .title{
  }
  .offerPrice{
    color: red;
  }
}
.agreeWithOfferRadioBox{
  display: flex;
  flex-direction: column;
  margin-top: 10rpx;
  font-size: 28rpx;
  padding-bottom: 20rpx;
}
</style>
