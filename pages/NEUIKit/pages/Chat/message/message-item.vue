<template>
  <div
    :id="MSG_ID_FLAG + msg.idClient"
    :key="msg.time"
    class="msg-item-wrapper"
  >
    <div
      v-if="msg.type === 'custom' && msg.attach.type === 'kk_product_msg_fed'"
      :style="{
        flexDirection: !isSelf ? 'row' : 'row-reverse',
      }"
      class="msg-common"
    >
      <Avatar
        :account="msg.from"
        :team-id="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <div class="mgr8">
        <div v-if="!isSelf" class="msg-name">
          {{ appellation }}<span v-if="gf" :class="gfclazz">{{ gf }}</span>
        </div>
        <div class="kk_custom_msg_wrapper_card">
          <div v-if="msg.attach.body.title" class="kk_custom_msg_title oneline">
            {{ msg.attach.body.title }}
          </div>
          <div @click="goProduct(msg.attach.data)">
            <mp-html
              :show-img-menu="false"
              :lazy-load="false"
              :content="msg.attach.body.content"
              class="kk_custom_msg_content"
            />
          </div>
          <div
            v-if="
              msg.attach &&
              msg.attach.body &&
              msg.attach.body.type4List &&
              msg.attach.body.type4List.length
            "
            class="kk_custom_msg_question"
          >
            常用问题
          </div>
          <div
            v-for="(item, idx) in msg.attach.body.type4List"
            :key="idx"
            class="kk_custom_msg_question"
            @click="handleQuesClick(item, msg.attach.data, msg)"
          >
            {{ idx + 1 }}、{{ item }}
          </div>
          <div
            class="kk_custom_msg_question_red"
            @click="handleBaojiaClick(msg.attach.data, msg)"
          >
            {{ msg.attach.body.type4List.length+1 }}、我要议价
          </div>
        </div>
      </div>
    </div>
    <div
      v-else-if="
        msg.type === 'custom' && msg.attach.type === 'kk_order_msg_fed'
      "
      :style="{
        flexDirection: !isSelf ? 'row' : 'row-reverse',
      }"
      class="msg-common"
    >
      <Avatar
        :account="msg.from"
        :team-id="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <div class="mgr8">
        <div v-if="!isSelf" class="msg-name">
          {{ appellation }}<span v-if="gf" :class="gfclazz">{{ gf }}</span>
        </div>
        <div class="kk_custom_msg_wrapper_card">
          <div v-if="msg.attach.body.title" class="kk_custom_msg_title oneline">
            {{ msg.attach.body.title }}
          </div>
          <div @click="goOrder(msg.attach.data)">
            <mp-html
              :show-img-menu="false"
              :lazy-load="false"
              :content="msg.attach.body.content"
              class="kk_custom_msg_content"
            />
          </div>
          <!-- <div
            v-if="msg.attach.body.action"
            class="kk_custom_msg_action"
            @click="goOrder(msg.attach.body.orderId)"
          >
            {{ msg.attach.body.action.name }}
          </div> -->
        </div>
      </div>
    </div>
    <div
      v-else-if="msg.type === 'custom' && msg.attach.type === 'kkmsg_action2'"
      :style="{
        flexDirection: !isSelf ? 'row' : 'row-reverse',
      }"
      class="msg-common"
    >
      <Avatar
        :account="msg.from"
        :team-id="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <div class="mgr8">
        <div v-if="!isSelf" class="msg-name">
          {{ appellation }}<span v-if="gf" :class="gfclazz">{{ gf }}</span>
        </div>
        <div class="kk_custom_msg_wrapper_card">
          <mp-html
            v-if="msg.attach.body.title"
            :content="msg.attach.body.title"
            :show-img-menu="false"
            :lazy-load="false"
            class="kk_custom_msg_title"
          />
          <mp-html
            :show-img-menu="false"
            :lazy-load="false"
            :content="msg.attach.body.content"
            class="kk_custom_msg_content"
            @linktap="handleLinkTap"
          />
          <div class="actions_box">
            <div
              v-for="(item, index) in msg.attach.body.action || []"
              v-if="canShow(item)"
              :key="index"
              class="kk_custom_msg_action"
              @click="()=>handleClick(item, msg.attach.data)"
            >
              <span> {{ item.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-else-if="msg.type === 'custom' && msg.attach.type === 'kkmsg_action'"
      :style="{
        flexDirection: !isSelf ? 'row' : 'row-reverse',
      }"
      class="msg-common"
    >
      <Avatar
        :account="msg.from"
        :team-id="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <div class="mgr8">
        <div v-if="!isSelf" class="msg-name">
          {{ appellation }}<span v-if="gf" :class="gfclazz">{{ gf }}</span>
        </div>
        <div
          :class="msg.attach.body.title ? 'card-box' : ''"
          class="kk_custom_msg_wrapper_card"
        >
          <mp-html
            v-if="msg.attach.body.title"
            :content="msg.attach.body.title"
            :show-img-menu="false"
            :lazy-load="false"
            class="kk_custom_msg_title"
          />
          <mp-html
            :show-img-menu="false"
            :lazy-load="false"
            :content="msg.attach.body.content"
            class="kk_custom_msg_content"
            @linktap="handleLinkTap"
          />
          <div class="actions_box">
            <div v-if="msg.attach.body.action2">
              <div
                v-for="(item, index) in msg.attach.body.action2 || []"
                v-if="canShow(item)"
                :key="index"
                class="kk-btn line-blue"
                @click="()=>handleClick(item, msg.attach.data, 'action2')"
              >
                <span> {{ item.name }}</span>
              </div>
            </div>
            <div v-else>
              <div
                v-for="(item, index) in msg.attach.body.action || []"
                v-if="canShow(item)"
                :key="index"
                class="kk-btn line-blue"
                @click="()=>handleClick(item, msg.attach.data)"
              >
                <span> {{ item.name }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-else-if="msg.type === 'custom' && msg.attach.type === 'time'"
      class="msg-time"
    >
      {{ msg.attach.value }}
    </div>
    <div
      v-else-if="
        msg.type === 'custom' &&
        msg.attach.type === 'reCallMsg' &&
        msg.attach.canEdit
      "
      :style="{
        flexDirection: !isSelf ? 'row' : 'row-reverse',
      }"
      class="msg-common"
    >
      <Avatar
        :account="msg.from"
        :team-id="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <MessageBubble :msg="msg" :bg-visible="true">
        {{ t('recall2') }}
        <text
          class="msg-recall-btn"
          @tap="
            () => {
              handleReeditMsg(msg);
            }
          "
        >
          {{ t('reeditText') }}
        </text>
      </MessageBubble>
    </div>
    <div
      v-else-if="
        msg.type === 'custom' &&
        msg.attach.type === 'reCallMsg' &&
        !msg.attach.canEdit
      "
      :style="{ flexDirection: !isSelf ? 'row' : 'row-reverse' }"
      class="msg-common"
    >
      <Avatar
        :account="msg.from"
        :team-id="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <MessageBubble :msg="msg" :bg-visible="true">
        <div class="recall-text">{{ t('you') + t('recall') }}</div>
      </MessageBubble>
    </div>
    <div
      v-else-if="msg.type === 'custom' && msg.attach.type === 'beReCallMsg'"
      :style="{ flexDirection: !isSelf ? 'row' : 'row-reverse' }"
      class="msg-common"
    >
      <Avatar
        :account="msg.from"
        :team-id="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <div class="msg-content">
        <div v-if="!isSelf" class="msg-name">
          {{ appellation }}<span v-if="gf" :class="gfclazz">{{ gf }}</span>
        </div>
        <div :class="isSelf ? 'self-msg-recall' : 'msg-recall'">
          <text class="msg-recall2">
            {{ !isSelf ? t('recall2') : `${t('you') + t('recall')}` }}</text
          >
        </div>
      </div>
    </div>
    <div
      v-else-if="msg.type === 'text'"
      :style="{ flexDirection: !isSelf ? 'row' : 'row-reverse' }"
      class="msg-common"
    >
      <Avatar
        :account="msg.from"
        :team-id="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <div class="msg-content">
        <div v-if="!isSelf" class="msg-name">
          {{ appellation }}<span v-if="gf" :class="gfclazz">{{ gf }}</span>
        </div>
        <MessageBubble :msg="msg" :tooltip-visible="true" :bg-visible="true">
          <ReplyMessage
            v-if="replyMsg.idClient"
            :reply-msg="replyMsg"
          ></ReplyMessage>
          <MessageText :msg="msg"></MessageText>
        </MessageBubble>
      </div>
    </div>
    <div
      v-else-if="msg.type === 'image'"
      :style="{ flexDirection: !isSelf ? 'row' : 'row-reverse' }"
      class="msg-common"
    >
      <Avatar
        :account="msg.from"
        :team-id="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <div class="msg-content">
        <div v-if="!isSelf" class="msg-name">
          {{ appellation }}<span v-if="gf" :class="gfclazz">{{ gf }}</span>
        </div>
        <MessageBubble
          :msg="msg"
          :tooltip-visible="true"
          :bg-visible="true"
          style="cursor: pointer"
        >
          <div
            @tap="
              () => {
                handleImageTouch(msg.attach.url);
              }
            "
          >
            <image
              :lazy-load="true"
              :src="imageUrl"
              class="msg-image"
              mode="aspectFill"
            ></image>
          </div>
        </MessageBubble>
      </div>
    </div>
    <div
      v-else-if="msg.type === 'video'"
      :style="{ flexDirection: !isSelf ? 'row' : 'row-reverse' }"
      class="msg-common"
    >
      <Avatar
        :account="msg.from"
        :team-id="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <div class="msg-content">
        <div v-if="!isSelf" class="msg-name">
          {{ appellation }}<span v-if="gf" :class="gfclazz">{{ gf }}</span>
        </div>
        <MessageBubble
          :msg="msg"
          :tooltip-visible="true"
          :bg-visible="true"
          style="cursor: pointer"
        >
          <div class="video-msg-wrapper" @tap="() => handleVideoTouch(msg)">
            <div class="video-play-button">
              <div class="video-play-icon"></div>
            </div>
            <image
              :lazy-load="true"
              :src="videoFirstFrameDataUrl"
              class="msg-image"
              mode="aspectFill"
            ></image>
          </div>
        </MessageBubble>
      </div>
    </div>
    <div
      v-else-if="msg.type === 'g2'"
      :style="{ flexDirection: !isSelf ? 'row' : 'row-reverse' }"
      class="msg-common"
    >
      <Avatar
        :account="msg.from"
        :team-id="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <div class="msg-content">
        <div v-if="!isSelf" class="msg-name">
          {{ appellation }}<span v-if="gf" :class="gfclazz">{{ gf }}</span>
        </div>
        <MessageBubble :msg="msg" :tooltip-visible="true" :bg-visible="true">
          <MessageG2 :msg="msg" />
        </MessageBubble>
      </div>
    </div>
    <div
      v-else-if="msg.type === 'file'"
      :style="{ flexDirection: !isSelf ? 'row' : 'row-reverse' }"
      class="msg-common"
    >
      <Avatar
        :account="msg.from"
        :team-id="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <div class="msg-content">
        <div v-if="!isSelf" class="msg-name">
          {{ appellation }}<span v-if="gf" :class="gfclazz">{{ gf }}</span>
        </div>
        <MessageBubble :msg="msg" :tooltip-visible="true" :bg-visible="false">
          <MessageFile :msg="msg" />
        </MessageBubble>
      </div>
    </div>
    <div
      v-else-if="msg.type === 'audio'"
      :style="{
        flexDirection: !isSelf ? 'row' : 'row-reverse',
      }"
      class="msg-common"
    >
      <Avatar
        :account="msg.from"
        :team-id="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <div class="msg-content">
        <div v-if="!isSelf" class="msg-name">
          {{ appellation }}<span v-if="gf" :class="gfclazz">{{ gf }}</span>
        </div>
        <MessageBubble
          :msg="msg"
          :tooltip-visible="true"
          :bg-visible="true"
          style="cursor: pointer"
        >
          <MessageAudio
            :msg="msg"
            :broadcast-new-audio-src="broadcastNewAudioSrc"
          />
        </MessageBubble>
      </div>
    </div>
    <!--notification都不显示-->
    <MessageNotification
      v-else-if="msg.type === 'notification'"
      :msg="msg"
      class="hide"
    />
    <!-- 这里设置自定义消息 -->
    <div v-else-if="msg.type === 'tip'" class="msg-noti">
      <div class="lingdan"></div>
      <div>{{ msg.body }}</div>
    </div>
    <!-- <div v-else-if="msg.type === 'notification'"></div> -->
    <div
      v-else
      :style="{ flexDirection: !isSelf ? 'row' : 'row-reverse' }"
      class="msg-common"
    >
      <Avatar
        :account="msg.from"
        :team-id="msg.scene === 'team' ? msg.to : ''"
        :goto-user-card="true"
      />
      <div class="msg-content">
        <div v-if="!isSelf" class="msg-name">
          {{ appellation }}<span v-if="gf" :class="gfclazz">{{ gf }}</span>
        </div>
        <MessageBubble :msg="msg" :tooltip-visible="true" :bg-visible="true">
          [{{ t('unknowMsgText') }}]
        </MessageBubble>
      </div>
    </div>
    <!-- <div class="spaceEnd" style="margin-right: 50px;"> -->
    <div v-if="msg.attach && msg.scene == 'team' && canShowIt(msg)">
      <div
        v-if="!msg.attach.yxRead || msg.attach.yxRead == 0"
        class="spaceEnd statustxtteam blue"
      >
        <div>未读</div>
      </div>
      <div v-else class="spaceEnd statustxtteam">
        <div>{{ msg.attach.yxRead }}人已读</div>
        <div>
          <span class="notread">{{ msg.attach.yxUnread }}人未读</span>
        </div>
      </div>
    </div>
    <div
      v-else-if="msg.scene == 'p2p' && canShowIt(msg)"
      :class="[getStatusClazz(readed)]"
    >
      {{ getStatus(readed) }}
    </div>
    <!-- <div>
        {{msg.time | timeformat}}
      </div> -->
    <!-- </div> -->
    <TipPanel
      v-if="showEditPop"
      confirm-text="提交"
      sub-title="议价"
      @cancel="submitEditFormCancel"
      @confirm="submitEditForm"
    >
      <input placeholder="请输入价格、无需支付议价金"
      v-model="baojiaNumber"
          class="uni-input submit_ipt_tedian_msg" 
          type="text" maxlength="6"/>
          <view class="submit_ipt_tedian_msg_text">12小时内仅限议价一次，请谨慎填写</view>
    </TipPanel>
    <Bargain
      v-if="showBargain"
      :data-info="shopDetailJson"
      :max-price="maxHeightPrice"
      :history-top-price="historyTopPrice"
      @confirm="sureSubmit"
      @cancel="showBargain = false"
    />
  </div>
</template>

<script lang="ts" setup>
import util from '@/utils/util';
import { isAndroidApp } from '../../../utils';
import { ref, computed, onUnmounted } from '../../../utils/transformVue';
import Avatar from '../../../components/Avatar.vue';
import MessageBubble from './message-bubble.vue';
import { events, MSG_ID_FLAG } from '../../../utils/constants';
import { autorun } from 'mobx';
import { deepClone, stopAllAudio } from '../../../utils';
import { t } from '../../../utils/i18n';
import ReplyMessage from './message-reply.vue';
import MessageFile from './message-file.vue';
import MessageText from './message-text.vue';
import MessageAudio from './message-audio.vue';
import MessageNotification from './message-notification.vue';
import MessageG2 from './message-g2.vue';
import {freeNegoOffer} from '@/config/api/confirmOrder.js';
import Bargain from '@/components/bargain/index.vue'
import {
  getSkinAndHero,
  getDetail,
  getDetailByCode,
  getPreview,
  readHistoryCreate,
  topOfferPrice,
} from '@/config/api/accountDetail.js';
import { customNavigateTo } from '../../../utils/customNavigate';

import request from '../../../../../common/request.js';
const canShowIt = (msg) => {
  const myAccount = uni.$UIKitStore.userStore.myUserInfo.account;
  if (msg.from !== myAccount) {
    return false;
  }

  if (msg.type === 'custom' && msg.attach.type === 'time') {
    return false;
  } else if (msg.type == 'notification') {
    return false;
  } else if (msg.type === 'custom' && msg.attach.type === 'reCallMsg') {
    return false;
  } else if (msg.type === 'custom' && msg.attach.type === 'beReCallMsg') {
    return false;
  }
  return true;
};
const props = defineProps({
  flowState: {
    type: Object,
    default() {
      return {};
    },
  },
  readed: {
    type: Boolean,
    default: false,
  },
  scene: {
    type: String, // Assuming TMsgScene is a custom object type
    required: true,
  },
  to: {
    type: String,
    required: true,
  },
  msg: {
    type: Object, // Assuming IMMessage is a custom object type
    default: () => ({
      idClient: undefined,
      body: undefined,
      attach: {
        type: undefined,
        value: undefined,
        url: undefined,
        canEdit: false,
        canRecall: false,
      },
      type: undefined,
      from: undefined,
      to: undefined,
      scene: undefined,
      time: undefined,
    }),
    required: true,
  },
  index: {
    type: Number,
    required: true,
  },
  replyMsg: {
    type: Object,
    default: () => ({
      idClient: undefined,
      body: undefined,
      attach: {
        type: undefined,
        value: undefined,
        url: undefined,
        canEdit: false,
        canRecall: false,
      },
      type: undefined,
    }),
  },
  broadcastNewAudioSrc: {
    type: String,
  },
});
const appellation = ref('');
const gf = ref('');
const gfclazz = ref('gficon');
const isSelf = ref(false);
const showEditPop = ref(false)
const baojiaNumber=ref('')
const baojiaData = ref({});
const baojiaMsg = ref({});
const showBargain= ref(false)
const shopDetailJson=ref({ subTitle: '' })
const maxHeightPrice=ref(0)
const historyTopPrice=ref(0)
const productIdAccount=ref('')
const sureSubmit=(price)=>{
  uni.showLoading({
        title: '拼命加载中',
      });
      let data = {
        offerPrice: price,
        productId: productIdAccount.value,
      };
      freeNegoOffer(data).then((res) => {
        uni.hideLoading();
        if (res.code == 200) {
            showBargain.value = false;
            uni.showToast({
              title: res.data || '获取数据失败.',
              icon: 'none',
            });
        }
      });
  showBargain.value=false
}
const postPriceFun=()=> {
        getDetail(productIdAccount.value).then(res=>{
          if (res.code == 200) {
            const { product, productAttributeList, productAttributeValueList } =
            res.data;
            shopDetailJson.value = product;
          }
        })
        topOfferPrice({
          productId: productIdAccount.value,
        })
          .then((res) => {
            if (res.code == 200) {
              const { hisTopPrice, offerPrice } = res.data;
              historyTopPrice.value = hisTopPrice.offerPrice || {};
              historyTopPrice.value = offerPrice;
            }
          })
          .finally(() => {
            console.log(2222222);
            
            showBargain.value = true;
          });
    }
// 获取视频首帧
const videoFirstFrameDataUrl = computed(() => {
  const url = props.msg?.attach?.url;
  return url ? `${url}${url.includes('?') ? '&' : '?'}vframe=1` : '';
});

const getStatus = (status) => {
  if (status) {
    return '已读';
  } else {
    return '未读';
  }
};
const handleBaojiaClick=(data = {}, msg)=>{
  
  // console.log(data.productId,'执行了')
  productIdAccount.value=data.productId
  postPriceFun()
  return
  baojiaData.value = data;
  baojiaMsg.value = msg;
  baojiaNumber.value=''
  showEditPop.value=true

}

const submitEditFormCancel=()=>{

  
  showEditPop.value=false
  
}
const submitEditForm=()=>{
  console.log(baojiaNumber.value)
  if(!baojiaNumber.value.trim()){
    uni.showToast({
      title:'请填写报价金额',
      icon: 'none',
    });
  }
  if (/^0/.test(baojiaNumber.value.trim())) {
    uni.showToast({
      title: '请填写正确的报价金额',
      icon: 'none',
    });
  }
  if(Number(baojiaNumber.value)<1000000){
    let txt=`${baojiaNumber.value}元卖不卖`
  handleQuesClick(txt,baojiaData.value,baojiaMsg.value)
  showEditPop.value=false
  }else{
    uni.showToast({
      title:'填写金额不能大于1000000元',
      icon: 'none',
    });
  }

}
const handleQuesClick = (txt, data = {}, msg) => {
  console.log(txt,data,msg)
  // return
  const txtMsg = txt;
  const { productSn } = data;
  const { scene, to, from } = msg;
  let toAccount = to;
  if (to.indexOf('kkzhw') == 0) {
    toAccount = to;
  }
  if (from.indexOf('kkzhw') == 0) {
    toAccount = from;
  }
  uni.$UIKitStore.msgStore
    .sendTextMsgActive({
      scene,
      to: toAccount,
      ext: { 'noyidun': 1, productSn },
      body: txtMsg,
    })
    .then((res) => {
      if (isAndroidApp) {
        setTimeout(() => {
          uni.$emit(events.ON_SCROLL_BOTTOM);
        }, 300);
      } else {
        uni.$emit(events.ON_SCROLL_BOTTOM);
      }
    });
};

const handleLinkTap = (event) => {
  const txt = event.innerText || '';
  if (txt) {
    uni.setClipboardData({
      data: txt,
      success: () =>
        uni.showToast({
          title: '复制成功',
        }),
    });
  }
};

const getStatusClazz = (readed) => {
  return readed ? 'statustxt' : 'statustxt blue';
};

const handleClick = (item, data = {}, actionType) => {
  let { url, fedtemp, msg, h5_url } = item;
  if (actionType == 'action2') {
    let query = util.obj2params(data);
    if (query) {
      h5_url = `${h5_url}?${query}`;
    }
    uni.navigateTo({
      url: h5_url,
    });
  } else if (fedtemp == '127_topay_nego') {
    uni.navigateTo({
      url: `/pages/confirmOrder/confirmOrder?negoId=${data.negoId}&from=myAssess&productCategoryId=${data.productCategoryId}`,
    });
  } else if (fedtemp == '127_member_score') {
    uni.$emit('showScore', { item, data });
  } else if (fedtemp == 'ai_openOrder') {
    uni.navigateTo({
      url: `/pages/confirmOrder/orderDetail?order_id=${url}`,
    });
  } else if (fedtemp == 'ai_openYijia') {
    productIdAccount.value=url
    postPriceFun()
    // #ifdef APP-PLUS | H5
    // uni.navigateTo({
    //   url: `/pages/accountDetail/accountDetail?productId=${url}&autokanjia=1`,
    // });
    // #endif
  } else if (fedtemp == '127_baopei_form') {
    const sessionId = uni.$UIKitStore.uiStore.selectedSession;
    const flowImteamId = props.flowState.id;
    uni.navigateTo({
      url: `/pages/identify/payIdentify?sessionId=${sessionId}&flowImteamId=${flowImteamId}`,
    });
  } else if (fedtemp === '127_seller_draw_form') {
    uni.$emit('showSellerDraw', { item, data });
  } else if (fedtemp === '127_openProduct') {
    // #ifdef APP-PLUS | H5
    uni.navigateTo({
      url: `/pages/accountDetail/accountDetail?${url}`,
    });
    // #endif
  } else if (fedtemp === 'open_baopei') {
    uni.navigateTo({
      url: `/pages/identify/payIdentify`,
    });
  } else if (fedtemp === '127_scjt') {
    uni.$emit('showUploadImg', { url });
  } else if (fedtemp === '127_img') {
    uni.previewImage({
      urls: [url],
    });
  } else if (fedtemp === 'wzry_actionSellerCommitAP') {
    uni.$emit('showAccountForm', { url, iswz: 1 });
  } else if (fedtemp === '127_actionSellerCommitAP') {
    uni.$emit('showAccountForm', { url });
  } else if (fedtemp === '127_actionBuyerUpPhone') {
    uni.$emit('showBuyerUpPhone', { url, data });
  } else if (fedtemp === '127_spzx') {
    uni.$emit('showSpzx', { item });
  } else if (url) {
    if (msg) {
      uni.showModal({
        title: '二次确认',
        content: msg,
        confirmText: '确认',
        success(res) {
          if (url == '#') {
            return;
          }
          if (res.confirm) {
            request.get(item.url).then((res) => {
              if (res.code == 200) {
                uni.showToast({
                  title: res.message,
                  icon: 'none',
                });
              }
            });
          }
        },
      });
    } else {
      if (url == '#') {
        return;
      }
      request.get(item.url).then((res) => {
        if (res.code == 200) {
          uni.showToast({
            title: res.message,
            icon: 'none',
          });
        }
      });
    }
  }
};

const goProduct = (data = {}) => {
  const { productId } = data;
  // #ifdef APP-PLUS | H5
  uni.navigateTo({
    url: `/pages/accountDetail/accountDetail?productId=${productId}`,
  });
  // #endif
};

const goOrder = (data = {}) => {
  const { orderId } = data;
  uni.navigateTo({
    url: `/pages/confirmOrder/orderDetail?order_id=${orderId}`,
  });
};

const canShow = (item) => {
  let isShow = false

  // #ifdef MP
  // 这个时间之前的消息小程序不展示卡片按钮
  const timestamp = new Date(2024, 11,10, 21, 10, 0).getTime();
  isShow = props.msg.time > timestamp && item.accid === uni.$UIKitStore.userStore.myUserInfo.account && item.mp_hidden!=='true' ;
  // #endif

  // #ifdef APP-PLUS|H5
  isShow =  item.accid === uni.$UIKitStore.userStore.myUserInfo.account;
  // #endif

  return isShow
};


const imageUrl = computed(() => {
  return props?.msg?.attach?.url || props.msg?.uploadFileInfo?.filePath;
});

const uninstallIsSelfWatch = autorun(() => {
  // @ts-ignore
  isSelf.value =
    props.msg.from === uni.$UIKitStore.userStore.myUserInfo.account;
});

// 点击图片预览
const handleImageTouch = (url: string) => {
  if (url) {
    uni.previewImage({
      urls: [url],
    });
  }
};

// 点击视频播放
const handleVideoTouch = (msg: any) => {
  stopAllAudio();
  const url = msg?.attach?.url;
  if (url) {
    customNavigateTo({
      url: `/pages/Chat/video-play?videoUrl=${encodeURIComponent(url)}`,
    });
  }
};

// 重新编辑消息
const handleReeditMsg = (msg: any) => {
  uni.$emit(events.ON_REEDIT_MSG, msg);
};

const uninstallAppellationWatch = autorun(() => {
  // 昵称展示顺序 群昵称 > 备注 > 个人昵称 > 帐号
  appellation.value = deepClone(
    // @ts-ignore
    uni.$UIKitStore.uiStore.getAppellation({
      account: props.msg.from,
      teamId: props.scene === 'team' ? props.to : '',
    }),
  );
  gf.value = '';
  if (props.msg) {
    const { scene, to, from } = props.msg;
    if (scene == 'team') {
      try {
        const team = uni.$UIKitStore.teamStore.teams.get(to);
        const { serverExt = {} } = team;
        let obj = JSON.parse(serverExt);
        const { sim, bim } = obj;
        if (from !== uni.$UIKitStore.userStore.myUserInfo.account) {
          if (from == sim) {
            gf.value = '卖家';
            gfclazz.value = 'smsg';
          } else if (from == bim) {
            gf.value = '买家';
            gfclazz.value = 'bmsg';
          }
        }
      } catch (e) {}
    }
  }
  if (props.msg && props.msg.from && props.msg.from.indexOf('kk') === 0) {
    gf.value = '官方';
    gfclazz.value = 'gficon';
  }
});

onUnmounted(() => {
  uninstallIsSelfWatch();
  uninstallAppellationWatch();
});
</script>

<style scoped lang="scss">
.statustxtteam {
  text-align: right;
  font-size: 20rpx;
  color: #969696;
  margin-right: 50px;
}
.statustxtteam.blue {
  color: #4285F4;
}
.notread {
  margin-left: 4rpx;
  color: #FF720C;
}
.statustxt {
  text-align: right;
  margin-right: 100rpx;
  margin-top: 4rpx;
  font-size: 20rpx;
  color: #969696;
}
.statustxt.blue {
  color: #4285F4;
}
.lingdan {
  width: 40rpx;
  height: 28rpx;
  position: relative;
  top: 4rpx;
  background: url('../../../../../static/old/lingdan.jpg') no-repeat;
  background-size: contain;
}
/deep/ .copy.iconfontnew::after {
  content: '\e60d';
  color: #4285F4;
  margin-left: 5px;
  cursor: pointer;
  font-size: 14px;
}
.mgr8 {
  margin-right: 8px;
}
.gficon {
  padding: 2px 8px;
  display: inline-block;
  margin-left: 6px;

  border-radius: 12px;
  border: 1px solid #ff9a3d;
  background: var(
    --Tag-Orange-Stroke,
    linear-gradient(87deg, #ff7a00 3.31%, #ffe3c0 142.11%)
  );

  color: #fff;
  font-size: 9px;
  font-weight: 500;
}
.smsg {
  padding: 2px 8px;
  display: inline-block;
  margin-left: 6px;

  border-radius: 12px;
  border: 1px solid #ff002e;
  background: linear-gradient(87deg, #ff002e 3.31%, #ffc0c0 142.11%);
  color: #fff;
  font-size: 9px;
  font-weight: 500;
}
.bmsg {
  padding: 2px 8px;
  display: inline-block;
  margin-left: 6px;

  border-radius: 12px;
  border: 1px solid #4285F4;
  background: linear-gradient(90deg, #4285F4 11.95%, #5C99FF 31.36%, #BBD5FF 91.25%);

  color: #fff;
  font-size: 9px;
  font-weight: 500;
}

// 自定义内容样式
/deep/ .kk_custom_msg_wrapper_card {
  border-radius: 24rpx;
  background: #fff;
  box-shadow: 2rpx 4rpx 12rpx 0px rgba(0, 0, 0, 0.05);
  margin-left: 24rpx;
  overflow: hidden;
  padding: 24rpx;
  max-width: 520rpx;

  .at {
    color: #409eff;
  }
  .atbuy {
    color: #4285F4;
  }
  .atsell {
    color: #ff002e;
  }

  // 卡片信息的常问问题
  .kk_custom_msg_question {
    font-family: 'PingFang SC';
    font-size: 24rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 48rpx; /* 200% */
    background: var(
      --Blue,
      linear-gradient(90deg, #4285f4 11.95%, #5c99ff 31.36%, #bbd5ff 91.25%)
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .kk_custom_msg_question_red{
    font-family: 'PingFang SC';
    font-size: 24rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 48rpx; /* 200% */
    color: #FF720C;
  }
  .kk_custom_msg_question:hover {
    color: #5c99ff;
  }
  .el-tag {
    white-space: normal;
    line-height: 48rpx;
    height: auto;

    padding: 20rpx;
    border-radius: 20rpx;
    background-color: rgba(66, 133, 244, 0.1);

    color: #1b1b1b;
    font-size: 24rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 32rpx;
  }

  .oneline {
    max-width: 440rpx;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .twoLine {
    max-width: 440rpx;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  // 卡片信息的操作按钮
  .actions_box {
    display: flex;
    justify-content: flex-end;
    flex-wrap: wrap;

    .kk-btn {
      margin-left: 20rpx;
      margin-top: 20rpx;
      padding: 0 20rpx;
    }
    .kk_custom_msg_action {
      font-weight: 700;
      margin-top: 20rpx;
      text-align: center;
      height: 60rpx;
      line-height: 60rpx;
      color: #409eff;
      background: #ecf5ff;
      border: 2rpx solid #b3d8ff;
    }
  }

  // 卡片信息里的标题
  .kk_custom_msg_title {
    max-width: 440rpx;
    word-wrap: break-word;
    line-height: 48rpx;

    color: #1b1b1b;
    font-size: 24rpx;
    font-weight: 500;
  }

  // 卡片信息里的内容字体，除商品信息项，在App.vue单独设置了
  .kk_custom_msg_content {
    color: #000;
    font-size: 24rpx;
    font-weight: 400;
    line-height: 40rpx;
    // padding-bottom: 10px;

    .kkimimg {
      height: 400rpx;
      object-fit: contain;
      cursor: pointer;
    }
  }
}
.card-box {
  background: #fff;
}
.msg-item-wrapper {
  padding: 0 30rpx 30rpx;
}

.msg-common {
  margin-top: 16rpx;
  display: flex;
  align-items: flex-start;
  font-size: 24rpx;
}

.msg-content {
  display: flex;
  flex-direction: column;
}

.msg-name {
  font-size: 24rpx;
  color: #000;
  text-align: left;
  margin-bottom: 8rpx;
  max-width: 300rpx;
  padding-left: 24rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

.msg-image {
  max-width: 100%;
}

.msg-time {
  margin-top: 16rpx;
  text-align: center;
  color: #969696;
  font-size: 24rpx;
  letter-spacing: 0.48px;
}

.msg-recall-btn {
  margin-left: 10rpx;
  color: #4285F4;
}

.msg-recall2 {
  font-size: 32rpx;
}

.self-msg-recall {
  max-width: 360rpx;
  overflow: hidden;
  padding: 24rpx 32rpx;
  border-radius: 16rpx 0px 16rpx 16rpx;
  margin-right: 16rpx;
  background-color: #d6e5f6;
  color: #666666;
}

.msg-recall {
  max-width: 360rpx;
  overflow: hidden;
  padding: 24rpx 32rpx;
  border-radius: 0px 16rpx 16rpx 16rpx;
  margin-left: 16rpx;
  background-color: #e8eaed;
  color: #666666;
}

.recall-text {
  color: #666666;
}

.video-play-button {
  width: 100rpx;
  height:100rpx;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  z-index: 9;
}

.video-play-icon {
  width: 0;
  height: 0;
  border-top: 20rpx solid transparent;
  border-bottom: 20rpx solid transparent;
  border-left: 36rpx solid #fff;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-40%, -50%);
}

.video-msg-wrapper {
  box-sizing: border-box;
  max-width: 360rpx;
}
.msg-noti {
  display: flex;
  margin: 16rpx auto 0;
  text-align: center;
  font-size: 28rpx;
  color: #b3b7bc;
  max-width: 70%;
  justify-self: start;
  align-items: baseline;
}
.msg-noti.hide {
  display: none;
}
.submit_ipt_tedian_msg {
  background-color: #fff;
  height: 70rpx;
  line-height: 70rpx;
  box-sizing: border-box;
  padding: 0 48rpx;
  border-radius: 40rpx;
  border: 2rpx solid rgba(0, 0, 0, 0.1);

  color: #1b1b1b;
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.48px;
  margin: 40rpx 0 0 0px;
}
.submit_ipt_tedian_msg_text{
  color: red;
  font-size: 24rpx;
  text-indent: 48rpx;
  margin-bottom: 40rpx;
}
</style>
