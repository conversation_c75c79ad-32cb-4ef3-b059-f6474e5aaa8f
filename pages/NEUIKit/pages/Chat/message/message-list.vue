<template>
  <div class="msg-list-wrapper" @touchstart="handleTapMessageList">
    <scroll-view
      :scroll-top="scrollTop"
      scroll-y="true"
      class="message-scroll-list"
    >
      <div v-show="!noMore" class="view-more-text" @click="onLoadMore">
        {{ t('viewMoreText') }}
      </div>
      <view v-show="noMore" class="msg-tip">{{ t('noMoreText') }}</view>
      <div v-for="(item, index) in finalMsgs" :key="item.renderKey">
        <MessageItem
          :flow-state="flowState"
          :scene="scene"
          :to="to"
          :msg="item"
          :readed="item.time <= msgReceiptTime"
          :index="index"
          :key="`${item.renderKey}`"
          :reply-msg="replyMsgsMap && replyMsgsMap[item.idClient]"
          :broadcast-new-audio-src="broadcastNewAudioSrc"
        >
        </MessageItem>
      </div>
      <div ref="trigger" class="trigger"></div>
    </scroll-view>
    <div v-if="unreadCount > 0" class="tool_bar2">
      <div class="unreadCount" @click="goBottom">
        <uni-icons type="arrow-down" size="16" color="#409eff"></uni-icons>
        {{ unreadCount }}条新消息
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import _ from 'lodash';
import { deepClone } from '../../../utils';
import type { IMMessage } from 'nim-web-sdk-ng/dist/NIM_MINIAPP_SDK/MsgServiceInterface';
import {
  ref,
  computed,
  onBeforeMount,
  onUnmounted,
  onMounted,
  getCurrentInstance,
  onBeforeUnmount
} from '../../../utils/transformVue';
import MessageItem from './message-item.vue';
import { events } from '../../../utils/constants';
import { caculateTimeago } from '../../../utils/date';
import { t } from '../../../utils/i18n';
import { autorun } from 'mobx';
const props = defineProps({
  flowState: {
    type: Object,
    default() {
      return {};
    },
  },
  sessionId2: {
    type: String,
    default: '',
  },
  msgs: {
    type: Array,
    required: true,
  },
  scene: {
    type: String, // Assuming TMsgScene is a custom object type
    required: true,
  },
  to: {
    type: String,
    required: true,
  },
  loadingMore: {
    type: Boolean,
    default: undefined,
  },
  noMore: {
    type: Boolean,
    default: undefined,
  },
  replyMsgsMap: {
    type: Object,
    default: undefined,
  },
});
const msgReceiptTime = ref(0);

// 如果你想结合时间戳来确保更高的唯一性（特别是在分布式系统中）
function randomKey() {
  return _.uniqueId();
}

autorun(() => {
  const session = deepClone(
    uni?.$UIKitStore?.sessionStore?.sessions?.get(props.sessionId2),
  );
  if (session) {
    msgReceiptTime.value = session.msgReceiptTime || 0;
  }
});

let unreadCount = ref(0);

const goBottom = () => {
  uni.$emit(events.ON_SCROLL_BOTTOM);
};
let observer = null;
let triggerShow = ref(true);
const initIntersectionObserver = () => {
  observer = uni.createIntersectionObserver(getCurrentInstance()?.proxy);
  observer.observe('.trigger', (res) => {
    if (res.intersectionRatio) {
      triggerShow.value = true;
      uni.$emit('clearUnreadCount');
    } else {
      triggerShow.value = false;
    }
  });
};


onMounted(() => {
  initIntersectionObserver();
  
});

  
onBeforeMount(() => {
  if (props.scene === 'team') {
    // @ts-ignore
    uni.$UIKitStore.teamMemberStore.getTeamMemberActive(props.to);
  }

  // 保证一下事件最后能加上，比如切换客服的情况
  setTimeout(() => {
    uni.$on('addUnreadCount', () => {
      if (!triggerShow.value) {
        unreadCount.value++;
      } else {
        uni.$emit(events.ON_SCROLL_BOTTOM);
      }
    });
    uni.$on('clearUnreadCount', () => {
      unreadCount.value = 0;
    });

    uni.$on(events.AUDIO_URL_CHANGE, (url) => {
      broadcastNewAudioSrc.value = url;
    });

    uni.$on(events.ON_SCROLL_BOTTOM, () => {
      scrollToBottom();
    });

    uni.$on(events.ON_LOAD_MORE, () => {
      const msg = finalMsgs.value.filter(
        (item) =>
          !(
            item.type === 'custom' &&
            ['beReCallMsg', 'reCallMsg', 'time'].includes(
              item.attach?.type || '',
            )
          ),
      )[0];
      if (msg) {
        uni.$emit(events.GET_HISTORY_MSG, msg);
      }
    });
  }, 34);
});

onUnmounted(() => {
  if (observer) {
    observer.disconnect();
  }
  uni.$off('addUnreadCount');
  uni.$off('clearUnreadCount');
  uni.$off(events.ON_SCROLL_BOTTOM);
  uni.$off(events.ON_LOAD_MORE);
  uni.$off(events.AUDIO_URL_CHANGE);
});

const scrollTop = ref(99999);
const finalMsgs = computed(() => {
  const res: IMMessage[] = [];
  props.msgs.forEach((item: IMMessage, index) => {
    // 如果两条消息间隔超过5分钟，插入一条自定义时间消息
    // @ts-ignore
    if (index > 0 && item.time - props.msgs[index - 1].time > 5 * 60 * 1000) {
      // @ts-ignore
      res.push({
        idClient: 'time-' + item.time,
        type: 'custom',
        attach: {
          type: 'time',
          value: caculateTimeago(item.time),
        },
        status: 'sent',
        time: item.time,
        // @ts-ignore
        renderKey: `${item.time + 1}-${randomKey()}`,
      });
    }
    let msg = {
      ...item,
      // @ts-ignore
      renderKey: `${item.time}-${randomKey()}`,
    };
    if (
      msg.scene == 'team' &&
      msg.type === 'custom' &&
      msg.attach?.type === 'kkmsg_action'
    ) {
      const team = uni.$UIKitStore.teamStore.teams.get(msg.to);
      let msgText = msg.attach?.body?.title;
      let content = msg.attach?.body?.content;
      const { serverExt = '{}' } = team;
      const serverExtJson = JSON.parse(serverExt);
      const { bim, sim } = serverExtJson;
      const myAccount = uni.$UIKitStore?.userStore.myUserInfo.account;
      if (bim == myAccount) {
        let nick = serverExtJson.bnm || '';
        if (nick) {
          if (msgText.indexOf('@买家老板') != -1) {
            msgText = msgText.replace(
              /@买家老板/g,
              `<span class="atbuy">@买家${nick}</span>`,
            );
          }
          if (content.indexOf('@买家老板') != -1) {
            content = content.replace(
              /@买家老板/g,
              `<span class="atbuy">@买家${nick}</span>`,
            );
          }
        }
      }
      if (sim == myAccount) {
        let nick = serverExtJson.snm || '';
        if (nick) {
          if (msgText.indexOf('@卖家老板') != -1) {
            msgText = msgText.replace(
              /@卖家老板/g,
              `<span class="atsell">@卖家${nick}</span>`,
            );
          }
          if (content.indexOf('@卖家老板') != -1) {
            content = content.replace(
              /@卖家老板/g,
              `<span class="atsell">@卖家${nick}</span>`,
            );
          }
        }
      }
      msg.attach.body.title = msgText;
      msg.attach.body.content = content;
    }
    if (!(msg.type === 'custom' && msg.attach?.type === 'beReCallMsg')) {
      res.push(msg);
    }
  });

  return res;
});

const broadcastNewAudioSrc = ref<string>('');

// 消息滑动到底部，建议搭配 nextTick 使用
const scrollToBottom = () => {
  scrollTop.value += 3000;
  const timer = setTimeout(() => {
    scrollTop.value += 1;
    clearTimeout(timer);
  }, 1000);
};

const onLoadMore = () => {
  const msg = finalMsgs.value.filter(
    (item) =>
      !(
        item.type === 'custom' &&
        ['beReCallMsg', 'reCallMsg', 'time'].includes(item.attach?.type || '')
      ),
  )[0];
  if (msg) {
    uni.$emit(events.GET_HISTORY_MSG, msg);
  }
};

const handleTapMessageList = () => {
  uni.$emit(events.CLOSE_PANEL);
  setTimeout(() => {
    uni.$emit(events.CLOSE_PANEL);
  }, 300);
};
</script>

<style scoped lang="scss">
.tool_bar2 {
  position: fixed;
  bottom: 212rpx;
  right: 20rpx;
  font-size: 24rpx;
  color: #409eff;
  background: #fff;
  border-radius: 20rpx;
  padding: 10rpx;
  .unreadCount {
    display: flex;
    justify-content: center;
  }
}
.trigger {
  height: 2rpx;
  width: 100%;
}
.msg-list-wrapper {
  flex: 1;
  overflow: hidden;
  display: flex;
  height: 100%;
  box-sizing: border-box;
  // padding: 16px 0;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  background-color: #FCFCFC;
}

// .msg-list {
//   padding: 0 16px 20px 16px;
//   box-sizing: border-box;
//   transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
//   overflow-x: hidden;
//   // height: 100%;
// }

.block {
  width: 100%;
  height: 80rpx;
}

.message-scroll-list {
  height: 100%;
  // height: calc(100vh - 320rpx);
  box-sizing: border-box;
  padding-bottom: 2rpx;
}

.msg-tip,.view-more-text {
  margin-top: 40rpx;
  text-align: center;
  color: #969696;
  font-size: 24rpx;
  letter-spacing: 0.48px;
}
page > view > message > view > message-list {
  height: 100%;
}
</style>
