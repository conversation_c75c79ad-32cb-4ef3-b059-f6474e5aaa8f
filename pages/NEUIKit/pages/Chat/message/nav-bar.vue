<template>
  <!-- 样式兼容微信小程序  -->
  <div>
    <div :style="{height:statusBarHeight+'px'}"></div>
    <div
      :style="{
        backgroundColor: backgroundColor || '#ffffff',
        backgroundImage: `url(${title})`,
        height: isWxApp? titleBarHeight+'px':'96rpx',
        alignItems: isWxApp ? 'flex-end' : 'center',
      }"
      class="nav-bar-wrapper"
    >
      <slot v-if="showLeft" name="left"></slot>
      <div v-else @tap="back">
        <Icon :size="22" type="icon-zuojiantou"></Icon>
      </div>
      <div class="title-container">
        <div class="title">
          <span>{{ title }}</span> 
          <span v-if="to.indexOf('kkzhw') == 0" class="onlineTime gradient-primary">09:30-00:30</span>
          <span v-if="online" class="online">在线</span>
        </div>
        <div v-if="subTitle" class="subTitle">{{ subTitle }}</div>
        <slot name="icon"></slot>
      </div>
      <div>
        <slot name="right"></slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { isWxApp } from '../../../utils';
import Icon from '../../../components/Icon.vue';
import {
  ref,
} from '../../../utils/transformVue';


const SYS = uni.getSystemInfoSync()
const statusBarHeight = ref(SYS.statusBarHeight) // 安全高度
const {top, height} = uni.getMenuButtonBoundingClientRect() // 胶囊高度
const titleBarHeight = ref((top-SYS.statusBarHeight)*2+height) // 导航栏高度

console.log('高度',top, height,statusBarHeight.value,titleBarHeight.value )

defineProps({
  online: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    required: true,
  },
  subTitle: {
    type: String,
    default: '',
  },
  backgroundColor: {
    type: String,
    default: '',
  },
  showLeft: {
    type: Boolean,
  },
  to: {
    type: String,
    default: '',
  },
});

const back = () => {
  uni.navigateBackCustom();
};
</script>

<style lang="scss" scoped>
@import '../../../pages/styles/common.scss';

.nav-bar-wrapper {
  display: flex;
  justify-content: space-between;
  padding: 0 32rpx;
  z-index: 99;
  position: relative;

  box-shadow: 2rpx 4rpx 6rpx 0px rgba(0, 0, 0, 0.05);

  .title-container {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 0;
    bottom: 0;
    z-index: -1;
    // width: 260px;

    flex: 1;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .title {
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    white-space: nowrap;

    color: #1B1B1B;
    font-size: 28rpx;
    font-weight: 500;
    letter-spacing: 1.2rpx;

    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .onlineTime {
    font-family: Inter;
    font-size: 28rpx;
    font-weight: 500;
    letter-spacing: 0.56px;
    margin-left: 4rpx;
    display: inline-block;
    vertical-align: middle;
    margin-top: -4rpx;
  }
  .online {
    margin-left: 10rpx;
    display: inline-block;
    box-sizing: border-box;
    
    width: 74rpx;
    height: 34rpx;
    line-height: 30rpx;

    border-radius: 48rpx;
    border: 2rpx solid #28D386;
    background: linear-gradient(90deg, #28D386 11.95%, #94EAA7 91.25%);
    box-shadow: 0px 0px 10rpx 0px rgba(255, 255, 255, 0.60) inset;

    color: #FFF;
    text-align: center;
    font-size: 20rpx;
    font-style: normal;
    font-weight: 500;
    letter-spacing: 0.4rpx;
  }
  .subTitle {
    white-space: nowrap;
    font-weight: 500;
  }
}
</style>
