<template>
  <uni-popup ref="popup" background-color="#fff" @change="change">
    <view class="order_box">
      <view class="title_box">
        <view class="order_title">
          选择您要咨询的订单或商品
          <!-- <icon class="close_btn" type="clear" size="18" @click="hide" /> -->
          <IconFont :size="16" icon="close" class="close_btn" @click="hide" />
        </view>
        <view class="spaceBetween tabs">
          <view
            :class="type === 3 ? 'active' : ''"
            class="typelist_item"
            @click="changeType(3)"
            >我的足迹</view
          ><view
            :class="type === 4 ? 'active' : ''"
            class="typelist_item"
            @click="changeType(4)"
            >我的收藏</view
          >
          <view
            :class="type === 1 ? 'active' : ''"
            class="typelist_item"
            @click="changeType(1)"
            >买家订单</view
          >
          <view
            :class="type === 2 ? 'active' : ''"
            class="typelist_item"
            @click="changeType(2)"
            >卖家订单</view
          >
        </view>
      </view>
      <scroll-view class="scroll_box g-bg" scroll-y @scrolltolower="loadMore">
        <template v-if="type == 3 || type === 4">
          <view v-if="footerList.length === 0" class="empty_box"
            >暂时还没有数据</view
          >
          <view v-for="item in footerList" v-else :key="item.id">
            <OrderCard
              v-bind="{
                time: item.createTime || '',
                pic: item.productPic
              }"
            >
              <view slot="detail">
                <view class="text_linTwo">{{ item.productName }}</view>
                <view class="game-name">{{ item.productCategoryName }}</view>
                <view class="spaceBetween" style="align-items: flex-end;">
                  <view class="price">¥{{ item.newProductPrice }}</view>
                  <view
                    class="kk-btn line"
                    style="margin-left: 40rpx"
                    @click="sendFooter(item)"
                    ><span>发送链接</span></view
                  >
                </view>
              </view>
            </OrderCard>
          </view>
        </template>
        <template v-else>
          <view v-if="orderList.length === 0" class="empty_box"
            >暂时还没有数据</view
          >
          <view v-for="item in orderList" v-else :key="item.id">
            <OrderCard
              v-bind="{
                time: item.createTime || '',
                pic: item.orderItem.productPic,
              }"
            >
            <view slot="detail">
                <view class="text_linTwo">{{ item.orderItem.productName }}</view>
                <view class="game-name">{{ item.productCategoryName }}</view>
                <view class="spaceBetween" style="align-items: flex-end;">
                  <view class="price">¥{{ item.payAmount }}</view>
                  <view
                    class="kk-btn line"
                    style="margin-left: 40rpx"
                    @click="sendOrder(item)"
                    ><span>发送链接</span></view
                  >
                </view>
              </view>
            </OrderCard>
          </view>
        </template>
      </scroll-view>
    </view>
  </uni-popup>
</template>

<script>
import { isAndroidApp } from '../../utils';
import { events } from '../../utils/constants';
import { getDetail } from '@/config/api/accountDetail.js';
import { m2kfSendOrder, m2kfSendProduct } from '@/config/api/kf';
import util from '@/utils/util';
import { myOrderList, mySellerList } from '@/config/api/confirmOrder.js';
import {
  getReadHistoryList,
  getProductCollectionList,
} from '@/config/api/accountDetail.js';

import OrderCard from '@/components/product/OrderCard.vue';

export default {
  components: { OrderCard },
  props: {
    myorderModal: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      util,
      type: 3,
      orderList: [],
      page: 1,
      pageSize: 10,
      totalPage: 1,
      status: -1,
      showPayPopup: false,
      footerList: [],
    };
  },
  watch: {
    myorderModal(val) {
      if (val) {
        this.show();
      } else {
        this.hide();
      }
    },
  },
  methods: {
    loadMore() {
      if (this.page < this.totalPage) {
        this.page++;
        this.getOrder('add');
      } else {
        // setTimeout(() => {
        //   uni.showToast({
        //     title: '已全部加载完毕',
        //     icon: 'none',
        //   });
        // }, 1000);
      }
    },
    show() {
      this.$refs.popup.open('bottom');
      this.getOrder();
    },
    hide() {
      this.$refs.popup.close();
    },
    change(item) {
      const { show } = item;
      this.$emit('changeMyorderModal', show);
    },
    sendOrder(orderDetail) {
      let sessionId = uni.$UIKitStore.uiStore.selectedSession;
      const splitList = sessionId.split('-');
      const scene = splitList[0];
      splitList.shift();
      const to = splitList.join('-');
      const myAccount = uni.$UIKitStore.userStore.myUserInfo.account;
      const orderId = orderDetail.id;
      const orderSn = orderDetail.orderSn;
      const { orderItemList } = orderDetail;
      const findProduct = orderItemList.find((ele) => {
        return ele.itemType === 0;
      });
      const { productId, productSn } = findProduct;
      const status = util.getStatus(orderDetail.orderStatus);
      const createTime = util.formatTime(
        orderDetail.createTime,
        'YYYY-MM-DD HH:mm:ss',
      );
      const content = `
    <div>
      <div class="spaceBetween msg-flexstart">
        <img src="${findProduct.productPic}" class="msg-productImg" />
        <div>
          <div class="twoLine">${findProduct.productName.substring(0,30)}</div>
          <div class="msg-red">￥${orderDetail.payAmount}</div>
        </div>
      </div>
      <div class="spaceBetween">
          <div>订单状态：</div>
          <div>${status}</div>
      </div>
      <div class="spaceBetween">
          <div>订单号：</div>
          <div>${orderDetail.orderSn}</div>
      </div>
      <div class="spaceBetween">
          <div>订单时间：</div>
          <div>${createTime}</div>
      </div>
    </div>`;
      const attach = {
        data: {
          type: 'order',
          orderId,
          orderSn,
          productId,
          productSn,
        },
        body: {
          title: '我要咨询这笔订单',
          content,
        },
        type: 'kk_order_msg_fed',
      };
      m2kfSendOrder({
        orderId,
        kfIM: to,
      });
      uni.$UIKitStore.msgStore
        .sendCustomMsgActive({
          scene: scene,
          from: myAccount,
          to: to,
          attach: JSON.stringify(attach),
        })
        .finally((res) => {
          if (isAndroidApp) {
            setTimeout(() => {
              uni.$emit(events.ON_SCROLL_BOTTOM);
            }, 300);
          } else {
            uni.$emit(events.ON_SCROLL_BOTTOM);
          }
        })
        .catch((err) => {
          console.log('发送失败', err);
        });
      this.hide();
    },
    sendFooter(item) {
      getDetail(item.productId).then((res) => {
        if (res.code == 200) {
          const productAttributeList = res.data.productAttributeList;
          let sessionId = uni.$UIKitStore.uiStore.selectedSession;
          const splitList = sessionId.split('-');
          const scene = splitList[0];
          splitList.shift();
          const to = splitList.join('-');
          const myAccount = uni.$UIKitStore.userStore.myUserInfo.account;
          const content = `<div class="spaceBetween msg-flexstart">
            <img src="${item.productPic}" class="msg-productImg" />
            <div>
              <div class="twoLine">${item.productSubTitle.substring(0,30)}</div>
              <div class="msg-red">￥${item.productPrice || ''}</div>
            </div>
          </div>`;
          let type4List = [];
          productAttributeList.forEach((ele) => {
            if (ele.type == 4) {
              type4List.push(ele.name);
            }
          });
          if (
            item.productStatusTxt == '已售' ||
            item.productStatusTxt == '已下架'
          ) {
            type4List = [];
          }
          const attach = {
            data: {
              type: 'product',
              productId: item.productId,
              productSn: item.productSn,
              productCategoryId: item.productCategoryId,
            },
            body: {
              title: item.productSubTitle.substring(0,30),
              content,
              type4List,
            },
            type: 'kk_product_msg_fed',
          };
          m2kfSendProduct({
            productId: item.productId,
            kfIM: to,
          });
          uni.$UIKitStore.msgStore
            .sendCustomMsgActive({
              scene: scene,
              from: myAccount,
              to: to,
              attach: JSON.stringify(attach),
            })
            .finally((res) => {
              if (isAndroidApp) {
                setTimeout(() => {
                  uni.$emit(events.ON_SCROLL_BOTTOM);
                }, 300);
              } else {
                uni.$emit(events.ON_SCROLL_BOTTOM);
              }
            })
            .catch((err) => {
              console.log('发送失败', err);
            });
          this.hide();
        }
      });
    },
    changeType(type) {
      this.type = type;
      this.page = 1;
      this.getOrder();
    },
    getOrder(str) {
      if (!str) {
        this.footerList = [];
        this.orderList = [];
      }
      if (this.type === 1) {
        myOrderList({
          status: -1,
          pageNum: this.page,
          pageSize: 10,
        }).then((res) => {
          if (res.code == 200) {
            let list = res.data.list || [];
            this.totalPage = res.data.totalPage;
            list = list.map((i) => ({
              ...i,
              orderItem:
                i.orderItemList.filter((e) => e.itemType === 0)[0] || {},
            }));
            if (str == 'add') {
              this.orderList = this.orderList.concat(list);
            } else {
              this.orderList = list;
            }
            console.log('orderList', this.orderList);
          }
        });
      } else if (this.type === 2) {
        mySellerList({
          status: -1,
          pageNum: this.page,
          pageSize: 10,
        }).then((res) => {
          if (res.code == 200) {
            let list = res.data.list || [];
            list = list.map((i) => ({
              ...i,
              orderItem:
                i.orderItemList.filter((e) => e.itemType === 0)[0] || {},
            }));
            this.totalPage = res.data.totalPage;
            if (str == 'add') {
              this.orderList = this.orderList.concat(list);
            } else {
              this.orderList = list;
            }
          }
        });
      } else if (this.type === 3) {
        getReadHistoryList({
          pageSize: 10,
          pageNum: this.page,
        }).then((res) => {
          if (res.code == 200) {
            let list = res.data.list || [];
            this.totalPage = res.data.totalPage;
            if (str == 'add') {
              this.footerList = this.footerList.concat(list);
            } else {
              this.footerList = list;
            }
          }
        });
      } else {
        getProductCollectionList({
          pageSize: 10,
          pageNum: this.page,
        }).then((res) => {
          if (res.code == 200) {
            let list = res.data.list || [];
            this.totalPage = res.data.totalPage;
            if (str == 'add') {
              this.footerList = this.footerList.concat(list);
            } else {
              this.footerList = list;
            }
          }
        });
      }
      console.log(this.footerList);
    },
  },
};
</script>

<style lang="scss" scoped>
.empty_box {
  border-radius: 40rpx;
  background: #f9f6f3;
  height: 34px;
  line-height: 34px;

  color: #1b1b1b;
  text-align: center;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  letter-spacing: 0.48px;
}

.order_box {
  height: 960rpx;
  background: #fff;
  overflow: hidden;

  .scroll_box {
    height: 780rpx;
    overflow: auto;

    padding: 40rpx 40rpx 0 40rpx;
    box-sizing: border-box;
  }
  .title_box {
    position: sticky;
    top: 0;
    z-index: 90;
  }
  .order_title {
    background: #fff;
    text-align: center;
    padding: 40rpx 0;

    color: #000;
    font-size: 30rpx;
    font-weight: 500;

    position: relative;

    .close_btn {
      position: absolute;
      right: 22rpx;
      top: 14px;
    }
  }
  .tabs {
    justify-content: space-between;
    padding: 16px 22px;
    background: #fff;
    box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);

    .typelist_item {
      position: relative;
      color: rgba(0, 0, 0, 0.4);
      font-size: 24rpx;
      font-weight: 400;
      line-height: normal;
      letter-spacing: 0.48px;
    }
    .typelist_item.active {
      color: #1b1b1b;
      font-weight: 500;
    }
  }
}

.game-name {
  margin-top: 16rpx;
  display: flex;
  height: 20px;
  line-height: 20px;
  padding: 0 20rpx;
  width: fit-content;
  justify-content: center;
  align-items: center;
  border-radius: 20px;
  background: #ffb74a;

  color: #fff;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 10px;
  font-style: normal;
  font-weight: 500;
}
.price {
  color: $uni-color-primary;
  font-size: 28rpx;
  font-weight: 600;
}

/deep/.card-box .item-pic{
  width: 180rpx;
  height: 180rpx;
  border-radius: 24rpx;
}
/deep/.card-box .item-content .content-info {
  height: fit-content;
}
</style>
