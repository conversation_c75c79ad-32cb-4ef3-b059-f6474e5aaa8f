<template>
  <div
    :class="[
      'conversation-item-container',
      { 'show-action-list': showMoreActions, 'stick-on-top': isStickOnTop },
    ]"
    @touchstart="handleTouchStart"
    @touchmove="handleTouchMove"
    @click="handleConversationItemClick()"
  >
    <div class="conversation-item-content">
      <div class="conversation-item-left">
        <div class="unread" v-if="unread">
          <div class="dot" v-if="isMute"></div>
          <div class="badge" v-else>{{ unread }}</div>
        </div>
        <Avatar :account="avatarId" :avatar="teamAvatar" />
      </div>
      <div class="conversation-item-right">
        <div class="conversation-item-top">
          <Appellation
            class="conversation-item-title"
            v-if="session.scene === 'p2p'"
            :account="session.to"
            :online="session.online"
          />
          <span v-else class="conversation-item-title">{{ sessionName }}</span>
          <span class="conversation-item-time">{{ date }}</span>
        </div>
        <div class="conversation-item-desc">
          <span v-if="beMentioned" class="beMentioned">{{
            '[' + t('someoneText') + '@' + t('meText') + ']'
          }}</span>
          <span class="conversation-item-desc-content">{{ content }}</span>
          <Icon
            v-if="isMute"
            iconClassName="conversation-item-desc-state"
            type="icon-xiaoximiandarao"
            color="#ccc"
          />
        </div>
      </div>
    </div>
    <div class="right-action-list">
      <div
        v-for="action in moreActions"
        :key="action.type"
        :class="['right-action-item', action.class]"
        @click="() => handleClick(action.type)"
      >
        {{ action.name }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import Avatar from '../../../components/Avatar.vue';
import Appellation from '../../../components/Appellation.vue';
import Icon from '../../../components/Icon.vue';
import { getMsgContentTipByType } from '../../../utils/msg';
import { computed, onUpdated } from '../../../utils/transformVue';
import type { NimKitCoreTypes } from '@xkit-yx/core-kit';
import dayjs from 'dayjs';
import { t } from '../../../utils/i18n';

const props = defineProps({
  session: {
    type: Object,
    default: () => {
      return {
        ack: 0,
        scene: '',
        id: '',
        to: '',
        lastMsg: {
          time: 0,
          type: '',
          body: '',
          status: '',
        },
        unread: 0,
        isMute: false,
        beMentioned: false,
        online: false,
      };
    },
  },
  showMoreActions: {
    type: Boolean,
    default: () => false,
  },
});

const emit = defineEmits(['click', 'delete', 'stickyToTop', 'leftSlide']);

const isStickOnTop = computed(() => {
  return props.session.stickTopInfo?.isStickOnTop;
});

const moreActions = computed(() => {
  return [
    {
      name: props.session.stickTopInfo?.isStickOnTop
        ? t('deleteStickTopText')
        : t('addStickTopText'),
      class: 'action-top',
      type: 'action-top',
    },
    {
      name: t('deleteSessionText'),
      class: 'action-delete',
      type: 'action-delete',
    },
  ];
});

const handleClick = (type: string) => {
  if (type === 'action-top') {
    emit('stickyToTop', props.session);
  } else {
    emit('delete', props.session);
  }
};

const teamAvatar = computed(() => {
  const { session } = props;
  if (session.scene === 'team') {
    const { avatar } = props.session as NimKitCoreTypes.TeamSession;
    return avatar;
  }
});

const sessionName = computed(() => {
  const { session } = props;
  if (session.name) {
    return session.name;
  } else {
    return session.teamId;
  }
});

const avatarId = computed(() => {
  const { session } = props;
  if (session.scene === 'p2p') {
    const { to } = props.session as NimKitCoreTypes.P2PSession;
    return to;
  }
  const { teamId } = props.session as NimKitCoreTypes.TeamSession;
  return teamId;
});

const content = computed(() => {
  const lastMsg = props.session.lastMsg;
  if (lastMsg) {
    const { status } = lastMsg;
    if (status === 'sending' || lastMsg.type === 'notification') {
      return '';
    }
    if (status === 'sendFailed' || status === 'refused') {
      // TODO:
      return '[发送失败]';
    }
    return getMsgContentTipByType(lastMsg);
  }
  return '';
});

const date = computed(() => {
  const time = props.session.lastMsg?.time || props.session.updateTime;
  // 如果最后一条消息时间戳不存在，则会话列表不显示
  if (!time) {
    return '';
  }
  const _d = dayjs(time);
  const isCurrentDay = _d.isSame(dayjs(), 'day');
  const isCurrentYear = _d.isSame(dayjs(), 'year');
  return _d.format(
    isCurrentDay ? 'HH:mm' : isCurrentYear ? 'MM-DD HH:mm' : 'YYYY-MM-DD HH:mm',
  );
});

const max = 99;

const unread = computed(() => {
  return props.session.unread > 0
    ? props.session.unread > max
      ? `${max}+`
      : props.session.unread + ''
    : '';
});

const isMute = computed(() => {
  return props.session.isMute;
});

const beMentioned = computed(() => {
  return props.session.beMentioned;
});

// 左滑显示 action 动画
let startX = 0,
  startY = 0;
// 开始左滑
function handleTouchStart(event: TouchEvent) {
  startX = event.changedTouches[0].pageX;
  startY = event.changedTouches[0].pageY;
}

function handleTouchMove(event: TouchEvent) {
  const moveEndX = event.changedTouches[0].pageX;
  const moveEndY = event.changedTouches[0].pageY;
  const X = moveEndX - startX + 20;
  const Y = moveEndY - startY;
  if (Math.abs(X) > Math.abs(Y) && X > 0) {
    emit('leftSlide', null);
  } else if (Math.abs(X) > Math.abs(Y) && X < 0) {
    emit('leftSlide', props.session);
  }
}

function handleConversationItemClick() {
  if (props.showMoreActions) {
    emit('leftSlide', null);
    return;
  }
  emit('click', props.session);
}

onUpdated(() => {
  console.log('onUpdated', props.session.unread);
});
</script>

<style lang="scss" scoped>
$cellHeight: 144rpx;

.conversation-item-container {
  position: relative;
  transition: transform 0.3s;
  &:active {
    background-color: #f4f4f4;
  }
  &.show-action-list {
    transform: translateX(-200px);
  }

  &.stick-on-top {
    background: #f3f5f7;
  }

  .beMentioned {
    color: #ff4d4f;
  }

  .content {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.right-action-list {
  position: absolute;
  top: 0;
  right: -200px;
  bottom: 0;
  width: 200px;

  .right-action-item {
    width: 100px;
    display: inline-block;
    color: #fff;
    text-align: center;
    height: $cellHeight;
    line-height: $cellHeight;
  }

  .action-top {
    background: #337eff;
  }

  .action-delete {
    background: #a8abb6;
  }
}

.conversation-item-content {
  display: flex;
  align-items: center;
  padding: 24rpx 40rpx;
  height: $cellHeight;
  box-sizing: border-box;

  border-bottom: solid 2rpx #ECECEC;
}

.conversation-item-left {
  position: relative;

  .conversation-item-badge {
    position: absolute;
    top: 0px;
    right: 0px;
    z-index: 10;
  }
}

.conversation-item-right {
  flex: 1;
  width: 0;
  margin-left: 20rpx;
}

.conversation-item-top {
  margin-bottom: 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .conversation-item-title {
    overflow: hidden; //超出的文本隐藏
    text-overflow: ellipsis; //溢出用省略号显示
    white-space: nowrap; //溢出不换行

    color:  #1B1B1B;
    font-size: 24rpx;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0.48px;
  }

  .conversation-item-time {
    font-size: 20rpx;
    color: #C4C4C4;
    text-align: right;
    width: 180rpx;
    flex-shrink: 0;

    letter-spacing: 0.4px;
  }
}

.conversation-item-desc {
  font-size: 20rpx;
  color: #969696;
  letter-spacing: 0.4px;

  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;

  .conversation-item-desc-content {
    overflow: hidden; //超出的文本隐藏
    text-overflow: ellipsis; //溢出用省略号显示
    white-space: nowrap; //溢出不换行
    flex: 1;
  }

  .conversation-item-desc-state {
    margin-left: 20rpx;
  }
}

.dot {
  color: #fff;
  width: 20rpx;
  height: 20rpx;
  border-radius: 5rpx;
  box-sizing: border-box;
  z-index: 99;

  background:  linear-gradient(87deg, #FF002E 3.31%, #FFC0C0 142.11%);
  box-shadow: 0px 0px 10rpx 0px rgba(255, 255, 255, 0.60) inset;
}

.badge {
  background-color: #ff4d4f;
  color: #fff;
  font-size: 24rpx;
  min-width: 36rpx;
  height: 36rpx;
  line-height: 34rpx;
  border-radius: 48rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
  text-align: center;
  z-index: 99;
  position: relative;

  background:  linear-gradient(87deg, #FF002E 3.31%, #FFC0C0 142.11%);
  box-shadow: 0px 0px 10rpx 0px rgba(255, 255, 255, 0.60) inset;
}

.unread {
  position: absolute;
  right: -10rpx;
  top:-10rpx;
  z-index: 99;
}
</style>
