body,
div,
ul,
ol,
dl,
dt,
dd,
li,
dl,
h1,
h2,
h3,
h4,
p {
  margin: 0;
  padding: 0;
  font-family: 'PingFang SC';
  font-style: normal;
}
ol,
ul,
li {
  list-style: none;
}
img {
  border: 0;
  vertical-align: middle;
}
body {
  color: #000000;
  background: #fff;
}
.clear {
  clear: both;
  height: 1px;
  width: 100%;
  overflow: hidden;
  margin-top: -1px;
}
a {
  color: #000000;
  text-decoration: none;
}
a:hover {
  text-decoration: none;
}

@keyframes loadingCircle {
  100% {
    transform: rotate(360deg);
  }
}

.ok-btn {
  color: #fff;
  background-color: rgb(20, 146, 209);
  padding: 10px;
  font-size: 16px;
  text-align: center;
  position: fixed;
  bottom: 0;
  left: calc(50% - 25vw);
  width: 50vw;
  border-radius: 3px;
}

.ok-btn-mp {
  color: #fff;
  background-color: rgb(20, 146, 209);
  padding: 10px;
  font-size: 16px;
  text-align: center;
  position: fixed;
  bottom: 20px;
  left: calc(50% - 25vw);
  width: 50vw;
  border-radius: 3px;
}

.button-box-mp {
  display: flex;
  align-items: center;
  position: fixed;
  z-index: 1;
  bottom: 30px;
  right: 30px;
}

.button-box {
  display: flex;
  align-items: center;
  position: relative;
}
.button-box,
.button-box-mp .button-icon {
  width: 40px;
  height: 40px;
  margin-left: 20px;
}
