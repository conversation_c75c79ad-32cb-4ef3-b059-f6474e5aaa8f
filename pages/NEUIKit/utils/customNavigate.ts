const preUrl = '/pages/NEUIKit';

export function customNavigateTo(options: { url: string }) {
  uni.navigateTo({ ...options, url: preUrl + options.url });
}

export function customRedirectTo(options: { url: string }) {
  uni.redirectTo({ ...options, url: preUrl + options.url });
}

export function customSwitchTab(options: { url: string }) {
  // #ifndef MP
  uni.switchTab({ ...options, url: preUrl + options.url });
  // #else
  uni.navigateTo({ ...options, url: preUrl + options.url });
  // #endif
}

export function customReLaunch(options: { url: string }) {
  uni.reLaunch({ ...options, url: preUrl + options.url });
}

export function customNavigateBack(size = 1) {
  uni.navigateBackCustom(size);
}
