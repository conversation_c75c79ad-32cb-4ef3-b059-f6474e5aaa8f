{"heroList": [{"heroId": 531, "heroType": "刺客", "name": "镜"}, {"heroId": 518, "heroType": "战士", "name": "马超"}, {"heroId": 502, "heroType": "刺客", "name": "裴擒虎"}, {"heroId": 159, "heroType": "辅助", "name": "朵莉亚"}, {"heroId": 184, "heroType": "辅助", "name": "蔡文姬"}, {"heroId": 505, "heroType": "辅助", "name": "瑶"}, {"heroId": 534, "heroType": "辅助", "name": "桑启"}, {"heroId": 176, "heroType": "法师", "name": "杨玉环"}, {"heroId": 157, "heroType": "刺客", "name": "不知火舞"}, {"heroId": 118, "heroType": "辅助", "name": "孙膑"}, {"heroId": 199, "heroType": "射手", "name": "公孙离"}, {"heroId": 109, "heroType": "法师", "name": "妲己"}, {"heroId": 515, "heroType": "法师", "name": "嫦娥"}, {"heroId": 152, "heroType": "法师", "name": "王昭君"}, {"heroId": 111, "heroType": "射手", "name": "孙尚香"}, {"heroId": 521, "heroType": "法师", "name": "海月"}, {"heroId": 537, "heroType": "战士", "name": "司空震"}, {"heroId": 189, "heroType": "辅助", "name": "鬼谷子"}, {"heroId": 171, "heroType": "辅助", "name": "张飞"}, {"heroId": 132, "heroType": "射手", "name": "马可波罗"}, {"heroId": 105, "heroType": "坦克", "name": "廉颇"}, {"heroId": 538, "heroType": "战士", "name": "云缨"}, {"heroId": 106, "heroType": "法师", "name": "小乔"}, {"heroId": 519, "heroType": "射手", "name": "敖隐"}, {"heroId": 150, "heroType": "刺客", "name": "韩信"}, {"heroId": 155, "heroType": "射手", "name": "艾琳"}, {"heroId": 136, "heroType": "法师", "name": "武则天"}, {"heroId": 174, "heroType": "射手", "name": "虞姬"}, {"heroId": 137, "heroType": "刺客", "name": "司马懿"}, {"heroId": 113, "heroType": "辅助", "name": "庄周"}, {"heroId": 131, "heroType": "刺客", "name": "<PERSON>白"}, {"heroId": 523, "heroType": "法师", "name": "西施"}, {"heroId": 141, "heroType": "法师", "name": "貂蝉"}, {"heroId": 154, "heroType": "战士", "name": "花木兰"}, {"heroId": 501, "heroType": "辅助", "name": "明世隐"}, {"heroId": 517, "heroType": "战士", "name": "大司命"}, {"heroId": 133, "heroType": "射手", "name": "狄仁杰"}, {"heroId": 169, "heroType": "射手", "name": "后羿"}, {"heroId": 110, "heroType": "法师", "name": "嬴政"}, {"heroId": 168, "heroType": "辅助", "name": "牛魔"}, {"heroId": 125, "heroType": "刺客", "name": "元歌"}, {"heroId": 190, "heroType": "法师", "name": "诸葛亮"}, {"heroId": 528, "heroType": "刺客", "name": "澜"}, {"heroId": 510, "heroType": "战士", "name": "孙策"}, {"heroId": 140, "heroType": "战士", "name": "关羽"}, {"heroId": 536, "heroType": "战士", "name": "夏洛特"}, {"heroId": 130, "heroType": "战士", "name": "宫本武藏"}, {"heroId": 167, "heroType": "刺客", "name": "孙悟空"}, {"heroId": 196, "heroType": "射手", "name": "百里守约"}, {"heroId": 170, "heroType": "战士", "name": "刘备"}, {"heroId": 524, "heroType": "射手", "name": "蒙犽"}, {"heroId": 581, "heroType": "坦克", "name": "元流之子(坦克)"}, {"heroId": 123, "heroType": "战士", "name": "吕布"}, {"heroId": 513, "heroType": "法师", "name": "上官婉儿"}, {"heroId": 126, "heroType": "坦克", "name": "夏侯惇"}, {"heroId": 195, "heroType": "刺客", "name": "百里玄策"}, {"heroId": 191, "heroType": "辅助", "name": "大乔"}, {"heroId": 522, "heroType": "战士", "name": "曜"}, {"heroId": 178, "heroType": "战士", "name": "杨戬"}, {"heroId": 182, "heroType": "法师", "name": "干将莫邪"}, {"heroId": 146, "heroType": "刺客", "name": "露娜"}, {"heroId": 134, "heroType": "战士", "name": "达摩"}, {"heroId": 149, "heroType": "坦克", "name": "刘邦"}, {"heroId": 525, "heroType": "辅助", "name": "鲁班大师"}, {"heroId": 107, "heroType": "战士", "name": "赵云"}, {"heroId": 115, "heroType": "法师", "name": "高渐离"}, {"heroId": 121, "heroType": "法师", "name": "芈月"}, {"heroId": 108, "heroType": "法师", "name": "墨子"}, {"heroId": 175, "heroType": "辅助", "name": "钟馗"}, {"heroId": 148, "heroType": "法师", "name": "姜子牙"}, {"heroId": 153, "heroType": "刺客", "name": "兰陵王"}, {"heroId": 508, "heroType": "射手", "name": "伽罗"}, {"heroId": 162, "heroType": "刺客", "name": "娜可露露"}, {"heroId": 116, "heroType": "刺客", "name": "阿轲"}, {"heroId": 193, "heroType": "战士", "name": "铠"}, {"heroId": 194, "heroType": "辅助", "name": "苏烈"}, {"heroId": 504, "heroType": "法师", "name": "米莱狄"}, {"heroId": 142, "heroType": "法师", "name": "安琪拉"}, {"heroId": 163, "heroType": "刺客", "name": "橘右京"}, {"heroId": 114, "heroType": "辅助", "name": "刘禅"}, {"heroId": 139, "heroType": "战士", "name": "老夫子"}, {"heroId": 186, "heroType": "辅助", "name": "太乙真人"}, {"heroId": 124, "heroType": "法师", "name": "周瑜"}, {"heroId": 192, "heroType": "射手", "name": "黄忠"}, {"heroId": 527, "heroType": "坦克", "name": "蒙恬"}, {"heroId": 127, "heroType": "法师", "name": "甄姬"}, {"heroId": 183, "heroType": "战士", "name": "雅典娜"}, {"heroId": 128, "heroType": "战士", "name": "曹操"}, {"heroId": 173, "heroType": "射手", "name": "李元芳"}, {"heroId": 177, "heroType": "射手", "name": "苍"}, {"heroId": 509, "heroType": "辅助", "name": "盾山"}, {"heroId": 120, "heroType": "坦克", "name": "白起"}, {"heroId": 312, "heroType": "法师", "name": "沈梦溪"}, {"heroId": 507, "heroType": "战士", "name": "李信"}, {"heroId": 540, "heroType": "法师", "name": "金蝉"}, {"heroId": 144, "heroType": "坦克", "name": "程咬金"}, {"heroId": 166, "heroType": "战士", "name": "亚瑟"}, {"heroId": 187, "heroType": "辅助", "name": "东皇太一"}, {"heroId": 197, "heroType": "法师", "name": "弈星"}, {"heroId": 503, "heroType": "战士", "name": "狂铁"}, {"heroId": 112, "heroType": "射手", "name": "鲁班七号"}, {"heroId": 129, "heroType": "战士", "name": "典韦"}, {"heroId": 135, "heroType": "坦克", "name": "项羽"}, {"heroId": 511, "heroType": "坦克", "name": "猪八戒"}, {"heroId": 119, "heroType": "法师", "name": "扁鹊"}, {"heroId": 156, "heroType": "法师", "name": "张良"}, {"heroId": 506, "heroType": "刺客", "name": "云中君"}, {"heroId": 545, "heroType": "射手", "name": "莱西奥"}, {"heroId": 117, "heroType": "战士", "name": "钟无艳"}, {"heroId": 544, "heroType": "战士", "name": "赵怀真"}, {"heroId": 563, "heroType": "法师", "name": "海诺"}, {"heroId": 180, "heroType": "战士", "name": "哪吒"}, {"heroId": 198, "heroType": "坦克", "name": "梦奇"}, {"heroId": 529, "heroType": "战士", "name": "盘古"}, {"heroId": 533, "heroType": "坦克", "name": "阿古朵"}, {"heroId": 564, "heroType": "战士", "name": "姬小满"}, {"heroId": 179, "heroType": "法师", "name": "女娲"}, {"heroId": 514, "heroType": "战士", "name": "亚连"}, {"heroId": 542, "heroType": "刺客", "name": "暃"}, {"heroId": 548, "heroType": "射手", "name": "戈娅"}, {"heroId": 558, "heroType": "战士", "name": "影"}, {"heroId": 577, "heroType": "辅助", "name": "少司缘"}, {"heroId": 582, "heroType": "法师", "name": "元流之子(法师)"}], "skinList": [{"skinId": 18606, "skinName": "谧流熔炉", "heroId": "186", "heroName": "太乙真人", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S38赛季专属", "skinNum": 5}, {"skinId": 16806, "skinName": "星界战将", "heroId": "168", "heroName": "牛魔", "classTypeName": ["史诗品质", "赛季限定", "限定", "王者之证", "星界纪事"], "heroType": "辅助", "accessWay": "时空之境获取", "skinNum": 6}, {"skinId": 51005, "skinName": "时之奇旅", "heroId": "510", "heroName": "孙策", "classTypeName": ["限定", "时之奇旅", "珍品限定", "珍品传说"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 6}, {"skinId": 51806, "skinName": "访茗客", "heroId": "518", "heroName": "马超", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 5}, {"skinId": 11808, "skinName": "茶境仙", "heroId": "118", "heroName": "孙膑", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "辅助", "accessWay": "荣耀战令获取", "skinNum": 8}, {"skinId": 56405, "skinName": "飞车小橘子", "heroId": "564", "heroName": "姬小满", "classTypeName": ["史诗品质"], "heroType": "战士", "accessWay": "", "skinNum": 4}, {"skinId": 11005, "skinName": "玄雷天君", "heroId": "110", "heroName": "嬴政", "classTypeName": ["传说品质", "限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 55801, "skinName": "魅影绮裳", "heroId": "558", "heroName": "影", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 1}, {"skinId": 12907, "skinName": "铁骨偃魂", "heroId": "129", "heroName": "典韦", "classTypeName": ["勇者品质", "活动专属"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 7}, {"skinId": 17704, "skinName": "苍林狼骑", "heroId": "177", "heroName": "苍", "classTypeName": ["勇者品质", "活动专属"], "heroType": "射手", "accessWay": "限时活动获取", "skinNum": 2}, {"skinId": 18207, "skinName": "雾都夜雨", "heroId": "182", "heroName": "干将莫邪", "classTypeName": ["勇者品质", "活动专属"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 7}, {"skinId": 11211, "skinName": "江户川柯南", "heroId": "112", "heroName": "鲁班七号", "classTypeName": ["限定", "珍品限定", "名侦探柯南", "珍品传说"], "heroType": "射手", "accessWay": "限时活动获取", "skinNum": 11}, {"skinId": 12310, "skinName": "逐霄战戟", "heroId": "123", "heroName": "吕布", "classTypeName": ["勇者品质", "限定", "KPL限定"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 13209, "skinName": "怪盗基德", "heroId": "132", "heroName": "马可波罗", "classTypeName": ["限定", "珍品限定", "名侦探柯南", "珍品传说"], "heroType": "射手", "accessWay": "限时活动获取", "skinNum": 9}, {"skinId": 19607, "skinName": "百相守梦", "heroId": "196", "heroName": "百里守约", "classTypeName": ["史诗品质", "战令限定", "限定", "百相守梦"], "heroType": "射手", "accessWay": "荣耀战令获取", "skinNum": 6}, {"skinId": 13906, "skinName": "百相守梦", "heroId": "139", "heroName": "老夫子", "classTypeName": ["勇者品质", "战令限定", "限定", "百相守梦"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 6}, {"skinId": 50406, "skinName": "怪诞之夜", "heroId": "504", "heroName": "米莱狄", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 56402, "skinName": "妄想食味", "heroId": "564", "heroName": "姬小满", "classTypeName": ["史诗品质", "限定", "珍宝阁专属", "妄想都市"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 4}, {"skinId": 17602, "skinName": "遇见飞天", "heroId": "176", "heroName": "杨玉环", "classTypeName": ["史诗品质", "限定", "周年限定", "敦煌研究院"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 4}, {"skinId": 53404, "skinName": "鸣野蒿", "heroId": "534", "heroName": "桑启", "classTypeName": ["传说品质", "限定", "周年限定", "草木风华"], "heroType": "辅助", "accessWay": "限时活动获取", "skinNum": 4}, {"skinId": 15210, "skinName": "映山客", "heroId": "152", "heroName": "王昭君", "classTypeName": ["传说品质", "限定", "周年限定", "草木风华"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 15505, "skinName": "陌上桑", "heroId": "155", "heroName": "艾琳", "classTypeName": ["史诗品质", "限定", "周年限定", "草木风华"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 18704, "skinName": "噬灭天穹", "heroId": "187", "heroName": "东皇太一", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S37赛季专属", "skinNum": 4}, {"skinId": 11308, "skinName": "牧神诗旅", "heroId": "113", "heroName": "庄周", "classTypeName": ["传说品质"], "heroType": "辅助", "accessWay": "", "skinNum": 8}, {"skinId": 13208, "skinName": "星界特工", "heroId": "132", "heroName": "马可波罗", "classTypeName": ["史诗品质", "星界纪事"], "heroType": "射手", "accessWay": "", "skinNum": 9}, {"skinId": 16608, "skinName": "动物派对", "heroId": "166", "heroName": "亚瑟", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 13109, "skinName": "谪仙醉月", "heroId": "131", "heroName": "<PERSON>白", "classTypeName": ["史诗品质", "限定"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 52905, "skinName": "敬我三分", "heroId": "529", "heroName": "盘古", "classTypeName": ["勇者品质", "限定"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 5}, {"skinId": 10505, "skinName": "撼地雄心", "heroId": "105", "heroName": "廉颇", "classTypeName": ["史诗品质", "战令限定", "限定", "破界"], "heroType": "坦克", "accessWay": "荣耀战令获取", "skinNum": 5}, {"skinId": 15601, "skinName": "天堂福音", "heroId": "156", "heroName": "张良", "classTypeName": [], "heroType": "法师", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 14403, "skinName": "华尔街大亨", "heroId": "144", "heroName": "程咬金", "classTypeName": [], "heroType": "坦克", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 19704, "skinName": "炽弈燎原", "heroId": "197", "heroName": "弈星", "classTypeName": ["史诗品质", "限定", "KPL限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 4}, {"skinId": 51404, "skinName": "破空之剑", "heroId": "514", "heroName": "亚连", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 3}, {"skinId": 16711, "skinName": "神迹守卫", "heroId": "167", "heroName": "孙悟空", "classTypeName": ["勇者品质", "战令限定", "限定", "神迹守卫"], "heroType": "刺客", "accessWay": "荣耀战令获取", "skinNum": 10}, {"skinId": 50506, "skinName": "拾光映像", "heroId": "505", "heroName": "瑶", "classTypeName": ["荣耀典藏"], "heroType": "辅助", "accessWay": "积分夺宝获取", "skinNum": 6}, {"skinId": 14804, "skinName": "天穹之誓", "heroId": "148", "heroName": "姜子牙", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "法师", "accessWay": "S36赛季专属", "skinNum": 4}, {"skinId": 56404, "skinName": "战舞者", "heroId": "564", "heroName": "姬小满", "classTypeName": ["史诗品质", "破界"], "heroType": "战士", "accessWay": "", "skinNum": 4}, {"skinId": 57702, "skinName": "涂山红红", "heroId": "577", "heroName": "少司缘", "classTypeName": ["史诗品质", "限定", "七夕限定"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 1}, {"skinId": 51702, "skinName": "东方月初", "heroId": "517", "heroName": "大司命", "classTypeName": ["史诗品质", "限定", "七夕限定"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 2}, {"skinId": 52406, "skinName": "顽岩魄", "heroId": "524", "heroName": "蒙犽", "classTypeName": ["史诗品质", "限定", "活动专属"], "heroType": "射手", "accessWay": "限时活动获取", "skinNum": 6}, {"skinId": 19907, "skinName": "离恨烟", "heroId": "199", "heroName": "公孙离", "classTypeName": ["无双", "珍品限定", "墨染江湖"], "heroType": "射手", "accessWay": "限时活动获取", "skinNum": 7}, {"skinId": 10903, "skinName": "仙境爱丽丝", "heroId": "109", "heroName": "妲己", "classTypeName": [], "heroType": "法师", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 18407, "skinName": "夏日便利店", "heroId": "184", "heroName": "蔡文姬", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "辅助", "accessWay": "荣耀战令获取", "skinNum": 7}, {"skinId": 12608, "skinName": "霜北刀", "heroId": "126", "heroName": "夏侯惇", "classTypeName": ["传说品质", "限定", "墨染江湖"], "heroType": "坦克", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 54203, "skinName": "埋骨钱", "heroId": "542", "heroName": "暃", "classTypeName": ["史诗品质", "限定", "墨染江湖"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 3}, {"skinId": 19306, "skinName": "冥王哈迪斯", "heroId": "193", "heroName": "铠", "classTypeName": ["传说品质", "限定", "圣域传说"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 19107, "skinName": "绒语心约", "heroId": "191", "heroName": "大乔", "classTypeName": ["勇者品质"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 17409, "skinName": "夏日便利店", "heroId": "174", "heroName": "虞姬", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "射手", "accessWay": "荣耀战令获取", "skinNum": 8}, {"skinId": 15703, "skinName": "花合斗", "heroId": "157", "heroName": "不知火舞", "classTypeName": ["无双", "珍品限定", "如愿武道会"], "heroType": "刺客", "accessWay": "限时活动获取", "skinNum": 3}, {"skinId": 12406, "skinName": "熔金海岸", "heroId": "124", "heroName": "周瑜", "classTypeName": ["勇者品质", "夏日海滩", "活动专属"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 6}, {"skinId": 58200, "skinName": "万妙之心", "heroId": "582", "heroName": "元流之子(法师)", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 1}, {"skinId": 58100, "skinName": "止戈之道", "heroId": "581", "heroName": "元流之子(坦克)", "classTypeName": [], "heroType": "坦克", "accessWay": "", "skinNum": 1}, {"skinId": 14202, "skinName": "魔法小厨娘", "heroId": "142", "heroName": "安琪拉", "classTypeName": ["史诗品质", "限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 17808, "skinName": "破阵·退雄兵", "heroId": "178", "heroName": "杨戬", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 6}, {"skinId": 14107, "skinName": "长夏之忆", "heroId": "141", "heroName": "貂蝉", "classTypeName": ["勇者品质", "夏日海滩"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 12107, "skinName": "浮光幕影", "heroId": "121", "heroName": "芈月", "classTypeName": ["史诗品质", "限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 17008, "skinName": "异域游侠", "heroId": "170", "heroName": "刘备", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 53106, "skinName": "青焰无极", "heroId": "531", "heroName": "镜", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "刺客", "accessWay": "荣耀战令获取", "skinNum": 6}, {"skinId": 19206, "skinName": "怒海争锋", "heroId": "192", "heroName": "黄忠", "classTypeName": ["史诗品质"], "heroType": "射手", "accessWay": "", "skinNum": 4}, {"skinId": 51701, "skinName": "暗都幽影", "heroId": "517", "heroName": "大司命", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 2}, {"skinId": 56302, "skinName": "心动手记", "heroId": "563", "heroName": "海诺", "classTypeName": ["史诗品质", "限定", "520限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 2}, {"skinId": 15902, "skinName": "心动手记", "heroId": "159", "heroName": "朵莉亚", "classTypeName": ["传说品质", "限定", "520限定"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 2}, {"skinId": 50105, "skinName": "夜落电台", "heroId": "501", "heroName": "明世隐", "classTypeName": ["史诗品质"], "heroType": "辅助", "accessWay": "", "skinNum": 5}, {"skinId": 14008, "skinName": "决胜骁骑", "heroId": "140", "heroName": "关羽", "classTypeName": ["限定", "KPL限定"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 14409, "skinName": "群星魔术团", "heroId": "144", "heroName": "程咬金", "classTypeName": ["勇者品质", "五五朋友节", "活动专属"], "heroType": "坦克", "accessWay": "限时活动获取", "skinNum": 9}, {"skinId": 11101, "skinName": "火炮千金", "heroId": "111", "heroName": "孙尚香", "classTypeName": [], "heroType": "射手", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 13508, "skinName": "苍威无极", "heroId": "135", "heroName": "项羽", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "坦克", "accessWay": "荣耀战令获取", "skinNum": 8}, {"skinId": 15008, "skinName": "群星魔术团", "heroId": "150", "heroName": "韩信", "classTypeName": ["无双", "珍品限定", "群星魔术团"], "heroType": "刺客", "accessWay": "限时活动获取", "skinNum": 8}, {"skinId": 50604, "skinName": "群星魔术团", "heroId": "506", "heroName": "云中君", "classTypeName": ["传说品质", "限定", "五五朋友节", "群星魔术团"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 4}, {"skinId": 15603, "skinName": "幽兰居士", "heroId": "156", "heroName": "张良", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 51306, "skinName": "群星魔术团", "heroId": "513", "heroName": "上官婉儿", "classTypeName": ["史诗品质", "限定", "五五朋友节", "群星魔术团"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 10806, "skinName": "黄金天蝎座", "heroId": "108", "heroName": "墨子", "classTypeName": ["传说品质", "限定", "圣域传说"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 14906, "skinName": "剑破天穹", "heroId": "149", "heroName": "刘邦", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "坦克", "accessWay": "S35赛季专属", "skinNum": 5}, {"skinId": 53403, "skinName": "奇遇星旅", "heroId": "534", "heroName": "桑启", "classTypeName": ["史诗品质", "梦幻童话"], "heroType": "辅助", "accessWay": "", "skinNum": 4}, {"skinId": 52904, "skinName": "经纬探寻者", "heroId": "529", "heroName": "盘古", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 11603, "skinName": "致命风华", "heroId": "116", "heroName": "阿轲", "classTypeName": ["勇者品质"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 52104, "skinName": "金乌负日", "heroId": "521", "heroName": "海月", "classTypeName": ["传说品质", "限定", "神州地方志"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 4}, {"skinId": 11904, "skinName": "无尽旅途", "heroId": "119", "heroName": "扁鹊", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 11210, "skinName": "蔬乡物语", "heroId": "112", "heroName": "鲁班七号", "classTypeName": ["史诗品质", "战令限定", "限定", "蔬菜精灵"], "heroType": "射手", "accessWay": "荣耀战令获取", "skinNum": 11}, {"skinId": 11706, "skinName": "春野之旅", "heroId": "117", "heroName": "钟无艳", "classTypeName": ["勇者品质", "蔬菜精灵"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 6}, {"skinId": 31207, "skinName": "龙舞盛年", "heroId": "312", "heroName": "沈梦溪", "classTypeName": ["勇者品质", "限定", "活动专属"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 7}, {"skinId": 17309, "skinName": "蔬乡物语", "heroId": "173", "heroName": "李元芳", "classTypeName": ["勇者品质", "战令限定", "限定", "蔬菜精灵"], "heroType": "射手", "accessWay": "荣耀战令获取", "skinNum": 9}, {"skinId": 51402, "skinName": "落雪白狼", "heroId": "514", "heroName": "亚连", "classTypeName": ["传说品质", "限定", "太古仙侠传"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 3}, {"skinId": 12309, "skinName": "曦玄引", "heroId": "123", "heroName": "吕布", "classTypeName": ["传说品质", "限定", "情人节限定"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 14111, "skinName": "曦玄引", "heroId": "141", "heroName": "貂蝉", "classTypeName": ["传说品质", "限定", "情人节限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 19505, "skinName": "超元猎域", "heroId": "195", "heroName": "百里玄策", "classTypeName": ["史诗品质", "限定"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 10609, "skinName": "时之魔女", "heroId": "106", "heroName": "小乔", "classTypeName": ["时之奇旅", "无双", "珍品限定"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 9}, {"skinId": 17408, "skinName": "神鉴启示录", "heroId": "174", "heroName": "虞姬", "classTypeName": ["荣耀典藏"], "heroType": "射手", "accessWay": "积分夺宝获取", "skinNum": 8}, {"skinId": 12906, "skinName": "战鼓燎原", "heroId": "129", "heroName": "典韦", "classTypeName": ["传说品质"], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 19109, "skinName": "乘龙·忆丹青", "heroId": "191", "heroName": "大乔", "classTypeName": ["史诗品质", "限定", "生肖限定"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 14207, "skinName": "乘龙·聚宝船", "heroId": "142", "heroName": "安琪拉", "classTypeName": ["史诗品质", "限定", "生肖限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 10712, "skinName": "乘龙·铭钟鼎", "heroId": "107", "heroName": "赵云", "classTypeName": ["传说品质", "限定", "生肖限定"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 51006, "skinName": "乘龙·淬吴钩", "heroId": "510", "heroName": "孙策", "classTypeName": ["传说品质", "限定", "生肖限定"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 11110, "skinName": "乘龙·问璇玑", "heroId": "111", "heroName": "孙尚香", "classTypeName": ["传说品质", "限定", "生肖限定"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 12808, "skinName": "夜都魔契", "heroId": "128", "heroName": "曹操", "classTypeName": ["史诗品质", "限定"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 7}, {"skinId": 16606, "skinName": "鸿运当头", "heroId": "166", "heroName": "亚瑟", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 15901, "skinName": "金色潮汐", "heroId": "159", "heroName": "朵莉亚", "classTypeName": ["勇者品质"], "heroType": "辅助", "accessWay": "", "skinNum": 2}, {"skinId": 10504, "skinName": "功夫炙烤", "heroId": "105", "heroName": "廉颇", "classTypeName": ["勇者品质"], "heroType": "坦克", "accessWay": "钻石夺宝获取", "skinNum": 5}, {"skinId": 51805, "skinName": "琥珀纪元", "heroId": "518", "heroName": "马超", "classTypeName": ["传说品质", "限定", "琥珀纪元"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 50806, "skinName": "琥珀纪元", "heroId": "508", "heroName": "伽罗", "classTypeName": ["史诗品质", "限定", "琥珀纪元"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 51505, "skinName": "漠中幻影", "heroId": "515", "heroName": "嫦娥", "classTypeName": ["勇者品质", "限定"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 5}, {"skinId": 19307, "skinName": "琥珀纪元", "heroId": "193", "heroName": "铠", "classTypeName": ["传说品质", "限定", "琥珀纪元"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 13108, "skinName": "碎月剑心", "heroId": "131", "heroName": "<PERSON>白", "classTypeName": ["无双", "珍品限定"], "heroType": "刺客", "accessWay": "限时活动获取", "skinNum": 8}, {"skinId": 11505, "skinName": "燃音魔法", "heroId": "115", "heroName": "高渐离", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 5}, {"skinId": 56301, "skinName": "时空谍影", "heroId": "563", "heroName": "海诺", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "", "skinNum": 2}, {"skinId": 18905, "skinName": "天穹祈灯", "heroId": "189", "heroName": "鬼谷子", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S34赛季专属", "skinNum": 5}, {"skinId": 18002, "skinName": "逐梦之翼", "heroId": "180", "heroName": "哪吒", "classTypeName": ["史诗品质", "限定", "KPL限定"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 10910, "skinName": "灵卜魔法", "heroId": "109", "heroName": "妲己", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 10}, {"skinId": 52703, "skinName": "荣光圣徽", "heroId": "527", "heroName": "蒙恬", "classTypeName": ["史诗品质", "赛季限定", "限定", "王者之证"], "heroType": "坦克", "accessWay": "时空之境获取", "skinNum": 3}, {"skinId": 15503, "skinName": "觅芳踪", "heroId": "155", "heroName": "艾琳", "classTypeName": ["传说品质", "限定", "贵族限定"], "heroType": "射手", "accessWay": "贵族10级赠送", "skinNum": 5}, {"skinId": 12405, "skinName": "雪夜绮愿", "heroId": "124", "heroName": "周瑜", "classTypeName": ["史诗品质", "限定", "圣诞颂歌"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 52804, "skinName": "逐花归海", "heroId": "528", "heroName": "澜", "classTypeName": ["传说品质", "FMVP"], "heroType": "刺客", "accessWay": "", "skinNum": 4}, {"skinId": 14408, "skinName": "暖冬絮语", "heroId": "144", "heroName": "程咬金", "classTypeName": ["勇者品质", "圣诞颂歌"], "heroType": "坦克", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 16908, "skinName": "完美运算", "heroId": "169", "heroName": "后羿", "classTypeName": ["勇者品质", "活动专属"], "heroType": "射手", "accessWay": "限时活动获取", "skinNum": 8}, {"skinId": 52405, "skinName": "百解令", "heroId": "524", "heroName": "蒙犽", "classTypeName": ["史诗品质", "限定", "源梦皮肤"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 14607, "skinName": "霜月吟", "heroId": "146", "heroName": "露娜", "classTypeName": ["传说品质", "世冠皮肤"], "heroType": "刺客", "accessWay": "", "skinNum": 7}, {"skinId": 15007, "skinName": "弑枪猎影", "heroId": "150", "heroName": "韩信", "classTypeName": ["荣耀典藏"], "heroType": "刺客", "accessWay": "积分夺宝获取", "skinNum": 8}, {"skinId": 11807, "skinName": "小动物乐团", "heroId": "118", "heroName": "孙膑", "classTypeName": ["传说品质"], "heroType": "辅助", "accessWay": "", "skinNum": 8}, {"skinId": 13308, "skinName": "夜礼服假面", "heroId": "133", "heroName": "狄仁杰", "classTypeName": ["传说品质", "限定", "美少女战士"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 15209, "skinName": "永恒水手月亮", "heroId": "152", "heroName": "王昭君", "classTypeName": ["传说品质", "限定", "美少女战士"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 12807, "skinName": "决胜大满贯", "heroId": "128", "heroName": "曹操", "classTypeName": ["勇者品质", "青春校园"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 53702, "skinName": "地狱燃心", "heroId": "537", "heroName": "司空震", "classTypeName": ["史诗品质", "战令限定", "限定", "地狱火"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 2}, {"skinId": 14803, "skinName": "闲日渔趣", "heroId": "148", "heroName": "姜子牙", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "", "skinNum": 4}, {"skinId": 15307, "skinName": "幻夜行动", "heroId": "153", "heroName": "兰陵王", "classTypeName": ["勇者品质"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 52306, "skinName": "至美·乘鲤谣", "heroId": "523", "heroName": "西施", "classTypeName": ["史诗品质", "限定", "至美风华"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 53303, "skinName": "江河有灵", "heroId": "533", "heroName": "阿古朵", "classTypeName": ["史诗品质", "限定", "周年限定"], "heroType": "坦克", "accessWay": "活动获取", "skinNum": 3}, {"skinId": 12707, "skinName": "至美·化雀舞", "heroId": "127", "heroName": "甄姬", "classTypeName": ["传说品质", "限定", "至美风华"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 15607, "skinName": "古海寻踪", "heroId": "156", "heroName": "张良", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "法师", "accessWay": "S33赛季专属", "skinNum": 7}, {"skinId": 19108, "skinName": "时之奇旅", "heroId": "191", "heroName": "大乔", "classTypeName": ["传说品质", "限定", "时之奇旅"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 11209, "skinName": "时之奇旅", "heroId": "112", "heroName": "鲁班七号", "classTypeName": ["传说品质", "限定", "时之奇旅"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 11}, {"skinId": 51401, "skinName": "破局者", "heroId": "514", "heroName": "亚连", "classTypeName": ["勇者品质", "胡桃异想国"], "heroType": "战士", "accessWay": "", "skinNum": 3}, {"skinId": 15306, "skinName": "影龙天霄", "heroId": "153", "heroName": "兰陵王", "classTypeName": ["传说品质", "限定", "太古仙侠传"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 11705, "skinName": "聚星闪耀", "heroId": "117", "heroName": "钟无艳", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 12308, "skinName": "遇见神鼓", "heroId": "123", "heroName": "吕布", "classTypeName": ["传说品质", "限定", "敦煌研究院"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 14104, "skinName": "逐梦之音", "heroId": "141", "heroName": "貂蝉", "classTypeName": ["史诗品质", "限定", "KPL限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 52103, "skinName": "王牌新星", "heroId": "521", "heroName": "海月", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 4}, {"skinId": 13007, "skinName": "惊梅引", "heroId": "130", "heroName": "宫本武藏", "classTypeName": ["传说品质"], "heroType": "战士", "accessWay": "", "skinNum": 6}, {"skinId": 15504, "skinName": "流音漫舞", "heroId": "155", "heroName": "艾琳", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "射手", "accessWay": "荣耀战令获取", "skinNum": 5}, {"skinId": 17307, "skinName": "匿光侦查者", "heroId": "173", "heroName": "李元芳", "classTypeName": ["史诗品质", "限定", "KPL限定"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 54502, "skinName": "末日机甲", "heroId": "545", "heroName": "莱西奥", "classTypeName": ["史诗品质", "末日机甲"], "heroType": "射手", "accessWay": "", "skinNum": 2}, {"skinId": 18005, "skinName": "热血海滩", "heroId": "180", "heroName": "哪吒", "classTypeName": ["勇者品质", "夏日海滩", "活动专属"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 5}, {"skinId": 53803, "skinName": "鹤归松栖", "heroId": "538", "heroName": "云缨", "classTypeName": ["传说品质", "限定", "七夕限定"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 2}, {"skinId": 54402, "skinName": "鹤归松栖", "heroId": "544", "heroName": "赵怀真", "classTypeName": ["史诗品质", "限定", "七夕限定"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 2}, {"skinId": 50505, "skinName": "真我赫兹", "heroId": "505", "heroName": "瑶", "classTypeName": ["音你", "无双", "珍品限定"], "heroType": "辅助", "accessWay": "限时活动获取", "skinNum": 6}, {"skinId": 53105, "skinName": "真我赫兹", "heroId": "531", "heroName": "镜", "classTypeName": ["史诗品质", "音你"], "heroType": "刺客", "accessWay": "", "skinNum": 6}, {"skinId": 19606, "skinName": "真我赫兹", "heroId": "196", "heroName": "百里守约", "classTypeName": ["史诗品质", "音你"], "heroType": "射手", "accessWay": "", "skinNum": 6}, {"skinId": 51305, "skinName": "妄想奇谈", "heroId": "513", "heroName": "上官婉儿", "classTypeName": ["传说品质", "限定", "妄想都市"], "heroType": "法师", "accessWay": "", "skinNum": 6}, {"skinId": 17308, "skinName": "妄想特派", "heroId": "173", "heroName": "李元芳", "classTypeName": ["勇者品质", "活动专属", "妄想都市"], "heroType": "射手", "accessWay": "限时活动获取", "skinNum": 9}, {"skinId": 52102, "skinName": "浮梦罗烟", "heroId": "521", "heroName": "海月", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "", "skinNum": 4}, {"skinId": 15501, "skinName": "女武神", "heroId": "155", "heroName": "艾琳", "classTypeName": ["勇者品质", "限定", "珍宝阁专属"], "heroType": "射手", "accessWay": "限时活动获取", "skinNum": 5}, {"skinId": 13207, "skinName": "妄想实况", "heroId": "132", "heroName": "马可波罗", "classTypeName": ["传说品质", "限定", "妄想都市"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 54802, "skinName": "驭风魔法", "heroId": "548", "heroName": "戈娅", "classTypeName": ["史诗品质", "战令限定", "限定", "魔法世界"], "heroType": "射手", "accessWay": "荣耀战令获取", "skinNum": 2}, {"skinId": 18703, "skinName": "灼幽烈阳", "heroId": "187", "heroName": "东皇太一", "classTypeName": ["传说品质", "FMVP"], "heroType": "辅助", "accessWay": "", "skinNum": 4}, {"skinId": 16703, "skinName": "美猴王", "heroId": "167", "heroName": "孙悟空", "classTypeName": ["勇者品质", "限定", "生肖限定"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 10706, "skinName": "引擎之心", "heroId": "107", "heroName": "赵云", "classTypeName": ["史诗品质", "限定"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 50405, "skinName": "完美假期", "heroId": "504", "heroName": "米莱狄", "classTypeName": ["史诗品质", "夏日海滩"], "heroType": "法师", "accessWay": "", "skinNum": 6}, {"skinId": 52504, "skinName": "探海日志", "heroId": "525", "heroName": "鲁班大师", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S32赛季专属", "skinNum": 4}, {"skinId": 56401, "skinName": "零食大作战", "heroId": "564", "heroName": "姬小满", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 4}, {"skinId": 11406, "skinName": "电玩·爆裂旋风", "heroId": "114", "heroName": "刘禅", "classTypeName": ["史诗品质", "电玩狂想"], "heroType": "辅助", "accessWay": "", "skinNum": 6}, {"skinId": 52305, "skinName": "玲珑珍味", "heroId": "523", "heroName": "西施", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 6}, {"skinId": 19007, "skinName": "鹤羽星尊", "heroId": "190", "heroName": "诸葛亮", "classTypeName": ["传说品质", "限定", "太古仙侠传"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 18406, "skinName": "电玩·兔顽号", "heroId": "184", "heroName": "蔡文姬", "classTypeName": ["史诗品质", "电玩狂想"], "heroType": "辅助", "accessWay": "", "skinNum": 7}, {"skinId": 52803, "skinName": "电玩·雷克斯", "heroId": "528", "heroName": "澜", "classTypeName": ["传说品质", "电玩狂想"], "heroType": "刺客", "accessWay": "", "skinNum": 4}, {"skinId": 14206, "skinName": "追逃游戏", "heroId": "142", "heroName": "安琪拉", "classTypeName": ["传说品质", "限定", "CP皮肤", "520限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 16605, "skinName": "追逃游戏", "heroId": "166", "heroName": "亚瑟", "classTypeName": ["传说品质", "限定", "CP皮肤", "520限定"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 31206, "skinName": "匿光破解者", "heroId": "312", "heroName": "沈梦溪", "classTypeName": ["史诗品质", "限定", "KPL限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 54501, "skinName": "西部游侠", "heroId": "545", "heroName": "莱西奥", "classTypeName": ["勇者品质"], "heroType": "射手", "accessWay": "", "skinNum": 2}, {"skinId": 18604, "skinName": "劲辣红锅", "heroId": "186", "heroName": "太乙真人", "classTypeName": ["勇者品质", "活动专属"], "heroType": "辅助", "accessWay": "限时活动获取", "skinNum": 5}, {"skinId": 14007, "skinName": "百相守梦", "heroId": "140", "heroName": "关羽", "classTypeName": ["史诗品质", "限定", "五五朋友节", "百相守梦"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 17105, "skinName": "百相守梦", "heroId": "171", "heroName": "张飞", "classTypeName": ["传说品质", "限定", "五五朋友节", "百相守梦"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 4}, {"skinId": 17006, "skinName": "百相守梦", "heroId": "170", "heroName": "刘备", "classTypeName": ["史诗品质", "限定", "五五朋友节", "百相守梦"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 10909, "skinName": "青丘·九尾", "heroId": "109", "heroName": "妲己", "classTypeName": ["妖灵志异", "无双", "珍品限定"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 10}, {"skinId": 19906, "skinName": "记忆之芯", "heroId": "199", "heroName": "公孙离", "classTypeName": ["传说品质"], "heroType": "射手", "accessWay": "", "skinNum": 7}, {"skinId": 11303, "skinName": "云端筑梦师", "heroId": "113", "heroName": "庄周", "classTypeName": ["史诗品质", "限定"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 50206, "skinName": "擒涛扼浪", "heroId": "502", "heroName": "裴擒虎", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "刺客", "accessWay": "S31赛季专属", "skinNum": 6}, {"skinId": 17805, "skinName": "潮玩骑兵", "heroId": "178", "heroName": "杨戬", "classTypeName": ["勇者品质", "战令限定", "限定", "奇趣潮玩"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 6}, {"skinId": 11905, "skinName": "奇幻香踪", "heroId": "119", "heroName": "扁鹊", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 5}, {"skinId": 17605, "skinName": "银翎春语", "heroId": "176", "heroName": "杨玉环", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "", "skinNum": 4}, {"skinId": 54401, "skinName": "太极少年", "heroId": "544", "heroName": "赵怀真", "classTypeName": ["勇者品质", "青春校园"], "heroType": "战士", "accessWay": "", "skinNum": 2}, {"skinId": 53602, "skinName": "浮生妄", "heroId": "536", "heroName": "夏洛特", "classTypeName": ["传说品质", "限定", "古今物语"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 2}, {"skinId": 53104, "skinName": "玫瑰异探", "heroId": "531", "heroName": "镜", "classTypeName": ["史诗品质"], "heroType": "刺客", "accessWay": "", "skinNum": 6}, {"skinId": 18206, "skinName": "画中仙", "heroId": "182", "heroName": "干将莫邪", "classTypeName": ["传说品质", "限定", "妖灵志异"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 16701, "skinName": "地狱火", "heroId": "167", "heroName": "孙悟空", "classTypeName": ["传说品质", "地狱火"], "heroType": "刺客", "accessWay": "", "skinNum": 10}, {"skinId": 17302, "skinName": "黑猫爱糖果", "heroId": "173", "heroName": "李元芳", "classTypeName": ["勇者品质"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 15409, "skinName": "燃星之曲", "heroId": "154", "heroName": "花木兰", "classTypeName": ["传说品质", "限定", "宇宙歌姬"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 12004, "skinName": "乐园追猎者", "heroId": "120", "heroName": "白起", "classTypeName": ["勇者品质", "乐园午夜"], "heroType": "坦克", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 17407, "skinName": "无限倾心", "heroId": "174", "heroName": "虞姬", "classTypeName": ["传说品质", "限定", "CP皮肤", "情人节限定"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 13507, "skinName": "无限倾心", "heroId": "135", "heroName": "项羽", "classTypeName": ["传说品质", "限定", "CP皮肤", "情人节限定"], "heroType": "坦克", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 50805, "skinName": "炽翼辉光", "heroId": "508", "heroName": "伽罗", "classTypeName": ["传说品质", "限定"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 19804, "skinName": "天降福星", "heroId": "198", "heroName": "梦奇", "classTypeName": ["史诗品质", "限定"], "heroType": "坦克", "accessWay": "星会员获取", "skinNum": 4}, {"skinId": 14110, "skinName": "幻阙歌", "heroId": "141", "heroName": "貂蝉", "classTypeName": ["荣耀典藏"], "heroType": "法师", "accessWay": "积分夺宝获取", "skinNum": 10}, {"skinId": 52903, "skinName": "冰霜神祇", "heroId": "529", "heroName": "盘古", "classTypeName": ["史诗品质", "限定", "冰雪之歌"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 5}, {"skinId": 16805, "skinName": "牛运亨通", "heroId": "168", "heroName": "牛魔", "classTypeName": ["勇者品质"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 16709, "skinName": "齐天大圣", "heroId": "167", "heroName": "孙悟空", "classTypeName": ["传说品质", "限定"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 50504, "skinName": "山海·碧波行", "heroId": "505", "heroName": "瑶", "classTypeName": ["史诗品质", "限定", "生肖限定"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 13206, "skinName": "山海·玄木吟", "heroId": "132", "heroName": "马可波罗", "classTypeName": ["史诗品质", "限定", "生肖限定"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 10608, "skinName": "山海·琳琅生", "heroId": "106", "heroName": "小乔", "classTypeName": ["传说品质", "限定", "生肖限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 52204, "skinName": "山海·苍雷引", "heroId": "522", "heroName": "曜", "classTypeName": ["传说品质", "限定", "生肖限定"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 4}, {"skinId": 50703, "skinName": "山海·炽霜斩", "heroId": "507", "heroName": "李信", "classTypeName": ["传说品质", "限定", "生肖限定"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 3}, {"skinId": 51104, "skinName": "潮玩探月行", "heroId": "511", "heroName": "猪八戒", "classTypeName": ["勇者品质", "奇趣潮玩"], "heroType": "坦克", "accessWay": "钻石夺宝获取", "skinNum": 4}, {"skinId": 13604, "skinName": "神器·明辉仪", "heroId": "136", "heroName": "武则天", "classTypeName": ["永宁纪", "无双", "珍品限定"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 4}, {"skinId": 13005, "skinName": "地狱之眼", "heroId": "130", "heroName": "宫本武藏", "classTypeName": ["传说品质"], "heroType": "战士", "accessWay": "", "skinNum": 6}, {"skinId": 19304, "skinName": "绛天战甲", "heroId": "193", "heroName": "铠", "classTypeName": ["传说品质"], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 52101, "skinName": "幻泉雾影", "heroId": "521", "heroName": "海月", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "", "skinNum": 4}, {"skinId": 14006, "skinName": "赤影疾锋", "heroId": "140", "heroName": "关羽", "classTypeName": ["荣耀典藏"], "heroType": "战士", "accessWay": "积分夺宝获取", "skinNum": 7}, {"skinId": 53402, "skinName": "海盐诗旅", "heroId": "534", "heroName": "桑启", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S30赛季专属", "skinNum": 4}, {"skinId": 54202, "skinName": "星界游侠", "heroId": "542", "heroName": "暃", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "刺客", "accessWay": "荣耀战令获取", "skinNum": 3}, {"skinId": 19803, "skinName": "顽趣", "heroId": "198", "heroName": "梦奇", "classTypeName": ["史诗品质", "赛季限定", "限定", "王者之证", "顽趣"], "heroType": "坦克", "accessWay": "时空之境获取", "skinNum": 4}, {"skinId": 51504, "skinName": "暖冬·兔眠", "heroId": "515", "heroName": "嫦娥", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 5}, {"skinId": 19305, "skinName": "银白咏叹调", "heroId": "193", "heroName": "铠", "classTypeName": ["荣耀典藏"], "heroType": "战士", "accessWay": "积分夺宝获取", "skinNum": 7}, {"skinId": 12307, "skinName": "怒海麟威", "heroId": "123", "heroName": "吕布", "classTypeName": ["传说品质", "FMVP"], "heroType": "战士", "accessWay": "", "skinNum": 10}, {"skinId": 13107, "skinName": "诗剑行", "heroId": "131", "heroName": "<PERSON>白", "classTypeName": ["传说品质", "世冠皮肤"], "heroType": "刺客", "accessWay": "", "skinNum": 8}, {"skinId": 10805, "skinName": "降魔", "heroId": "108", "heroName": "墨子", "classTypeName": ["史诗品质", "限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 12503, "skinName": "无心", "heroId": "125", "heroName": "元歌", "classTypeName": ["史诗品质", "限定", "源梦皮肤"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 3}, {"skinId": 18303, "skinName": "神奇女侠", "heroId": "183", "heroName": "雅典娜", "classTypeName": [], "heroType": "战士", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 51304, "skinName": "神器·万象笔", "heroId": "513", "heroName": "上官婉儿", "classTypeName": ["史诗品质", "限定", "永宁纪"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 13307, "skinName": "神器·狴犴令", "heroId": "133", "heroName": "狄仁杰", "classTypeName": ["传说品质", "限定", "永宁纪"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 14904, "skinName": "夺宝奇兵", "heroId": "149", "heroName": "刘邦", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "坦克", "accessWay": "荣耀战令获取", "skinNum": 5}, {"skinId": 11605, "skinName": "迷踪丽影", "heroId": "116", "heroName": "阿轲", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "刺客", "accessWay": "荣耀战令获取", "skinNum": 5}, {"skinId": 13504, "skinName": "职棒王牌", "heroId": "135", "heroName": "项羽", "classTypeName": ["史诗品质"], "heroType": "坦克", "accessWay": "限时活动获取", "skinNum": 8}, {"skinId": 53302, "skinName": "顽趣", "heroId": "533", "heroName": "阿古朵", "classTypeName": ["史诗品质", "顽趣"], "heroType": "坦克", "accessWay": "", "skinNum": 3}, {"skinId": 50904, "skinName": "梦圆繁星", "heroId": "509", "heroName": "盾山", "classTypeName": ["史诗品质", "限定"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 4}, {"skinId": 16904, "skinName": "黄金射手座", "heroId": "169", "heroName": "后羿", "classTypeName": ["传说品质", "圣域传说"], "heroType": "射手", "accessWay": "", "skinNum": 8}, {"skinId": 18904, "skinName": "五谷丰年", "heroId": "189", "heroName": "鬼谷子", "classTypeName": ["史诗品质", "限定", "周年限定"], "heroType": "辅助", "accessWay": "限时活动获取", "skinNum": 5}, {"skinId": 15702, "skinName": "绯月行", "heroId": "157", "heroName": "不知火舞", "classTypeName": ["传说品质", "限定", "古今物语"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 3}, {"skinId": 15208, "skinName": "星穹之声", "heroId": "152", "heroName": "王昭君", "classTypeName": ["传说品质", "限定", "宇宙歌姬"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 11704, "skinName": "超时空战士", "heroId": "117", "heroName": "钟无艳", "classTypeName": ["史诗品质", "战令限定", "限定", "超时空小队"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 6}, {"skinId": 17005, "skinName": "潮玩造梦师", "heroId": "170", "heroName": "刘备", "classTypeName": ["勇者品质", "奇趣潮玩"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 19703, "skinName": "滕王阁序", "heroId": "197", "heroName": "弈星", "classTypeName": ["史诗品质", "神州地方志"], "heroType": "法师", "accessWay": "", "skinNum": 4}, {"skinId": 10908, "skinName": "时之奇旅", "heroId": "109", "heroName": "妲己", "classTypeName": ["史诗品质", "限定", "时之奇旅"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 54801, "skinName": "危途狂花", "heroId": "548", "heroName": "戈娅", "classTypeName": ["勇者品质"], "heroType": "射手", "accessWay": "", "skinNum": 2}, {"skinId": 17306, "skinName": "云中旅人", "heroId": "173", "heroName": "李元芳", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "射手", "accessWay": "S29赛季专属", "skinNum": 9}, {"skinId": 11307, "skinName": "天秀·幻梦", "heroId": "113", "heroName": "庄周", "classTypeName": ["史诗品质", "全民电竞"], "heroType": "辅助", "accessWay": "", "skinNum": 8}, {"skinId": 19605, "skinName": "碎云", "heroId": "196", "heroName": "百里守约", "classTypeName": ["传说品质"], "heroType": "射手", "accessWay": "", "skinNum": 6}, {"skinId": 11702, "skinName": "王者之锤", "heroId": "117", "heroName": "钟无艳", "classTypeName": ["史诗品质", "限定", "赏金赛限定"], "heroType": "战士", "accessWay": "全民电竞获取", "skinNum": 6}, {"skinId": 16302, "skinName": "枫霜尽", "heroId": "163", "heroName": "橘右京", "classTypeName": ["传说品质", "限定", "古今物语"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 2}, {"skinId": 18402, "skinName": "奇迹圣诞", "heroId": "184", "heroName": "蔡文姬", "classTypeName": ["史诗品质", "限定", "圣诞颂歌"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 10704, "skinName": "嘻哈天王", "heroId": "107", "heroName": "赵云", "classTypeName": ["史诗品质"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 10}, {"skinId": 52503, "skinName": "匿光启智者", "heroId": "525", "heroName": "鲁班大师", "classTypeName": ["史诗品质", "限定", "KPL限定"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 4}, {"skinId": 11103, "skinName": "蔷薇恋人", "heroId": "111", "heroName": "孙尚香", "classTypeName": ["史诗品质", "限定", "珍宝阁专属"], "heroType": "射手", "accessWay": "限时活动获取", "skinNum": 10}, {"skinId": 10905, "skinName": "热情桑巴", "heroId": "109", "heroName": "妲己", "classTypeName": ["史诗品质", "限定", "五环荣耀"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 16202, "skinName": "前尘镜", "heroId": "162", "heroName": "娜可露露", "classTypeName": ["传说品质", "限定", "古今物语"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 2}, {"skinId": 11109, "skinName": "音你闪耀", "heroId": "111", "heroName": "孙尚香", "classTypeName": ["传说品质", "音你"], "heroType": "射手", "accessWay": "", "skinNum": 10}, {"skinId": 52702, "skinName": "蔚蓝守将", "heroId": "527", "heroName": "蒙恬", "classTypeName": ["勇者品质", "活动专属", "胡桃异想国"], "heroType": "坦克", "accessWay": "限时活动获取", "skinNum": 3}, {"skinId": 54002, "skinName": "唐三藏", "heroId": "540", "heroName": "金蝉", "classTypeName": ["史诗品质", "限定", "86版西游记"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 2}, {"skinId": 52404, "skinName": "胡桃狂想曲", "heroId": "524", "heroName": "蒙犽", "classTypeName": ["勇者品质", "胡桃异想国"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 50304, "skinName": "电玩高手", "heroId": "503", "heroName": "狂铁", "classTypeName": ["史诗品质", "战令限定", "限定", "电玩狂想"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 4}, {"skinId": 13205, "skinName": "深海之息", "heroId": "132", "heroName": "马可波罗", "classTypeName": ["传说品质", "限定"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 16907, "skinName": "无尽星芒", "heroId": "169", "heroName": "后羿", "classTypeName": ["传说品质", "限定"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 31205, "skinName": "大漠名商", "heroId": "312", "heroName": "沈梦溪", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "法师", "accessWay": "S28赛季专属", "skinNum": 7}, {"skinId": 15606, "skinName": "千筹问战", "heroId": "156", "heroName": "张良", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 15408, "skinName": "九霄神辉", "heroId": "154", "heroName": "花木兰", "classTypeName": ["荣耀典藏"], "heroType": "战士", "accessWay": "积分夺宝获取", "skinNum": 9}, {"skinId": 15207, "skinName": "午后时光", "heroId": "152", "heroName": "王昭君", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 9}, {"skinId": 11504, "skinName": "天秀·音浪", "heroId": "115", "heroName": "高渐离", "classTypeName": ["史诗品质", "全民电竞"], "heroType": "法师", "accessWay": "", "skinNum": 5}, {"skinId": 51804, "skinName": "无双飞将", "heroId": "518", "heroName": "马超", "classTypeName": ["传说品质", "FMVP"], "heroType": "战士", "accessWay": "", "skinNum": 5}, {"skinId": 19106, "skinName": "挚爱花嫁", "heroId": "191", "heroName": "大乔", "classTypeName": ["传说品质", "限定", "CP皮肤", "花嫁", "520限定"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 51004, "skinName": "挚爱之约", "heroId": "510", "heroName": "孙策", "classTypeName": ["传说品质", "限定", "CP皮肤", "花嫁", "520限定"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 50104, "skinName": "吟游魔法", "heroId": "501", "heroName": "明世隐", "classTypeName": ["史诗品质", "战令限定", "限定", "魔法世界"], "heroType": "辅助", "accessWay": "荣耀战令获取", "skinNum": 5}, {"skinId": 11405, "skinName": "唤灵魔甲", "heroId": "114", "heroName": "刘禅", "classTypeName": ["勇者品质", "活动专属"], "heroType": "辅助", "accessWay": "限时活动获取", "skinNum": 6}, {"skinId": 50404, "skinName": "契约魔法", "heroId": "504", "heroName": "米莱狄", "classTypeName": ["勇者品质", "魔法世界"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 12605, "skinName": "匿光决锋者", "heroId": "126", "heroName": "夏侯惇", "classTypeName": ["史诗品质", "限定", "KPL限定"], "heroType": "坦克", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 15604, "skinName": "黄金白羊座", "heroId": "156", "heroName": "张良", "classTypeName": ["传说品质", "圣域传说"], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 14109, "skinName": "唤灵魅影", "heroId": "141", "heroName": "貂蝉", "classTypeName": ["史诗品质", "五五朋友节"], "heroType": "法师", "accessWay": "", "skinNum": 10}, {"skinId": 15305, "skinName": "金庭之子", "heroId": "153", "heroName": "兰陵王", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "刺客", "accessWay": "赛季之旅", "skinNum": 7}, {"skinId": 14606, "skinName": "启示之音", "heroId": "146", "heroName": "露娜", "classTypeName": ["传说品质", "限定"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 53401, "skinName": "画中游", "heroId": "534", "heroName": "桑启", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 4}, {"skinId": 12005, "skinName": "夜都怪侠", "heroId": "120", "heroName": "白起", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "坦克", "accessWay": "荣耀战令获取", "skinNum": 5}, {"skinId": 15502, "skinName": "奇遇舞章", "heroId": "155", "heroName": "艾琳", "classTypeName": ["史诗品质", "梦幻童话"], "heroType": "射手", "accessWay": "", "skinNum": 5}, {"skinId": 52802, "skinName": "赏金猎手", "heroId": "528", "heroName": "澜", "classTypeName": ["史诗品质"], "heroType": "刺客", "accessWay": "", "skinNum": 4}, {"skinId": 19905, "skinName": "玉兔公主", "heroId": "199", "heroName": "公孙离", "classTypeName": ["史诗品质", "限定", "86版西游记"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 10709, "skinName": "百木心枪", "heroId": "107", "heroName": "赵云", "classTypeName": ["史诗品质", "限定", "探幽"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 53601, "skinName": "永昼", "heroId": "536", "heroName": "夏洛特", "classTypeName": ["史诗品质"], "heroType": "战士", "accessWay": "", "skinNum": 2}, {"skinId": 18405, "skinName": "花朝如约", "heroId": "184", "heroName": "蔡文姬", "classTypeName": ["传说品质", "荣耀中国节"], "heroType": "辅助", "accessWay": "", "skinNum": 7}, {"skinId": 17903, "skinName": "补天", "heroId": "179", "heroName": "女娲", "classTypeName": ["传说品质", "限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 3}, {"skinId": 51003, "skinName": "末日机甲", "heroId": "510", "heroName": "孙策", "classTypeName": ["传说品质", "末日机甲"], "heroType": "战士", "accessWay": "", "skinNum": 6}, {"skinId": 14407, "skinName": "无双福将", "heroId": "144", "heroName": "程咬金", "classTypeName": ["勇者品质"], "heroType": "坦克", "accessWay": "钻石夺宝获取", "skinNum": 9}, {"skinId": 50503, "skinName": "时之祈愿", "heroId": "505", "heroName": "瑶", "classTypeName": ["传说品质", "限定", "CP皮肤", "情人节限定", "时之际遇"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 50603, "skinName": "时之祈愿", "heroId": "506", "heroName": "云中君", "classTypeName": ["史诗品质", "限定", "CP皮肤", "情人节限定", "时之际遇"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 4}, {"skinId": 10603, "skinName": "纯白花嫁", "heroId": "106", "heroName": "小乔", "classTypeName": ["史诗品质", "限定", "CP皮肤", "情人节限定", "花嫁"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 12402, "skinName": "真爱至上", "heroId": "124", "heroName": "周瑜", "classTypeName": ["史诗品质", "限定", "CP皮肤", "情人节限定", "花嫁"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 18004, "skinName": "雪上飞焰", "heroId": "180", "heroName": "哪吒", "classTypeName": ["史诗品质", "五环荣耀"], "heroType": "战士", "accessWay": "", "skinNum": 5}, {"skinId": 14905, "skinName": "虎啸剑宗", "heroId": "149", "heroName": "刘邦", "classTypeName": ["勇者品质"], "heroType": "坦克", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 19006, "skinName": "星域神启", "heroId": "190", "heroName": "诸葛亮", "classTypeName": ["荣耀典藏"], "heroType": "法师", "accessWay": "积分夺宝获取", "skinNum": 7}, {"skinId": 11806, "skinName": "寅虎·展翼", "heroId": "118", "heroName": "孙膑", "classTypeName": ["史诗品质", "限定", "生肖限定"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 11208, "skinName": "寅虎·瑞焰", "heroId": "112", "heroName": "鲁班七号", "classTypeName": ["传说品质", "限定", "生肖限定"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 11}, {"skinId": 17604, "skinName": "寅虎·心曲", "heroId": "176", "heroName": "杨玉环", "classTypeName": ["史诗品质", "限定", "生肖限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 4}, {"skinId": 50205, "skinName": "寅虎·赤拳", "heroId": "502", "heroName": "裴擒虎", "classTypeName": ["传说品质", "限定", "生肖限定"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 10503, "skinName": "寅虎·御盾", "heroId": "105", "heroName": "廉颇", "classTypeName": ["史诗品质", "限定", "生肖限定"], "heroType": "坦克", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 13104, "skinName": "敏锐之力", "heroId": "131", "heroName": "<PERSON>白", "classTypeName": ["勇者品质", "五路精神"], "heroType": "刺客", "accessWay": "", "skinNum": 8}, {"skinId": 19204, "skinName": "火炮绅士", "heroId": "192", "heroName": "黄忠", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "射手", "accessWay": "荣耀战令获取", "skinNum": 4}, {"skinId": 13405, "skinName": "沙漠行僧", "heroId": "134", "heroName": "达摩", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "战士", "accessWay": "S26赛季专属", "skinNum": 5}, {"skinId": 11106, "skinName": "沉稳之力", "heroId": "111", "heroName": "孙尚香", "classTypeName": ["勇者品质", "五路精神"], "heroType": "射手", "accessWay": "", "skinNum": 10}, {"skinId": 12303, "skinName": "末日机甲", "heroId": "123", "heroName": "吕布", "classTypeName": ["史诗品质", "末日机甲"], "heroType": "战士", "accessWay": "", "skinNum": 10}, {"skinId": 15006, "skinName": "傲雪梅枪", "heroId": "150", "heroName": "韩信", "classTypeName": ["史诗品质", "限定", "探幽"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 54201, "skinName": "碧珀绯影", "heroId": "542", "heroName": "暃", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 3}, {"skinId": 12105, "skinName": "幻夜卜梦", "heroId": "121", "heroName": "芈月", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 6}, {"skinId": 18305, "skinName": "黎明之约", "heroId": "183", "heroName": "雅典娜", "classTypeName": ["史诗品质", "赛季限定", "限定", "王者之证"], "heroType": "战士", "accessWay": "时空之境获取", "skinNum": 5}, {"skinId": 15404, "skinName": "青春决赛季", "heroId": "154", "heroName": "花木兰", "classTypeName": ["史诗品质", "限定", "青春校园"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 53103, "skinName": "匿光追影者", "heroId": "531", "heroName": "镜", "classTypeName": ["史诗品质", "限定", "KPL限定"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 12705, "skinName": "女儿国国王", "heroId": "127", "heroName": "甄姬", "classTypeName": ["史诗品质", "限定", "86版西游记"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 13204, "skinName": "潮玩牛仔", "heroId": "132", "heroName": "马可波罗", "classTypeName": ["勇者品质", "奇趣潮玩"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 51503, "skinName": "拒霜思", "heroId": "515", "heroName": "嫦娥", "classTypeName": ["史诗品质", "限定", "源梦皮肤"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 54001, "skinName": "前尘", "heroId": "540", "heroName": "金蝉", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 2}, {"skinId": 50303, "skinName": "特工战影", "heroId": "503", "heroName": "狂铁", "classTypeName": ["勇者品质", "活动专属"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 4}, {"skinId": 11108, "skinName": "异界灵契", "heroId": "111", "heroName": "孙尚香", "classTypeName": ["传说品质", "限定"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 16802, "skinName": "制霸全明星", "heroId": "168", "heroName": "牛魔", "classTypeName": ["勇者品质"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 15405, "skinName": "冠军飞将", "heroId": "154", "heroName": "花木兰", "classTypeName": ["传说品质", "FMVP"], "heroType": "战士", "accessWay": "", "skinNum": 9}, {"skinId": 11306, "skinName": "高山流水", "heroId": "113", "heroName": "庄周", "classTypeName": ["史诗品质", "限定", "周年限定"], "heroType": "辅助", "accessWay": "限时活动获取", "skinNum": 8}, {"skinId": 11301, "skinName": "鲤鱼之梦", "heroId": "113", "heroName": "庄周", "classTypeName": ["勇者品质"], "heroType": "辅助", "accessWay": "", "skinNum": 8}, {"skinId": 13702, "skinName": "暗渊魔法", "heroId": "137", "heroName": "司马懿", "classTypeName": ["史诗品质", "魔法世界"], "heroType": "刺客", "accessWay": "", "skinNum": 2}, {"skinId": 10606, "skinName": "青蛇", "heroId": "106", "heroName": "小乔", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "", "skinNum": 9}, {"skinId": 12703, "skinName": "游园惊梦", "heroId": "127", "heroName": "甄姬", "classTypeName": ["史诗品质", "限定", "周年限定", "中华曲艺"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 8}, {"skinId": 18205, "skinName": "真爱魔法", "heroId": "182", "heroName": "干将莫邪", "classTypeName": ["史诗品质", "战令限定", "限定", "魔法世界"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 7}, {"skinId": 51103, "skinName": "猪悟能", "heroId": "511", "heroName": "猪八戒", "classTypeName": ["史诗品质", "限定", "86版西游记"], "heroType": "坦克", "accessWay": "限时出售", "skinNum": 4}, {"skinId": 10907, "skinName": "紫罗兰之誓", "heroId": "109", "heroName": "妲己", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 19503, "skinName": "原初追逐者", "heroId": "195", "heroName": "百里玄策", "classTypeName": ["史诗品质", "赛季限定", "限定", "王者之证"], "heroType": "刺客", "accessWay": "时空之境获取", "skinNum": 5}, {"skinId": 19404, "skinName": "千军破阵", "heroId": "194", "heroName": "苏烈", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S25赛季专属", "skinNum": 4}, {"skinId": 10705, "skinName": "白执事", "heroId": "107", "heroName": "赵云", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 10}, {"skinId": 19104, "skinName": "白蛇", "heroId": "191", "heroName": "大乔", "classTypeName": ["史诗品质"], "heroType": "辅助", "accessWay": "", "skinNum": 9}, {"skinId": 14205, "skinName": "时之奇旅", "heroId": "142", "heroName": "安琪拉", "classTypeName": ["史诗品质", "时之奇旅"], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 16708, "skinName": "孙行者", "heroId": "167", "heroName": "孙悟空", "classTypeName": ["史诗品质", "限定", "86版西游记"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 14401, "skinName": "爱与正义", "heroId": "144", "heroName": "程咬金", "classTypeName": ["勇者品质"], "heroType": "坦克", "accessWay": "", "skinNum": 9}, {"skinId": 17503, "skinName": "乐园奇幻夜", "heroId": "175", "heroName": "钟馗", "classTypeName": ["史诗品质", "乐园午夜"], "heroType": "辅助", "accessWay": "", "skinNum": 4}, {"skinId": 14108, "skinName": "遇见胡旋", "heroId": "141", "heroName": "貂蝉", "classTypeName": ["传说品质", "限定", "敦煌研究院"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 16906, "skinName": "圣弓游侠", "heroId": "169", "heroName": "后羿", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "射手", "accessWay": "荣耀战令获取", "skinNum": 8}, {"skinId": 31204, "skinName": "月团寄思", "heroId": "312", "heroName": "沈梦溪", "classTypeName": ["史诗品质", "荣耀中国节"], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 12905, "skinName": "铁甲之心", "heroId": "129", "heroName": "典韦", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 11002, "skinName": "暗夜贵公子", "heroId": "110", "heroName": "嬴政", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "", "skinNum": 5}, {"skinId": 50903, "skinName": "圆桌骑士", "heroId": "509", "heroName": "盾山", "classTypeName": ["勇者品质", "圆桌骑士", "活动专属"], "heroType": "辅助", "accessWay": "限时活动获取", "skinNum": 4}, {"skinId": 10710, "skinName": "淬星耀世", "heroId": "107", "heroName": "赵云", "classTypeName": ["传说品质", "世冠皮肤"], "heroType": "战士", "accessWay": "", "skinNum": 10}, {"skinId": 17002, "skinName": "纽约教父", "heroId": "170", "heroName": "刘备", "classTypeName": ["勇者品质", "活动专属"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 7}, {"skinId": 15206, "skinName": "乞巧织情", "heroId": "152", "heroName": "王昭君", "classTypeName": ["传说品质", "荣耀中国节"], "heroType": "法师", "accessWay": "", "skinNum": 9}, {"skinId": 52304, "skinName": "游龙清影", "heroId": "523", "heroName": "西施", "classTypeName": ["传说品质", "FMVP"], "heroType": "法师", "accessWay": "", "skinNum": 6}, {"skinId": 19902, "skinName": "蜜橘之夏", "heroId": "199", "heroName": "公孙离", "classTypeName": ["史诗品质", "限定", "夏日海滩"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 11503, "skinName": "玩趣恶龙", "heroId": "115", "heroName": "高渐离", "classTypeName": ["勇者品质", "限定", "珍宝阁专属"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 5}, {"skinId": 13903, "skinName": "功夫老勺", "heroId": "139", "heroName": "老夫子", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "钻石夺宝获取", "skinNum": 6}, {"skinId": 15004, "skinName": "逐梦之影", "heroId": "150", "heroName": "韩信", "classTypeName": ["史诗品质", "限定", "KPL限定"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 17804, "skinName": "天秀·启明", "heroId": "178", "heroName": "杨戬", "classTypeName": ["史诗品质", "全民电竞"], "heroType": "战士", "accessWay": "", "skinNum": 6}, {"skinId": 52502, "skinName": "乓乓大师", "heroId": "525", "heroName": "鲁班大师", "classTypeName": ["勇者品质", "活动专属", "五环荣耀"], "heroType": "辅助", "accessWay": "限时活动获取", "skinNum": 4}, {"skinId": 11207, "skinName": "乒乒小将", "heroId": "112", "heroName": "鲁班七号", "classTypeName": ["史诗品质", "限定", "五环荣耀"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 11}, {"skinId": 17504, "skinName": "驱傩正仪", "heroId": "175", "heroName": "钟馗", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S24赛季专属", "skinNum": 4}, {"skinId": 19105, "skinName": "白鹤梁神女", "heroId": "191", "heroName": "大乔", "classTypeName": ["史诗品质", "限定", "神州地方志"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 50804, "skinName": "天狼溯光者", "heroId": "508", "heroName": "伽罗", "classTypeName": ["史诗品质", "限定", "KPL限定"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 13905, "skinName": "航海奇遇记", "heroId": "139", "heroName": "老夫子", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 6}, {"skinId": 11404, "skinName": "秘密基地", "heroId": "114", "heroName": "刘禅", "classTypeName": ["勇者品质"], "heroType": "辅助", "accessWay": "钻石夺宝获取", "skinNum": 6}, {"skinId": 10804, "skinName": "神迹守卫", "heroId": "108", "heroName": "墨子", "classTypeName": ["勇者品质", "战令限定", "限定", "神迹守卫"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 6}, {"skinId": 53801, "skinName": "赤焰之缨", "heroId": "538", "heroName": "云缨", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 2}, {"skinId": 13002, "skinName": "未来纪元", "heroId": "130", "heroName": "宫本武藏", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 6}, {"skinId": 16801, "skinName": "西部大镖客", "heroId": "168", "heroName": "牛魔", "classTypeName": ["勇者品质", "西部大镖客"], "heroType": "辅助", "accessWay": "", "skinNum": 6}, {"skinId": 14203, "skinName": "心灵骇客", "heroId": "142", "heroName": "安琪拉", "classTypeName": ["史诗品质", "CP皮肤"], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 11902, "skinName": "化身博士", "heroId": "119", "heroName": "扁鹊", "classTypeName": ["勇者品质", "活动专属"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 5}, {"skinId": 52403, "skinName": "龙鼓争鸣", "heroId": "524", "heroName": "蒙犽", "classTypeName": ["史诗品质", "荣耀中国节"], "heroType": "射手", "accessWay": "", "skinNum": 6}, {"skinId": 52203, "skinName": "李逍遥 ", "heroId": "522", "heroName": "曜", "classTypeName": ["传说品质", "限定"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 4}, {"skinId": 13501, "skinName": "帝国元帅", "heroId": "135", "heroName": "项羽", "classTypeName": [], "heroType": "坦克", "accessWay": "", "skinNum": 8}, {"skinId": 10802, "skinName": "龙骑士", "heroId": "108", "heroName": "墨子", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "", "skinNum": 6}, {"skinId": 11001, "skinName": "摇滚巨星", "heroId": "110", "heroName": "嬴政", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "", "skinNum": 5}, {"skinId": 10607, "skinName": "音你心动", "heroId": "106", "heroName": "小乔", "classTypeName": ["传说品质", "CP皮肤", "音你"], "heroType": "法师", "accessWay": "", "skinNum": 9}, {"skinId": 12404, "skinName": "音你心动", "heroId": "124", "heroName": "周瑜", "classTypeName": ["传说品质", "CP皮肤", "音你"], "heroType": "法师", "accessWay": "", "skinNum": 6}, {"skinId": 31202, "skinName": "鲨炮海盗猫", "heroId": "312", "heroName": "沈梦溪", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "钻石夺宝获取", "skinNum": 7}, {"skinId": 19802, "skinName": "胖达荣荣", "heroId": "198", "heroName": "梦奇", "classTypeName": ["传说品质", "中华曲艺"], "heroType": "坦克", "accessWay": "", "skinNum": 4}, {"skinId": 52902, "skinName": "重装意志", "heroId": "529", "heroName": "盘古", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 15605, "skinName": "缤纷绘卷", "heroId": "156", "heroName": "张良", "classTypeName": ["勇者品质", "活动专属", "青春校园"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 7}, {"skinId": 19002, "skinName": "黄金分割率", "heroId": "190", "heroName": "诸葛亮", "classTypeName": ["史诗品质", "青春校园"], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 13601, "skinName": "东方不败", "heroId": "136", "heroName": "武则天", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "", "skinNum": 4}, {"skinId": 19302, "skinName": "曙光守护者", "heroId": "193", "heroName": "铠", "classTypeName": ["史诗品质", "五五朋友节"], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 15003, "skinName": "白龙吟", "heroId": "150", "heroName": "韩信", "classTypeName": ["史诗品质"], "heroType": "刺客", "accessWay": "", "skinNum": 8}, {"skinId": 19103, "skinName": "猫狗日记", "heroId": "191", "heroName": "大乔", "classTypeName": ["史诗品质", "CP皮肤"], "heroType": "辅助", "accessWay": "", "skinNum": 9}, {"skinId": 19504, "skinName": "热力回旋", "heroId": "195", "heroName": "百里玄策", "classTypeName": ["史诗品质", "五五朋友节", "青春校园"], "heroType": "刺客", "accessWay": "", "skinNum": 5}, {"skinId": 14003, "skinName": "冰锋战神", "heroId": "140", "heroName": "关羽", "classTypeName": ["史诗品质", "限定", "周年限定", "冰雪之歌"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 7}, {"skinId": 13202, "skinName": "逐梦之星", "heroId": "132", "heroName": "马可波罗", "classTypeName": ["史诗品质", "限定", "KPL限定"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 52303, "skinName": "诗语江南", "heroId": "523", "heroName": "西施", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "", "skinNum": 6}, {"skinId": 16705, "skinName": "全息碎影", "heroId": "167", "heroName": "孙悟空", "classTypeName": ["荣耀典藏"], "heroType": "刺客", "accessWay": "积分夺宝获取", "skinNum": 10}, {"skinId": 14406, "skinName": "演武夺筹", "heroId": "144", "heroName": "程咬金", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "坦克", "accessWay": "S23赛季专属", "skinNum": 9}, {"skinId": 18902, "skinName": "幻乐之宴", "heroId": "189", "heroName": "鬼谷子", "classTypeName": ["勇者品质"], "heroType": "辅助", "accessWay": "钻石夺宝获取", "skinNum": 5}, {"skinId": 13402, "skinName": "大发明家", "heroId": "134", "heroName": "达摩", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 5}, {"skinId": 11802, "skinName": "天使之翼", "heroId": "118", "heroName": "孙膑", "classTypeName": ["勇者品质"], "heroType": "辅助", "accessWay": "", "skinNum": 8}, {"skinId": 50403, "skinName": "胡桃异想国", "heroId": "504", "heroName": "米莱狄", "classTypeName": ["史诗品质", "战令限定", "限定", "胡桃异想国"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 6}, {"skinId": 13404, "skinName": "星际陆战队", "heroId": "134", "heroName": "达摩", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 5}, {"skinId": 11204, "skinName": "星空梦想", "heroId": "112", "heroName": "鲁班七号", "classTypeName": ["荣耀典藏", "太空漫游"], "heroType": "射手", "accessWay": "积分夺宝获取", "skinNum": 11}, {"skinId": 17305, "skinName": "飞鸢探春", "heroId": "173", "heroName": "李元芳", "classTypeName": ["传说品质", "荣耀中国节"], "heroType": "射手", "accessWay": "", "skinNum": 9}, {"skinId": 11402, "skinName": "绅士熊喵", "heroId": "114", "heroName": "刘禅", "classTypeName": ["史诗品质"], "heroType": "辅助", "accessWay": "", "skinNum": 6}, {"skinId": 14601, "skinName": "哥特玫瑰", "heroId": "146", "heroName": "露娜", "classTypeName": ["勇者品质"], "heroType": "刺客", "accessWay": "", "skinNum": 7}, {"skinId": 18702, "skinName": "逐梦之光", "heroId": "187", "heroName": "东皇太一", "classTypeName": ["史诗品质", "限定", "KPL限定"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 4}, {"skinId": 50502, "skinName": "遇见神鹿", "heroId": "505", "heroName": "瑶", "classTypeName": ["史诗品质", "五五朋友节", "敦煌研究院"], "heroType": "辅助", "accessWay": "", "skinNum": 6}, {"skinId": 50101, "skinName": "占星师", "heroId": "501", "heroName": "明世隐", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 5}, {"skinId": 51002, "skinName": "猫狗日记", "heroId": "510", "heroName": "孙策", "classTypeName": ["史诗品质", "CP皮肤"], "heroType": "战士", "accessWay": "", "skinNum": 6}, {"skinId": 11104, "skinName": "杀手不太冷", "heroId": "111", "heroName": "孙尚香", "classTypeName": ["荣耀典藏"], "heroType": "射手", "accessWay": "积分夺宝获取", "skinNum": 10}, {"skinId": 14605, "skinName": "瓷语鉴心", "heroId": "146", "heroName": "露娜", "classTypeName": ["勇者品质"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 11701, "skinName": "生化警戒", "heroId": "117", "heroName": "钟无艳", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 6}, {"skinId": 13101, "skinName": "范海辛", "heroId": "131", "heroName": "<PERSON>白", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 8}, {"skinId": 10702, "skinName": "未来纪元", "heroId": "107", "heroName": "赵云", "classTypeName": ["史诗品质"], "heroType": "战士", "accessWay": "", "skinNum": 10}, {"skinId": 18603, "skinName": "华丽摇滚", "heroId": "186", "heroName": "太乙真人", "classTypeName": ["勇者品质", "限定", "战队赛限定"], "heroType": "辅助", "accessWay": "战队赛专属", "skinNum": 5}, {"skinId": 13102, "skinName": "千年之狐", "heroId": "131", "heroName": "<PERSON>白", "classTypeName": ["史诗品质"], "heroType": "刺客", "accessWay": "", "skinNum": 8}, {"skinId": 19603, "skinName": "特工魅影", "heroId": "196", "heroName": "百里守约", "classTypeName": ["史诗品质"], "heroType": "射手", "accessWay": "", "skinNum": 6}, {"skinId": 13303, "skinName": "超时空战士", "heroId": "133", "heroName": "狄仁杰", "classTypeName": ["传说品质", "超时空小队"], "heroType": "射手", "accessWay": "", "skinNum": 8}, {"skinId": 11602, "skinName": "暗夜猫娘", "heroId": "116", "heroName": "阿轲", "classTypeName": ["史诗品质"], "heroType": "刺客", "accessWay": "", "skinNum": 5}, {"skinId": 11601, "skinName": "爱心护理", "heroId": "116", "heroName": "阿轲", "classTypeName": ["勇者品质"], "heroType": "刺客", "accessWay": "", "skinNum": 5}, {"skinId": 11901, "skinName": "救世之瞳", "heroId": "119", "heroName": "扁鹊", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "", "skinNum": 5}, {"skinId": 10901, "skinName": "女仆咖啡", "heroId": "109", "heroName": "妲己", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "", "skinNum": 10}, {"skinId": 16707, "skinName": "零号·赤焰", "heroId": "167", "heroName": "孙悟空", "classTypeName": ["传说品质", "限定"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 15304, "skinName": "默契交锋", "heroId": "153", "heroName": "兰陵王", "classTypeName": ["传说品质", "限定", "CP皮肤", "情人节限定"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 15407, "skinName": "默契交锋", "heroId": "154", "heroName": "花木兰", "classTypeName": ["史诗品质", "限定", "CP皮肤", "情人节限定"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 13001, "skinName": "鬼剑武藏", "heroId": "130", "heroName": "宫本武藏", "classTypeName": ["史诗品质"], "heroType": "战士", "accessWay": "", "skinNum": 6}, {"skinId": 13306, "skinName": "万华元夜", "heroId": "133", "heroName": "狄仁杰", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "射手", "accessWay": "S22赛季专属", "skinNum": 8}, {"skinId": 13403, "skinName": "黄金狮子座", "heroId": "134", "heroName": "达摩", "classTypeName": ["传说品质", "圣域传说"], "heroType": "战士", "accessWay": "", "skinNum": 5}, {"skinId": 10801, "skinName": "金属风暴", "heroId": "108", "heroName": "墨子", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "", "skinNum": 6}, {"skinId": 16902, "skinName": "阿尔法小队", "heroId": "169", "heroName": "后羿", "classTypeName": ["勇者品质"], "heroType": "射手", "accessWay": "", "skinNum": 8}, {"skinId": 18401, "skinName": "蔷薇王座", "heroId": "184", "heroName": "蔡文姬", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 7}, {"skinId": 13106, "skinName": "鸣剑·曳影", "heroId": "131", "heroName": "<PERSON>白", "classTypeName": ["荣耀典藏"], "heroType": "刺客", "accessWay": "积分夺宝获取", "skinNum": 8}, {"skinId": 11403, "skinName": "天才门将", "heroId": "114", "heroName": "刘禅", "classTypeName": ["史诗品质"], "heroType": "辅助", "accessWay": "", "skinNum": 6}, {"skinId": 17102, "skinName": "乱世虎臣", "heroId": "171", "heroName": "张飞", "classTypeName": ["史诗品质", "御龙在天"], "heroType": "辅助", "accessWay": "", "skinNum": 4}, {"skinId": 17001, "skinName": "万事如意", "heroId": "170", "heroName": "刘备", "classTypeName": ["勇者品质", "新春专属"], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 12502, "skinName": "云间偶戏", "heroId": "125", "heroName": "元歌", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "刺客", "accessWay": "荣耀战令获取", "skinNum": 3}, {"skinId": 17402, "skinName": "霸王别姬", "heroId": "174", "heroName": "虞姬", "classTypeName": ["史诗品质", "限定", "CP皮肤", "中华曲艺"], "heroType": "射手", "accessWay": "限时活动获取", "skinNum": 8}, {"skinId": 13505, "skinName": "霸王别姬", "heroId": "135", "heroName": "项羽", "classTypeName": ["史诗品质", "限定", "CP皮肤", "中华曲艺"], "heroType": "坦克", "accessWay": "限时活动获取", "skinNum": 8}, {"skinId": 17803, "skinName": "次元傲视", "heroId": "178", "heroName": "杨戬", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 12306, "skinName": "御风骁将", "heroId": "123", "heroName": "吕布", "classTypeName": ["史诗品质", "限定", "天文志（牛年限定）", "生肖限定"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 16804, "skinName": "奔雷神使", "heroId": "168", "heroName": "牛魔", "classTypeName": ["史诗品质", "限定", "天文志（牛年限定）", "生肖限定"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 19005, "skinName": "时雨天司", "heroId": "190", "heroName": "诸葛亮", "classTypeName": ["传说品质", "限定", "天文志（牛年限定）", "生肖限定"], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 19904, "skinName": "祈雪灵祝", "heroId": "199", "heroName": "公孙离", "classTypeName": ["传说品质", "限定", "天文志（牛年限定）", "生肖限定"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 11203, "skinName": "电玩小子", "heroId": "112", "heroName": "鲁班七号", "classTypeName": ["史诗品质", "电玩狂想"], "heroType": "射手", "accessWay": "", "skinNum": 11}, {"skinId": 14602, "skinName": "绯红之刃", "heroId": "146", "heroName": "露娜", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 7}, {"skinId": 11102, "skinName": "水果甜心", "heroId": "111", "heroName": "孙尚香", "classTypeName": ["史诗品质"], "heroType": "射手", "accessWay": "", "skinNum": 10}, {"skinId": 12104, "skinName": "白晶晶", "heroId": "121", "heroName": "芈月", "classTypeName": ["史诗品质", "限定", "大话西游"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 53102, "skinName": "炽阳神光", "heroId": "531", "heroName": "镜", "classTypeName": ["传说品质", "FMVP"], "heroType": "刺客", "accessWay": "", "skinNum": 6}, {"skinId": 16604, "skinName": "潮玩骑士王", "heroId": "166", "heroName": "亚瑟", "classTypeName": ["勇者品质", "战令限定", "限定", "奇趣潮玩"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 7}, {"skinId": 53701, "skinName": "启蛰", "heroId": "537", "heroName": "司空震", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 2}, {"skinId": 17405, "skinName": "启明星使", "heroId": "174", "heroName": "虞姬", "classTypeName": ["传说品质", "限定", "贵族限定"], "heroType": "射手", "accessWay": "贵族8级赠送", "skinNum": 8}, {"skinId": 11805, "skinName": "天狼运算者", "heroId": "118", "heroName": "孙膑", "classTypeName": ["史诗品质", "限定", "KPL限定"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 52801, "skinName": "孤猎", "heroId": "528", "heroName": "澜", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 4}, {"skinId": 50602, "skinName": "纤云弄巧", "heroId": "506", "heroName": "云中君", "classTypeName": ["史诗品质", "限定", "源梦皮肤"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 4}, {"skinId": 13506, "skinName": "科学大爆炸", "heroId": "135", "heroName": "项羽", "classTypeName": ["勇者品质"], "heroType": "坦克", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 13301, "skinName": "锦衣卫", "heroId": "133", "heroName": "狄仁杰", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 8}, {"skinId": 50204, "skinName": "李小龙", "heroId": "502", "heroName": "裴擒虎", "classTypeName": ["史诗品质", "限定", "周年限定"], "heroType": "刺客", "accessWay": "限时活动获取", "skinNum": 6}, {"skinId": 17901, "skinName": "尼罗河女神", "heroId": "179", "heroName": "女娲", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 3}, {"skinId": 19004, "skinName": "掌控之力", "heroId": "190", "heroName": "诸葛亮", "classTypeName": ["勇者品质", "五路精神"], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 13502, "skinName": "苍穹之光", "heroId": "135", "heroName": "项羽", "classTypeName": ["史诗品质"], "heroType": "坦克", "accessWay": "", "skinNum": 8}, {"skinId": 14802, "skinName": "炽热元素使", "heroId": "148", "heroName": "姜子牙", "classTypeName": ["勇者品质", "活动专属"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 4}, {"skinId": 18903, "skinName": "原初探秘者", "heroId": "189", "heroName": "鬼谷子", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S21赛季专属", "skinNum": 5}, {"skinId": 52402, "skinName": "狂想玩偶喵", "heroId": "524", "heroName": "蒙犽", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "射手", "accessWay": "荣耀战令获取", "skinNum": 6}, {"skinId": 14005, "skinName": "武圣", "heroId": "140", "heroName": "关羽", "classTypeName": ["传说品质", "限定", "五虎上将"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 10602, "skinName": "天鹅之梦", "heroId": "106", "heroName": "小乔", "classTypeName": ["荣耀典藏"], "heroType": "法师", "accessWay": "积分夺宝获取", "skinNum": 9}, {"skinId": 11502, "skinName": "死亡摇滚", "heroId": "115", "heroName": "高渐离", "classTypeName": ["勇者品质", "限定", "战队赛限定"], "heroType": "法师", "accessWay": "战队赛专属", "skinNum": 5}, {"skinId": 12701, "skinName": "冰雪圆舞曲", "heroId": "127", "heroName": "甄姬", "classTypeName": ["史诗品质", "冰雪之歌"], "heroType": "法师", "accessWay": "", "skinNum": 8}, {"skinId": 12803, "skinName": "幽灵船长", "heroId": "128", "heroName": "曹操", "classTypeName": ["史诗品质"], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 12003, "skinName": "星夜王子", "heroId": "120", "heroName": "白起", "classTypeName": ["史诗品质"], "heroType": "坦克", "accessWay": "", "skinNum": 5}, {"skinId": 18304, "skinName": "单词大作战", "heroId": "183", "heroName": "雅典娜", "classTypeName": ["勇者品质", "战令限定", "限定", "青春校园"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 5}, {"skinId": 10502, "skinName": "无尽征程", "heroId": "105", "heroName": "廉颇", "classTypeName": ["勇者品质"], "heroType": "坦克", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 13203, "skinName": "暗影游猎", "heroId": "132", "heroName": "马可波罗", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "射手", "accessWay": "荣耀战令获取", "skinNum": 9}, {"skinId": 52202, "skinName": "云鹰飞将", "heroId": "522", "heroName": "曜", "classTypeName": ["传说品质", "FMVP"], "heroType": "战士", "accessWay": "", "skinNum": 4}, {"skinId": 53301, "skinName": "熊喵少女", "heroId": "533", "heroName": "阿古朵", "classTypeName": [], "heroType": "坦克", "accessWay": "", "skinNum": 3}, {"skinId": 12901, "skinName": "黄金武士", "heroId": "129", "heroName": "典韦", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 17104, "skinName": "虎魄", "heroId": "171", "heroName": "张飞", "classTypeName": ["史诗品质", "限定", "五虎上将"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 4}, {"skinId": 13904, "skinName": "醍醐杖", "heroId": "139", "heroName": "老夫子", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "战士", "accessWay": "S20赛季专属", "skinNum": 6}, {"skinId": 50702, "skinName": "一念神魔", "heroId": "507", "heroName": "李信", "classTypeName": ["传说品质", "世冠皮肤"], "heroType": "战士", "accessWay": "", "skinNum": 3}, {"skinId": 51102, "skinName": "西部大镖客", "heroId": "511", "heroName": "猪八戒", "classTypeName": ["勇者品质", "战令限定", "限定", "西部大镖客"], "heroType": "坦克", "accessWay": "荣耀战令获取", "skinNum": 4}, {"skinId": 51803, "skinName": "神威", "heroId": "518", "heroName": "马超", "classTypeName": ["史诗品质", "限定", "五虎上将"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 5}, {"skinId": 51303, "skinName": "天狼绘梦者", "heroId": "513", "heroName": "上官婉儿", "classTypeName": ["史诗品质", "限定", "KPL限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 52701, "skinName": "秩序猎龙将", "heroId": "527", "heroName": "蒙恬", "classTypeName": [], "heroType": "坦克", "accessWay": "", "skinNum": 3}, {"skinId": 50103, "skinName": "疑决卦", "heroId": "501", "heroName": "明世隐", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S19赛季专属", "skinNum": 5}, {"skinId": 17004, "skinName": "时之恋人", "heroId": "170", "heroName": "刘备", "classTypeName": ["传说品质", "CP皮肤", "时之际遇"], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 11107, "skinName": "时之恋人", "heroId": "111", "heroName": "孙尚香", "classTypeName": ["史诗品质", "CP皮肤", "时之际遇"], "heroType": "射手", "accessWay": "", "skinNum": 10}, {"skinId": 17301, "skinName": "特种部队", "heroId": "173", "heroName": "李元芳", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 9}, {"skinId": 52301, "skinName": "归虚梦演", "heroId": "523", "heroName": "西施", "classTypeName": ["归虚梦演"], "heroType": "法师", "accessWay": "", "skinNum": 6}, {"skinId": 19203, "skinName": "烈魂", "heroId": "192", "heroName": "黄忠", "classTypeName": ["史诗品质", "限定", "五虎上将"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 4}, {"skinId": 10708, "skinName": "龙胆", "heroId": "107", "heroName": "赵云", "classTypeName": ["史诗品质", "限定", "五虎上将"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 16704, "skinName": "至尊宝", "heroId": "167", "heroName": "孙悟空", "classTypeName": ["史诗品质", "限定", "CP皮肤", "情人节限定", "大话西游"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 10501, "skinName": "地狱岩魂", "heroId": "105", "heroName": "廉颇", "classTypeName": ["史诗品质", "地狱火"], "heroType": "坦克", "accessWay": "", "skinNum": 5}, {"skinId": 12302, "skinName": "天魔缭乱", "heroId": "123", "heroName": "吕布", "classTypeName": ["传说品质", "限定"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 16301, "skinName": "修罗", "heroId": "163", "heroName": "橘右京", "classTypeName": ["史诗品质"], "heroType": "刺客", "accessWay": "", "skinNum": 2}, {"skinId": 53101, "skinName": "冰刃幻境", "heroId": "531", "heroName": "镜", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 6}, {"skinId": 15701, "skinName": "魅语", "heroId": "157", "heroName": "不知火舞", "classTypeName": ["史诗品质"], "heroType": "刺客", "accessWay": "", "skinNum": 3}, {"skinId": 18003, "skinName": "次元突破", "heroId": "180", "heroName": "哪吒", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "钻石夺宝获取", "skinNum": 5}, {"skinId": 13006, "skinName": "霸王丸", "heroId": "130", "heroName": "宫本武藏", "classTypeName": ["勇者品质", "SNK系列"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 6}, {"skinId": 16905, "skinName": "如梦令", "heroId": "169", "heroName": "后羿", "classTypeName": ["史诗品质", "限定", "CP皮肤", "情人节限定"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 51502, "skinName": "如梦令", "heroId": "515", "heroName": "嫦娥", "classTypeName": ["史诗品质", "限定", "CP皮肤", "情人节限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 50501, "skinName": "森", "heroId": "505", "heroName": "瑶", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 6}, {"skinId": 17304, "skinName": "银河之约", "heroId": "173", "heroName": "李元芳", "classTypeName": ["史诗品质", "战令限定", "限定", "太空漫游"], "heroType": "射手", "accessWay": "荣耀战令获取", "skinNum": 9}, {"skinId": 11305, "skinName": "玄嵩", "heroId": "113", "heroName": "庄周", "classTypeName": ["史诗品质", "限定", "生肖限定", "神州地方志"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 12704, "skinName": "幽恒", "heroId": "127", "heroName": "甄姬", "classTypeName": ["史诗品质", "限定", "生肖限定", "神州地方志"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 19601, "skinName": "绝影神枪", "heroId": "196", "heroName": "百里守约", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 6}, {"skinId": 19401, "skinName": "爱与和平", "heroId": "194", "heroName": "苏烈", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 4}, {"skinId": 10601, "skinName": "万圣前夜", "heroId": "106", "heroName": "小乔", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 9}, {"skinId": 14002, "skinName": "天启骑士", "heroId": "140", "heroName": "关羽", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 14405, "skinName": "活力突击", "heroId": "144", "heroName": "程咬金", "classTypeName": ["荣耀典藏"], "heroType": "坦克", "accessWay": "积分夺宝获取", "skinNum": 9}, {"skinId": 15302, "skinName": "暗隐猎兽者", "heroId": "153", "heroName": "兰陵王", "classTypeName": ["史诗品质"], "heroType": "刺客", "accessWay": "", "skinNum": 7}, {"skinId": 11202, "skinName": "福禄兄弟", "heroId": "112", "heroName": "鲁班七号", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 11}, {"skinId": 12001, "skinName": "白色死神", "heroId": "120", "heroName": "白起", "classTypeName": ["勇者品质"], "heroType": "坦克", "accessWay": "", "skinNum": 5}, {"skinId": 10906, "skinName": "时之彼端", "heroId": "109", "heroName": "妲己", "classTypeName": ["史诗品质", "限定", "时之际遇"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 10701, "skinName": "忍·炎影", "heroId": "107", "heroName": "赵云", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 10}, {"skinId": 14201, "skinName": "玩偶对对碰", "heroId": "142", "heroName": "安琪拉", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 15202, "skinName": "偶像歌手", "heroId": "152", "heroName": "王昭君", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "积分夺宝获取", "skinNum": 9}, {"skinId": 12101, "skinName": "红桃皇后", "heroId": "121", "heroName": "芈月", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "", "skinNum": 6}, {"skinId": 11401, "skinName": "英喵野望", "heroId": "114", "heroName": "刘禅", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 6}, {"skinId": 50801, "skinName": "花见巫女", "heroId": "508", "heroName": "伽罗", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 6}, {"skinId": 14103, "skinName": "仲夏夜之梦", "heroId": "141", "heroName": "貂蝉", "classTypeName": ["传说品质"], "heroType": "法师", "accessWay": "", "skinNum": 10}, {"skinId": 12501, "skinName": "午夜歌剧院", "heroId": "125", "heroName": "元歌", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 3}, {"skinId": 11206, "skinName": "黑桃队长", "heroId": "112", "heroName": "鲁班七号", "classTypeName": ["勇者品质"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 11}, {"skinId": 16903, "skinName": "辉光之辰", "heroId": "169", "heroName": "后羿", "classTypeName": ["史诗品质", "限定", "生肖限定"], "heroType": "射手", "accessWay": "限时活动获取", "skinNum": 8}, {"skinId": 17802, "skinName": "永曜之星", "heroId": "178", "heroName": "杨戬", "classTypeName": ["传说品质", "限定", "生肖限定"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 6}, {"skinId": 12904, "skinName": "岱宗", "heroId": "129", "heroName": "典韦", "classTypeName": ["史诗品质", "限定", "生肖限定", "神州地方志"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 15005, "skinName": "飞衡", "heroId": "150", "heroName": "韩信", "classTypeName": ["史诗品质", "限定", "生肖限定", "神州地方志"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 50803, "skinName": "太华", "heroId": "508", "heroName": "伽罗", "classTypeName": ["传说品质", "限定", "生肖限定", "神州地方志"], "heroType": "射手", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 13603, "skinName": "倪克斯神谕", "heroId": "136", "heroName": "武则天", "classTypeName": ["荣耀典藏"], "heroType": "法师", "accessWay": "积分夺宝获取", "skinNum": 4}, {"skinId": 15602, "skinName": "一千零一夜", "heroId": "156", "heroName": "张良", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 13701, "skinName": "魇语军师", "heroId": "137", "heroName": "司马懿", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 2}, {"skinId": 51001, "skinName": "海之征途", "heroId": "510", "heroName": "孙策", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 6}, {"skinId": 14404, "skinName": "功夫厨神", "heroId": "144", "heroName": "程咬金", "classTypeName": ["史诗品质"], "heroType": "坦克", "accessWay": "", "skinNum": 9}, {"skinId": 15402, "skinName": "兔女郎", "heroId": "154", "heroName": "花木兰", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 9}, {"skinId": 15203, "skinName": "凤凰于飞", "heroId": "152", "heroName": "王昭君", "classTypeName": ["史诗品质", "限定", "CP皮肤", "生肖限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 19903, "skinName": "无限星赏官", "heroId": "199", "heroName": "公孙离", "classTypeName": ["史诗品质"], "heroType": "射手", "accessWay": "", "skinNum": 7}, {"skinId": 16201, "skinName": "晚萤", "heroId": "162", "heroName": "娜可露露", "classTypeName": ["史诗品质"], "heroType": "刺客", "accessWay": "", "skinNum": 2}, {"skinId": 19702, "skinName": "混沌棋", "heroId": "197", "heroName": "弈星", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "法师", "accessWay": "S18赛季专属", "skinNum": 4}, {"skinId": 12802, "skinName": "超能战警", "heroId": "128", "heroName": "曹操", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 50401, "skinName": "精准探案法", "heroId": "504", "heroName": "米莱狄", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 6}, {"skinId": 12601, "skinName": "战争骑士", "heroId": "126", "heroName": "夏侯惇", "classTypeName": [], "heroType": "坦克", "accessWay": "", "skinNum": 6}, {"skinId": 19402, "skinName": "坚韧之力", "heroId": "194", "heroName": "苏烈", "classTypeName": ["勇者品质", "五路精神"], "heroType": "辅助", "accessWay": "", "skinNum": 4}, {"skinId": 12603, "skinName": "无限飓风号", "heroId": "126", "heroName": "夏侯惇", "classTypeName": ["荣耀典藏"], "heroType": "坦克", "accessWay": "积分夺宝获取", "skinNum": 6}, {"skinId": 50301, "skinName": "命运角斗场", "heroId": "503", "heroName": "狂铁", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 4}, {"skinId": 11801, "skinName": "未来旅行", "heroId": "118", "heroName": "孙膑", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 8}, {"skinId": 52401, "skinName": "归虚梦演", "heroId": "524", "heroName": "蒙犽", "classTypeName": ["归虚梦演"], "heroType": "射手", "accessWay": "", "skinNum": 6}, {"skinId": 31203, "skinName": "星空之诺", "heroId": "312", "heroName": "沈梦溪", "classTypeName": ["勇者品质", "战令限定", "限定", "太空漫游"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 7}, {"skinId": 18201, "skinName": "第七人偶", "heroId": "182", "heroName": "干将莫邪", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 14106, "skinName": "猫影幻舞", "heroId": "141", "heroName": "貂蝉", "classTypeName": ["传说品质", "FMVP皮肤"], "heroType": "法师", "accessWay": "", "skinNum": 10}, {"skinId": 12806, "skinName": "天狼征服者", "heroId": "128", "heroName": "曹操", "classTypeName": ["史诗品质", "限定", "KPL限定"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 11201, "skinName": "木偶奇遇记", "heroId": "112", "heroName": "鲁班七号", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 11}, {"skinId": 16603, "skinName": "心灵战警", "heroId": "166", "heroName": "亚瑟", "classTypeName": ["史诗品质", "CP皮肤"], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 12102, "skinName": "大秦宣太后", "heroId": "121", "heroName": "芈月", "classTypeName": ["荣耀典藏"], "heroType": "法师", "accessWay": "积分夺宝获取", "skinNum": 6}, {"skinId": 17303, "skinName": "逐浪之夏", "heroId": "173", "heroName": "李元芳", "classTypeName": ["史诗品质", "夏日海滩"], "heroType": "射手", "accessWay": "", "skinNum": 9}, {"skinId": 17401, "skinName": "加勒比小姐", "heroId": "174", "heroName": "虞姬", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 8}, {"skinId": 50201, "skinName": "街头旋风", "heroId": "502", "heroName": "裴擒虎", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 6}, {"skinId": 19102, "skinName": "守护之力", "heroId": "191", "heroName": "大乔", "classTypeName": ["勇者品质", "五路精神"], "heroType": "辅助", "accessWay": "", "skinNum": 9}, {"skinId": 18701, "skinName": "东海龙王", "heroId": "187", "heroName": "东皇太一", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 4}, {"skinId": 52501, "skinName": "归虚梦演", "heroId": "525", "heroName": "鲁班大师", "classTypeName": ["归虚梦演"], "heroType": "辅助", "accessWay": "", "skinNum": 4}, {"skinId": 18404, "skinName": "繁星吟游", "heroId": "184", "heroName": "蔡文姬", "classTypeName": ["史诗品质", "限定", "源梦皮肤"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 15303, "skinName": "驯魔猎人", "heroId": "153", "heroName": "兰陵王", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "刺客", "accessWay": "荣耀战令获取", "skinNum": 7}, {"skinId": 14402, "skinName": "星际陆战队", "heroId": "144", "heroName": "程咬金", "classTypeName": ["勇者品质"], "heroType": "坦克", "accessWay": "", "skinNum": 9}, {"skinId": 15204, "skinName": "幻想奇妙夜", "heroId": "152", "heroName": "王昭君", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 19501, "skinName": "威尼斯狂欢", "heroId": "195", "heroName": "百里玄策", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 5}, {"skinId": 18901, "skinName": "阿摩司公爵", "heroId": "189", "heroName": "鬼谷子", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 5}, {"skinId": 19801, "skinName": "美梦成真", "heroId": "198", "heroName": "梦奇", "classTypeName": ["乐园午夜"], "heroType": "坦克", "accessWay": "", "skinNum": 4}, {"skinId": 50102, "skinName": "虹云星官", "heroId": "501", "heroName": "明世隐", "classTypeName": ["勇者品质"], "heroType": "辅助", "accessWay": "钻石夺宝获取", "skinNum": 5}, {"skinId": 13305, "skinName": "鹰眼统帅", "heroId": "133", "heroName": "狄仁杰", "classTypeName": ["史诗品质", "信誉专属"], "heroType": "射手", "accessWay": "信誉系统专属", "skinNum": 8}, {"skinId": 12604, "skinName": "朔风刀", "heroId": "126", "heroName": "夏侯惇", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "坦克", "accessWay": "S17赛季专属", "skinNum": 6}, {"skinId": 17701, "skinName": "维京掠夺者", "heroId": "177", "heroName": "苍", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 2}, {"skinId": 19003, "skinName": "武陵仙君", "heroId": "190", "heroName": "诸葛亮", "classTypeName": ["传说品质", "限定", "情人节限定"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 19701, "skinName": "踏雪寻梅", "heroId": "197", "heroName": "弈星", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 4}, {"skinId": 51302, "skinName": "梁祝", "heroId": "513", "heroName": "上官婉儿", "classTypeName": ["史诗品质", "限定", "周年限定", "中华曲艺"], "heroType": "法师", "accessWay": "限时活动获取", "skinNum": 6}, {"skinId": 12403, "skinName": "赤莲之焰", "heroId": "124", "heroName": "周瑜", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 6}, {"skinId": 15201, "skinName": "精灵公主", "heroId": "152", "heroName": "王昭君", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 9}, {"skinId": 19201, "skinName": "芝加哥教父", "heroId": "192", "heroName": "黄忠", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 4}, {"skinId": 11205, "skinName": "狮舞东方", "heroId": "112", "heroName": "鲁班七号", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "射手", "accessWay": "荣耀战令获取", "skinNum": 11}, {"skinId": 51801, "skinName": "幸存者", "heroId": "518", "heroName": "马超", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 5}, {"skinId": 11804, "skinName": "归虚梦演", "heroId": "118", "heroName": "孙膑", "classTypeName": ["勇者品质", "活动专属", "归虚梦演"], "heroType": "辅助", "accessWay": "限时活动获取", "skinNum": 8}, {"skinId": 18203, "skinName": "久胜战神", "heroId": "182", "heroName": "干将莫邪", "classTypeName": ["传说品质", "FMVP"], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 50203, "skinName": "天狼狩猎者", "heroId": "502", "heroName": "裴擒虎", "classTypeName": ["史诗品质", "限定", "KPL限定"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 50902, "skinName": "御銮", "heroId": "509", "heroName": "盾山", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S16赛季专属", "skinNum": 4}, {"skinId": 17404, "skinName": "云霓雀翎", "heroId": "174", "heroName": "虞姬", "classTypeName": ["传说品质", "世冠皮肤"], "heroType": "射手", "accessWay": "", "skinNum": 8}, {"skinId": 52201, "skinName": "归虚梦演", "heroId": "522", "heroName": "曜", "classTypeName": ["归虚梦演"], "heroType": "战士", "accessWay": "", "skinNum": 4}, {"skinId": 17502, "skinName": "神迹守卫", "heroId": "175", "heroName": "钟馗", "classTypeName": ["勇者品质", "战令限定", "限定", "神迹守卫"], "heroType": "辅助", "accessWay": "荣耀战令获取", "skinNum": 4}, {"skinId": 15001, "skinName": "街头霸王", "heroId": "150", "heroName": "韩信", "classTypeName": ["勇者品质"], "heroType": "刺客", "accessWay": "", "skinNum": 8}, {"skinId": 12305, "skinName": "野性能量", "heroId": "123", "heroName": "吕布", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "", "skinNum": 10}, {"skinId": 18601, "skinName": "圆桌骑士", "heroId": "186", "heroName": "太乙真人", "classTypeName": ["圆桌骑士"], "heroType": "辅助", "accessWay": "", "skinNum": 5}, {"skinId": 12903, "skinName": "蓝屏警告", "heroId": "129", "heroName": "典韦", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 7}, {"skinId": 50901, "skinName": "极冰防御线", "heroId": "509", "heroName": "盾山", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 4}, {"skinId": 12401, "skinName": "海军大将", "heroId": "124", "heroName": "周瑜", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 6}, {"skinId": 50601, "skinName": "荷鲁斯之眼", "heroId": "506", "heroName": "云中君", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 4}, {"skinId": 51101, "skinName": "年年有余", "heroId": "511", "heroName": "猪八戒", "classTypeName": [], "heroType": "坦克", "accessWay": "", "skinNum": 4}, {"skinId": 51501, "skinName": "露花倒影", "heroId": "515", "heroName": "嫦娥", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 5}, {"skinId": 18001, "skinName": "三太子", "heroId": "180", "heroName": "哪吒", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 5}, {"skinId": 10605, "skinName": "丁香结", "heroId": "106", "heroName": "小乔", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "积分夺宝获取", "skinNum": 9}, {"skinId": 50802, "skinName": "箭羽风息", "heroId": "508", "heroName": "伽罗", "classTypeName": ["史诗品质", "五五朋友节", "青春校园"], "heroType": "射手", "accessWay": "", "skinNum": 6}, {"skinId": 50202, "skinName": "梅西", "heroId": "502", "heroName": "裴擒虎", "classTypeName": ["史诗品质"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 17601, "skinName": "霓裳曲", "heroId": "176", "heroName": "杨玉环", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 4}, {"skinId": 17902, "skinName": "朔望之晖", "heroId": "179", "heroName": "女娲", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 3}, {"skinId": 16803, "skinName": "御旌", "heroId": "168", "heroName": "牛魔", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S15赛季专属", "skinNum": 6}, {"skinId": 31201, "skinName": "棒球奇才", "heroId": "312", "heroName": "沈梦溪", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 50701, "skinName": "灼热之刃", "heroId": "507", "heroName": "李信", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 3}, {"skinId": 13602, "skinName": "海洋之心", "heroId": "136", "heroName": "武则天", "classTypeName": ["传说品质"], "heroType": "法师", "accessWay": "", "skinNum": 4}, {"skinId": 11501, "skinName": "金属狂潮", "heroId": "115", "heroName": "高渐离", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 5}, {"skinId": 14903, "skinName": "德古拉伯爵", "heroId": "149", "heroName": "刘邦", "classTypeName": ["史诗品质"], "heroType": "坦克", "accessWay": "", "skinNum": 5}, {"skinId": 18403, "skinName": "舞动绿茵", "heroId": "184", "heroName": "蔡文姬", "classTypeName": ["史诗品质"], "heroType": "辅助", "accessWay": "", "skinNum": 7}, {"skinId": 13004, "skinName": "万象初新", "heroId": "130", "heroName": "宫本武藏", "classTypeName": ["勇者品质", "新春专属"], "heroType": "战士", "accessWay": "", "skinNum": 6}, {"skinId": 18302, "skinName": "冰冠公主", "heroId": "183", "heroName": "雅典娜", "classTypeName": ["史诗品质", "冰雪之歌"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 19403, "skinName": "玄武志", "heroId": "194", "heroName": "苏烈", "classTypeName": ["史诗品质", "限定", "生肖限定"], "heroType": "辅助", "accessWay": "限时活动获取", "skinNum": 4}, {"skinId": 14102, "skinName": "圣诞恋歌", "heroId": "141", "heroName": "貂蝉", "classTypeName": ["史诗品质", "CP皮肤", "圣诞颂歌"], "heroType": "法师", "accessWay": "", "skinNum": 10}, {"skinId": 11004, "skinName": "白昼王子", "heroId": "110", "heroName": "嬴政", "classTypeName": ["史诗品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 5}, {"skinId": 11703, "skinName": "海滩丽影", "heroId": "117", "heroName": "钟无艳", "classTypeName": ["勇者品质", "夏日海滩"], "heroType": "战士", "accessWay": "钻石夺宝获取", "skinNum": 6}, {"skinId": 18301, "skinName": "战争女神", "heroId": "183", "heroName": "雅典娜", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 5}, {"skinId": 11304, "skinName": "奇妙博物学", "heroId": "113", "heroName": "庄周", "classTypeName": ["勇者品质"], "heroType": "辅助", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 10604, "skinName": "缤纷独角兽", "heroId": "106", "heroName": "小乔", "classTypeName": ["史诗品质", "夏日海滩"], "heroType": "法师", "accessWay": "", "skinNum": 9}, {"skinId": 18202, "skinName": "冰霜恋舞曲", "heroId": "182", "heroName": "干将莫邪", "classTypeName": ["史诗品质", "限定", "冰雪之歌"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 19604, "skinName": "朱雀志", "heroId": "196", "heroName": "百里守约", "classTypeName": ["史诗品质", "限定", "生肖限定"], "heroType": "射手", "accessWay": "限时活动获取", "skinNum": 6}, {"skinId": 15406, "skinName": "瑞麟志", "heroId": "154", "heroName": "花木兰", "classTypeName": ["史诗品质", "限定", "生肖限定"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 9}, {"skinId": 11604, "skinName": "节奏热浪", "heroId": "116", "heroName": "阿轲", "classTypeName": ["史诗品质"], "heroType": "刺客", "accessWay": "", "skinNum": 5}, {"skinId": 14604, "skinName": "一生所爱", "heroId": "146", "heroName": "露娜", "classTypeName": ["史诗品质", "限定", "CP皮肤", "情人节限定", "大话西游"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 19303, "skinName": "青龙志", "heroId": "193", "heroName": "铠", "classTypeName": ["史诗品质", "限定", "生肖限定"], "heroType": "战士", "accessWay": "限时活动获取", "skinNum": 7}, {"skinId": 12304, "skinName": "猎兽之王", "heroId": "123", "heroName": "吕布", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "战士", "accessWay": "荣耀战令获取", "skinNum": 10}, {"skinId": 19502, "skinName": "白虎志", "heroId": "195", "heroName": "百里玄策", "classTypeName": ["传说品质", "限定", "生肖限定"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 52901, "skinName": "创世神祝", "heroId": "529", "heroName": "盘古", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 5}, {"skinId": 16706, "skinName": "大圣娶亲", "heroId": "167", "heroName": "孙悟空", "classTypeName": ["史诗品质", "限定", "CP皮肤", "情人节限定", "大话西游"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 10}, {"skinId": 50402, "skinName": "御霄", "heroId": "504", "heroName": "米莱狄", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "法师", "accessWay": "S14赛季专属", "skinNum": 6}, {"skinId": 19101, "skinName": "伊势巫女", "heroId": "191", "heroName": "大乔", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 9}, {"skinId": 19901, "skinName": "花间舞", "heroId": "199", "heroName": "公孙离", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 7}, {"skinId": 51301, "skinName": "修竹墨客", "heroId": "513", "heroName": "上官婉儿", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 6}, {"skinId": 50302, "skinName": "御狮", "heroId": "503", "heroName": "狂铁", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "战士", "accessWay": "S13赛季专属", "skinNum": 4}, {"skinId": 13201, "skinName": "激情绿茵", "heroId": "132", "heroName": "马可波罗", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 9}, {"skinId": 19301, "skinName": "龙域领主", "heroId": "193", "heroName": "铠", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 12804, "skinName": "死神来了", "heroId": "128", "heroName": "曹操", "classTypeName": [], "heroType": "战士", "accessWay": "限时出售", "skinNum": 7}, {"skinId": 17801, "skinName": "埃及法老", "heroId": "178", "heroName": "杨戬", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 6}, {"skinId": 12301, "skinName": "圣诞狂欢", "heroId": "123", "heroName": "吕布", "classTypeName": ["史诗品质", "CP皮肤", "圣诞颂歌"], "heroType": "战士", "accessWay": "", "skinNum": 10}, {"skinId": 12702, "skinName": "花好人间", "heroId": "127", "heroName": "甄姬", "classTypeName": ["勇者品质", "新春专属"], "heroType": "法师", "accessWay": "", "skinNum": 8}, {"skinId": 14001, "skinName": "龙腾万里", "heroId": "140", "heroName": "关羽", "classTypeName": ["勇者品质", "新春专属"], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 17101, "skinName": "五福同心", "heroId": "171", "heroName": "张飞", "classTypeName": ["勇者品质", "新春专属"], "heroType": "辅助", "accessWay": "", "skinNum": 4}, {"skinId": 17003, "skinName": "汉昭烈帝", "heroId": "170", "heroName": "刘备", "classTypeName": ["勇者品质", "御龙在天"], "heroType": "战士", "accessWay": "", "skinNum": 7}, {"skinId": 12602, "skinName": "乘风破浪", "heroId": "126", "heroName": "夏侯惇", "classTypeName": ["史诗品质", "夏日海滩"], "heroType": "坦克", "accessWay": "", "skinNum": 6}, {"skinId": 13304, "skinName": "阴阳师", "heroId": "133", "heroName": "狄仁杰", "classTypeName": ["勇者品质", "限定", "成就限定"], "heroType": "射手", "accessWay": "成就系统专属", "skinNum": 8}, {"skinId": 12002, "skinName": "狰", "heroId": "120", "heroName": "白起", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "坦克", "accessWay": "S8赛季专属", "skinNum": 5}, {"skinId": 10803, "skinName": "进击墨子号", "heroId": "108", "heroName": "墨子", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "限时出售", "skinNum": 6}, {"skinId": 16702, "skinName": "西部大镖客", "heroId": "167", "heroName": "孙悟空", "classTypeName": ["西部大镖客"], "heroType": "刺客", "accessWay": "", "skinNum": 10}, {"skinId": 13302, "skinName": "魔术师", "heroId": "133", "heroName": "狄仁杰", "classTypeName": ["史诗品质", "限定", "贵族限定"], "heroType": "射手", "accessWay": "贵族6级赠送", "skinNum": 8}, {"skinId": 11105, "skinName": "末日机甲", "heroId": "111", "heroName": "孙尚香", "classTypeName": ["传说品质", "末日机甲"], "heroType": "射手", "accessWay": "", "skinNum": 10}, {"skinId": 15002, "skinName": "教廷特使", "heroId": "150", "heroName": "韩信", "classTypeName": ["勇者品质"], "heroType": "刺客", "accessWay": "钻石夺宝获取", "skinNum": 8}, {"skinId": 14603, "skinName": "紫霞仙子", "heroId": "146", "heroName": "露娜", "classTypeName": ["史诗品质", "CP皮肤", "大话西游"], "heroType": "刺客", "accessWay": "", "skinNum": 7}, {"skinId": 17501, "skinName": "地府判官", "heroId": "175", "heroName": "钟馗", "classTypeName": [], "heroType": "辅助", "accessWay": "", "skinNum": 4}, {"skinId": 15403, "skinName": "水晶猎龙者", "heroId": "154", "heroName": "花木兰", "classTypeName": ["史诗品质", "五五朋友节"], "heroType": "战士", "accessWay": "", "skinNum": 9}, {"skinId": 17403, "skinName": "凯尔特女王", "heroId": "174", "heroName": "虞姬", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "射手", "accessWay": "S7赛季专属", "skinNum": 8}, {"skinId": 13901, "skinName": "潮流仙人", "heroId": "139", "heroName": "老夫子", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 6}, {"skinId": 14801, "skinName": "时尚教父", "heroId": "148", "heroName": "姜子牙", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "", "skinNum": 4}, {"skinId": 14101, "skinName": "异域舞娘", "heroId": "141", "heroName": "貂蝉", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 10}, {"skinId": 11903, "skinName": "炼金王", "heroId": "119", "heroName": "扁鹊", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "法师", "accessWay": "S6赛季专属", "skinNum": 5}, {"skinId": 16901, "skinName": "精灵王", "heroId": "169", "heroName": "后羿", "classTypeName": [], "heroType": "射手", "accessWay": "", "skinNum": 8}, {"skinId": 13103, "skinName": "凤求凰", "heroId": "131", "heroName": "<PERSON>白", "classTypeName": ["传说品质", "限定", "CP皮肤", "生肖限定"], "heroType": "刺客", "accessWay": "限时出售", "skinNum": 8}, {"skinId": 14901, "skinName": "圣殿之光", "heroId": "149", "heroName": "刘邦", "classTypeName": [], "heroType": "坦克", "accessWay": "", "skinNum": 5}, {"skinId": 10703, "skinName": "皇家上将", "heroId": "107", "heroName": "赵云", "classTypeName": ["史诗品质", "限定", "贵族限定"], "heroType": "战士", "accessWay": "贵族5级赠送", "skinNum": 10}, {"skinId": 19001, "skinName": "星航指挥官", "heroId": "190", "heroName": "诸葛亮", "classTypeName": [], "heroType": "法师", "accessWay": "", "skinNum": 7}, {"skinId": 11302, "skinName": "蜃楼王", "heroId": "113", "heroName": "庄周", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "辅助", "accessWay": "S4赛季专属", "skinNum": 8}, {"skinId": 13401, "skinName": "拳王", "heroId": "134", "heroName": "达摩", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "限时出售", "skinNum": 5}, {"skinId": 12706, "skinName": "落雪兰心", "heroId": "127", "heroName": "甄姬", "classTypeName": ["勇者品质", "战令限定", "限定"], "heroType": "法师", "accessWay": "荣耀战令获取", "skinNum": 8}, {"skinId": 15401, "skinName": "剑舞者", "heroId": "154", "heroName": "花木兰", "classTypeName": [], "heroType": "战士", "accessWay": "", "skinNum": 9}, {"skinId": 10904, "skinName": "少女阿狸", "heroId": "109", "heroName": "妲己", "classTypeName": ["勇者品质"], "heroType": "法师", "accessWay": "教学关卡获取", "skinNum": 10}, {"skinId": 10902, "skinName": "魅力维加斯", "heroId": "109", "heroName": "妲己", "classTypeName": ["史诗品质"], "heroType": "法师", "accessWay": "碎片商店兑换", "skinNum": 10}, {"skinId": 15301, "skinName": "隐刃", "heroId": "153", "heroName": "兰陵王", "classTypeName": [], "heroType": "刺客", "accessWay": "", "skinNum": 7}, {"skinId": 13503, "skinName": "海滩派对", "heroId": "135", "heroName": "项羽", "classTypeName": ["勇者品质", "限定", "贵族限定", "夏日海滩"], "heroType": "坦克", "accessWay": "贵族4级赠送", "skinNum": 8}, {"skinId": 16602, "skinName": "狮心王", "heroId": "166", "heroName": "亚瑟", "classTypeName": ["勇者品质", "赛季限定", "限定"], "heroType": "战士", "accessWay": "S3赛季专属", "skinNum": 7}, {"skinId": 16601, "skinName": "死亡骑士", "heroId": "166", "heroName": "亚瑟", "classTypeName": ["勇者品质"], "heroType": "战士", "accessWay": "碎片商店兑换", "skinNum": 7}], "roleJobName": "无双王者", "userId": "226041252", "encrypted_user_id": "6a85f3cd0c6d9d31d7de9a2b756fe020"}