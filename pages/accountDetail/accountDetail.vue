<template>
  <view class="pageCommon g-bg" style="padding-bottom: 300rpx">
    <PageNav title="账号详情" background-color="#fff"  theme="black" shadow>
      <view
        slot="right"
        style="width: 40rpx; height: 40rpx; line-height: 40rpx"
        @click="sharePage"
      >
        <IconFont :size="18" icon="share" />
      </view>
    
    </PageNav>
    <view>
      <image
        src="../../assets/imgs/logo_banner.svg"
        mode="widthFix"
        style="width: 100%; margin-top: 24rpx"
      />

      <GradientBorder :round="16" style="margin-top: 12rpx">
        <view class="commonBox top-card-box" style="padding: 18rpx 24rpx">
          <view class="account_card">
            <view
              class="spaceBetween"
              style="
                border-bottom: solid 1px #ffeed5;
                position: relative;
                padding-bottom: 16rpx;
              "
            >
              <view class="sn" @click="copyNews">
                {{ shopDetailJson.productSn }}
                <IconFont :size="16" icon="copy" style="margin-left: 8rpx" />
              </view>
              <view class="price gradient-primary">
                ¥{{ shopDetailJson.price }}
              </view>
            </view>
            <view class="spaceBetween area1">
              <view
                v-for="item in area1List"
                :key="item.value"
                class="area1item"
              >
                <view class="label">{{ item.label }}</view>
                <view class="value">{{ item.value }}</view>
              </view>
            </view>
          </view>

          <view class="acc-info">
            <view id="desc1" :class="getToggleLine" @click="toggleLine">
              <view v-if="shopDetailJson.productCategoryId == 75">
                <view><text style="word-break: break-all">
                  {{
                  toggleLineFlag && textTitleAll.length>= 80
                    ? textTitleAll.slice(0, 80) + '...'
                    : textTitleAll
                }}
                </text><text
                  v-if="textTitleAll.length >= 80"
                  :style="{
                    color: '#ff7a00',
                    marginLeft: '8rpx',
                    fontSize: '22rpx',
                  }"
                >
                  {{ toggleLineFlag ? '展开' : '收起' }}
                </text></view>
              </view>
              <view v-else style="word-break: break-all">
                {{
                  toggleLineFlag && shopDetailJson.subTitle.length >= 80
                    ? shopDetailJson.subTitle.slice(0, 80) + '...'
                    : shopDetailJson.subTitle
                }}
                <text
                  v-if="shopDetailJson.subTitle.length >= 80"
                  :style="{
                    color: '#ff7a00',
                    marginLeft: '8rpx',
                    fontSize: '22rpx',
                  }"
                >
                  {{ toggleLineFlag ? '展开' : '收起' }}
                </text>
                <!-- <IconFont
                  v-if="shopDetailJson.subTitle.length >= 80"
                  :style="{
                    color: '#969696',
                    transform: toggleLineFlag ? 'rotate(0)' : 'rotate(180deg)',
                    marginLeft: '8rpx',
                  }"
                  icon="open"
                /> -->
              </view>
            </view>

            <!-- <uni-icons
            v-if="shopDetailJson.subTitle.length > 83"
            :type="toggleLineFlag ? 'down' : 'up'"
            class="up"
            @click="toggleLine"
          ></uni-icons> -->
            <!-- <view class="spaceStart topattr">
          <view class="attr_item">
            <view>区服：</view>
            <text v-if="shopDetailJson.gameAccountQufu">
              {{ shopDetailJson.gameAccountQufu }}
            </text>
          </view>
          <view class="attr_item">
            <view>职业：</view>
            <text v-if="zhiye">
              {{ zhiye }}
            </text>
          </view>
          <text>
            {{ shopDetailJson.gameGoodsYijia == '1' ? '可议价' : '' }}
          </text>
        </view> -->
            <view class="account_attr2">
              <view class="spaceBetween attr">
                <view class="spaceStart">
                  <view class="updatetime">更新时间：</view>
                  <view>{{ shopDetailJson.updateTime | timeformatday }}</view>
                </view>
                <view style="line-height: 36rpx">
                  <text>
                    <IconFont :size="12" icon="see" class="c-primary" />{{
                      ' ' + shopDetailJson.gameSysinfoReadcount
                    }}
                  </text>
                  <text style="margin-left: 20rpx">
                    <IconFont :size="10" icon="focus" class="c-primary" />{{
                      ' ' + shopDetailJson.gameSysinfoCollectcount
                    }}
                  </text>
                </view>
              </view>
              <view class="spaceBetween">
                <view>已实名认证</view>
                <view class="spaceStart">
                  <view class="updatetime">方便交易时间：</view>
                  <view> {{ shopDetailJson.gameCareinfoTime }}</view>
                </view>
              </view>
            </view>
          </view>
          <view class="spaceBetween midW_add">
            <view class="spaceStart">
              <image
                src="../../assets/imgs/acc_detail_list1.svg"
                mode="heightFix"
                style="height: 40rpx"
              />
              <view>找回包赔</view>
            </view>
            <view class="spaceStart">
              <image
                src="../../assets/imgs/acc_detail_list2.svg"
                mode="heightFix"
                style="height: 40rpx"
              />
              <view>合同保障</view>
            </view>
            <view class="spaceStart">
              <image
                src="../../assets/imgs/acc_detail_list3.svg"
                mode="heightFix"
                style="height: 40rpx"
              />
              <view>百人团队</view>
            </view>
          </view>
        </view>
      </GradientBorder>
      <!-- 官方验号报告 -->
      <Gfyh v-if="custom.goodsType!=='vgoods'" :gfyh-data="gfyhData"/>

      <!-- Tab板块 -->
      <MyTab
        :tabs="
          teams.name && teams.list.length
            ? ['文字介绍', '详情图片', '交易须知', '交易群','卖家说']
            : ['文字介绍', '详情图片', '交易须知','卖家说']
        "
        style="margin-top: 16rpx"
        border-class="tab-border"
        body-height="fit-content"
        theme="orange"
        @click="myTabClick"
      >
        <!-- 9998 9999 9996 9997-->
        <!-- <template slot="tab-btn-0"> -->
        <!-- <text>文字介绍</text>
          <IconFont
            v-if="shopDetailJson.productCategoryId == 75"
            :size="14"
            icon="help"
            style="margin-left: 10rpx"
            @click="showHe"
          />
        </template> -->
        <template v-slot="slotProps">
          <!-- 文字介绍 -->
          <view
            v-if="slotProps.data === 0"
            class="acDt_body"
            style="padding: 18rpx 48rpx; position: relative"
          >
            <Watermark />
            <view v-if="area3List.length">
              <uni-table ref="table">
                <uni-tr style="display: none">
                  <uni-th width="120" align="center">属性</uni-th>
                  <uni-th align="center">值</uni-th>
                </uni-tr>
                <uni-tr v-for="(item, index) in area3List" :key="index">
                  <!-- <uni-td class="title">{{ item.label }}</uni-td>
                  <uni-td>
                    <rich-text
                      :nodes="formatValue(item.value)"
                      class="rich-box"
                    >
                    </rich-text>
                  </uni-td> -->
                  <view  v-if="productAttributeListName(item.label)!=2&& (item.value!== null && item.value!== undefined && item.value!== '')">
                    <uni-td style="min-width:240rpx" class="title">{{ item.label }}</uni-td>
                   <uni-td style="width:100%">
                    <rich-text
                      :nodes="formatValue(item.value)"
                      class="rich-box"
                    >
                    </rich-text>
                  </uni-td>
                   </view>
                  <uni-td style='width:100%;font-size:24rpx;font-weight:500;text-indent: -14rpx;'  v-if="productAttributeListName(item.label)==2&& (item.value!== null && item.value!== undefined && item.value!== '')"> <rich-text
                      :nodes="formatValue( `【${item.label}】` + (item.value || '') )"
                      class="rich-box"
                    >
                    </rich-text>
                  </uni-td>
                </uni-tr>
              </uni-table>
              <uni-table
                v-if="area4List.length"
                ref="table"
                style="border-top: 0"
              >
                <uni-tr style="display: none">
                  <uni-th width="120" align="center">属性</uni-th>
                  <uni-th align="center">值</uni-th>
                </uni-tr>
                <uni-tr v-for="(item, index) in area4List" :key="index">
                  <view  v-if="productAttributeListName(item.label)!=2&& (item.value!== null && item.value!== undefined && item.value!== '')">
                    <uni-td style="min-width:240rpx" class="title">{{ item.label }}</uni-td>
                   <uni-td style="width:100%">
                    <rich-text
                    
                      :nodes="formatValue(item.value)"
                      class="rich-box"
                    >
                    </rich-text>
                  </uni-td>
                   </view>

                  <uni-td style='width:100%;font-size:24rpx;font-weight:500;text-indent: -14rpx;'  v-if="productAttributeListName(item.label)==2&& (item.value!== null && item.value!== undefined && item.value!== '')"> <rich-text
                      :nodes="formatValue( `【${item.label}】` + (item.value || '') )"
                      class="rich-box"
                    >
                    </rich-text>
                  </uni-td>
                
                 
                </uni-tr>
              </uni-table>
              <uni-table
                v-if="area4Listzhms.length"
                ref="table"
                style="border-top: 0"
              >
                <uni-tr style="display: none">
                  <uni-th width="120" align="center">属性</uni-th>
                  <uni-th align="center">值</uni-th>
                </uni-tr>
                <!-- 账号描述 -->
                <uni-tr v-for="(item, index) in area4Listzhms" :key="index">
                  <!-- <uni-td class="title" style="border-bottom: none">{{
                    item.label
                  }}</uni-td> -->
                  <uni-td
                    v-if="(item.value!== null && item.value!== undefined && item.value!== '')"
                    id="desc2"
                    style="position: relative; border-bottom: none;font-size:24rpx;font-weight:500;text-indent: -14rpx;"
                  >
                    <view>
                      <!-- <rich-text
                        :class="getToggleLine2"
                        :nodes="formatValue(item.value)"
                        style="word-break: break-all"
                        @click="toggleLine2"
                      >
                      </rich-text> -->
                      <rich-text
                      :nodes="formatValue( `【${item.label}】` + (item.value || '') )"
                      :class="getToggleLine2"
                      style="word-break: break-all"
                      @click="toggleLine2"
                    >
                    </rich-text>
                    </view>
                    <view
                      v-if="toggleLineFlag2"
                      class="up2"
                      @click="toggleLine2"
                    >
                      <text v-if="getToggleLine2"
                        >展开全部<IconFont
                          :size="12"
                          icon="show"
                          class="c-primary"
                      /></text>
                      <text v-else>
                        收回全部
                        <IconFont :size="12" icon="hide" class="c-primary"
                      /></text>
                    </view>
                  </uni-td>
                </uni-tr>
              </uni-table>
            </view>
            <view v-else>
              <pre v-if="description" class="descriptionBox">{{
                description
              }}</pre>
            </view>
          </view>
          <!-- 详情图片 -->
          <ScrollNav
            v-if="
              albumPicsByType && albumPicsByType.length && slotProps.data === 1
            "
            :nav-list="albumPicsByType"
            label-name="type"
            class="fix-head"
            @tabClick="changeNtype"
          ></ScrollNav>
          <!-- &&showWzImg -->
          <view
            v-if="slotProps.data === 1"
            class="acDt_body"
            style="padding: 0rpx 0rpx 28rpx 0rpx"
          >
            <!-- 王者 -->
            <view v-if="wzryId">
              <view class="wzChangeBox">
                <view class="wztab_box">
                  <view
                    :class="activeName == 'skin' ? 'active' : ''"
                    class="wztab"
                    @click="changeWzTab('skin')"
                    >皮肤</view
                  >
                  <view
                    :class="activeName == 'hero' ? 'active' : ''"
                    class="wztab"
                    @click="changeWzTab('hero')"
                    >英雄</view
                  >
                </view>
                <view v-if="activeName == 'skin'">
                  <view class="spaceStart">
                    <view
                      :class="activeType == 'career' ? 'active' : ''"
                      class="type_btn"
                      @click="changeActiveType('career')"
                      >职业</view
                    >
                    <view
                      :class="activeType == 'rare' ? 'active' : ''"
                      class="type_btn"
                      @click="changeActiveType('rare')"
                      >稀有度</view
                    >
                  </view>
                  <view v-if="activeType == 'career'" class="spaceStart wztype">
                    <view
                      v-for="(item, index) in wzList"
                      :class="item === wztype ? 'active' : ''"
                      :key="index"
                      class="item"
                      @click="changeWztype(item)"
                    >
                      {{ item }}
                    </view>
                  </view>
                </view>
              </view>
              <view style="margin-top: 100rpx;">
                <view v-if="activeName == 'skin'">
                  <view v-if="activeType == 'career'">
                    <view
                      v-for="(item, index) in arrDtPicForShow2"
                      :key="index"
                      class="spaceStart skin_box"
                    >
                      <view class="hero">
                        <view class="imgbox">
                          <trigger-lazyLoad
                            :minTimeOut="'0'"
                            :showDistance="{ bottom: 500 }"
                            :src="item.heroIcon"
                            class="img"
                          ></trigger-lazyLoad>
                        </view>
                        <view class="name">{{ item.heroName }}</view>
                        <view class="namenum"
                          >{{ item.list.length }}/{{ item.skinNum }}</view
                        >
                      </view>
                      <view class="skin_right">
                        <view
                          v-for="(ele, idx) in item.list"
                          :key="idx"
                          class="skin spaceStart"
                        >
                          <view class="skinimg">
                            <!-- <trigger-lazyLoad
                            :minTimeOut="0"
                            :showDistance="{ bottom: 500 }"
                            :src="ele.skinImg"
                          ></trigger-lazyLoad> -->
                          
                            <trigger-lazyLoad
                            :minTimeOut="'0'"
                            :showDistance="{ bottom: 500 }"
                            :src="ele.skinImg"
                            class="img"
                          ></trigger-lazyLoad>
                            <view class="skinname">{{ ele.skinName }}</view>
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                  <view v-else class="skin_box2">
                    <view
                      v-for="(item, index) in arrDtPicForShow2"
                      :key="index"
                    >
                      <view class="name"
                        >{{ item.name }} {{ item.list.length }}</view
                      >
                      <view class="spaceStart skins">
                        <view
                          v-for="(ele, idx) in item.list"
                          :key="idx"
                          class="skin"
                        >
                          <trigger-lazyLoad
                          :minTimeOut="'0'"
                          :showDistance="{ bottom: 500 }"
                          :src="ele.skinImg"
                        ></trigger-lazyLoad>
                          <!-- <image
                            :src="ele.skinImg"
                            mode="widthFix"
                            class="img"
                          /> -->
                          <view class="skinname">{{ ele.skinName }}</view>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
                <view v-else class="hero_box">
                  <view v-for="(item, index) in arrDtPicForShow3" :key="index">
                    <view class="name">
                      {{ item.name }} {{ item.list.length }}/{{
                        index == 0 ? '11' : '110'
                      }}
                    </view>
                    <view class="spaceStart heros">
                      <view
                        v-for="(ele, idx) in item.list"
                        :key="idx"
                        class="hero_warp hero"
                      >
                        <trigger-lazyLoad
                        :minTimeOut="'0'"
                        :showDistance="{ bottom: 500 }"
                        :src="ele.heroIcon"
                      ></trigger-lazyLoad>
                        <!-- <image
                          :src="ele.heroIcon"
                          mode="widthFix"
                          class="img"
                        /> -->
                        <view class="hero_name">{{ ele.name }}</view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 其他游戏 -->
            <view v-else>
              <view v-if="arrDtPicForShow && arrDtPicForShow.length > 0">
                <view
                  v-for="(item, index) in arrDtPicForShow"
                  :key="index"
                  class="picmain"
                  @click="previewImg(index)"
                >
                  <!-- <trigger-lazyLoad
                  :minTimeOut="0"
                  :showDistance="{ bottom: 500 }"
                  :src="item"
                ></trigger-lazyLoad> -->
                  <image :src="item" mode="widthFix" class="img" />
                </view>
              </view>
            </view>
          </view>
          <!-- 交易须知 -->
          <view
            v-if="slotProps.data === 2"
            class="acDt_body"
            style="min-height: 400rpx"
          >
            <mp-html
              :show-img-menu="false"
              :lazy-load="false"
              :content="introduceHtml"
            >
            </mp-html>
          </view>
          <!-- 交易群 -->
          <view
            v-if="teams.name && teams.list.length&&slotProps.data === 3"
            class="acDt_body"
            style="padding: 40rpx"
          >
            <view
              v-for="(item, index) in teams.list"
              :key="index"
              class="spaceBetween qqlist_item"
            >
              <image
                src="../../assets/imgs/logo.svg"
                mode="widthFix"
                style="width: 70rpx"
              />
              <view class="">
                <view class="title gradient-main-primary">{{
                  teams.name
                }}</view>
                <view class="qqcode">QQ：{{ item.code }}</view>
              </view>
              <view class="kk-btn orange sm" @click="goQQ(item)">立即加群</view>
            </view>
          </view>
          <view  v-if="teams.name && teams.list.length&&slotProps.data === 4||!teams.name&&slotProps.data === 3"  class="acDt_body"
          style="padding: 18rpx 28rpx; position: relative">
            <uni-table v-if="(description&&description.length<50)||consultationRecordsList.length>0" ref="table">
                <uni-tr style="display: none">
                  <uni-th width="120" align="center">属性</uni-th>
                  <uni-th align="center">值</uni-th>
                </uni-tr>
                <uni-tr v-if="description&&description.length<50">
                  <view style="display: flex;flex-direction: column;">
                    <uni-td style="min-width:100%" class="title">
                      <view  style="margin-top: 10rpx;font-size: 24rpx" class="spaceStartITStart">
                        <view> {{ description }}</view>
                      </view>
                    </uni-td>
                   </view>
                </uni-tr>
                <uni-tr v-for="(item, index) in consultationRecordsList" :key="index">
                  <view  style="display: flex;flex-direction: column;">
                    <uni-td style="min-width:100%" class="title">
                      <view class="spaceStartITStart" style="font-size: 24rpx;">
                        <img style="width: 32rpx;margin-right: 10rpx;" src="../../assets/imgs/wen.png"/>
                       <text> {{ item.consultQuestion }}</text>
                      </view>
                      <view  style="margin-top: 10rpx;font-size: 20rpx;color: #9a9a9a;" class="spaceStartITStart">
                        <img style="width: 32rpx;margin-right: 10rpx;"  src="../../assets/imgs/da.png"/>
                        <view> {{ item.consultContent }}</view>
                      </view>
                    </uni-td>
                   </view>
                </uni-tr>
                <uni-tr >
                  <view style="display: flex;flex-direction: column;">
                    <uni-td style="min-width:100%" class="title title_text">
                      <view  style="margin-top: 10rpx;font-size: 20rpx" class="spaceStartITStart">
                        <view>温馨提示：卖家说内容为号主历史咨询答复，不作为交易依据</view>
                      </view>
                    </uni-td>
                   </view>
                </uni-tr>
              </uni-table>
              <view v-else style="color: #ff720c;text-align: center;font-size: 24rpx;">暂无卖家咨询答复</view>
          </view>
          <!-- <view  v-if="!teams.name&&slotProps.data === 3"  class="acDt_body" style="padding: 18rpx 28rpx; position: relative;top: -80rpx;">
            <uni-table v-if="(description&&description.length<50)||consultationRecordsList.length>0" ref="table">
                <uni-tr style="display: none">
                  <uni-th width="120" align="center">属性</uni-th>
                  <uni-th align="center">值</uni-th>
                </uni-tr>
                <uni-tr v-if="description&&description.length<50">
                  <view style="display: flex;flex-direction: column;">
                    <uni-td style="min-width:100%" class="title">
                      <view  style="margin-top: 10rpx;font-size: 24rpx" class="spaceStartITStart">
                        <view> {{ description }}</view>
                      </view>
                    </uni-td>
                   </view>
                </uni-tr>
                <uni-tr v-for="(item, index) in consultationRecordsList" :key="index">
                  <view  style="display: flex;flex-direction: column;">
                    <uni-td style="min-width:100%" class="title">
                      <view class="spaceStartITStart" style="font-size: 24rpx;">
                        <img style="width: 32rpx;margin-right: 10rpx;" src="../../assets/imgs/wen.png"/>
                       <text> {{ item.consultQuestion }}</text>
                      </view>
                      <view  style="margin-top: 10rpx;font-size: 20rpx;color: #9a9a9a;" class="spaceStartITStart">
                        <img style="width: 32rpx;margin-right: 10rpx;"  src="../../assets/imgs/da.png"/>
                        <view> {{ item.consultContent }}</view>
                      </view>
                    </uni-td>
                   </view>
                </uni-tr>
              </uni-table>
              <view v-else style="color: #ff720c;text-align: center;font-size: 24rpx;">暂无卖家咨询答复</view>
          </view> -->
        </template>
      </MyTab>
      <!-- 下单流程 -->
      <FlowAgreement :is-vgoods="custom.goodsType==='vgoods'"/>
      <!-- 底部 -->
      <view class="accountDt_footer">
        <Wechat />
        <view class="spaceBetween accountDt_footer_box">
          <view class="spaceStart">
            <Collect
              :product-id="productId"
              :shop-detail-json="shopDetailJson"
              @refresh="getShopDtFun"
            />
            <view
              v-if="shopNumber"
              class="accountDt_footerItem"
              style=" margin-right: 20rpx;"
              @click="goShopFun"
            >
              <view style="margin-bottom: 10rpx">
                <IconFont
                  :size="19"
                  icon="dianpu"
                />
              </view>
              <view>店铺</view>
            </view>
          </view>
          <view class="spaceEnd" style="flex: 1;">
            <view
              v-if="canBuy && shopDetailJson.gameGoodsYijia === 1"
              class="kk-btn line"
              style="width: 130rpx;position: relative;"
              @click="postPriceFun"
            >
              <span>砍价</span>
              <img style="width: 80rpx;height: 32rpx;position: absolute;top: -10rpx;right: 2rpx;z-index: 99;" src="../../assets/imgs/hot_icon.png" alt="">
            </view>
            <view
              v-if="shopDetailJson.productCategoryName"
              class="kk-btn line"
              style="width: 130rpx;margin-left: 10rpx;"
              @click="popUpCustorm"
            >
              <span>咨询</span>
              
            </view>

            <view
              v-if="ifBuyBtn"
              class="kk-btn primary"
              style="min-width: 200rpx; flex: 1 1 0; margin-left: 10rpx;"
              @click="payNow"
            >立即购买</view>
            <view
              v-if="shopDetailJson.stock == 0"
              class="kk-btn primary"
              style="margin-left: 32rpx"
            >已成交</view>
            <view
              v-if="shopDetailJson.stock == 1"
              class="kk-btn primary"
              style="margin-left: 32rpx"
            >已预订</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 废 -->
    <uni-popup ref="showHePopup" type="center">
      <view class="he_box">
        <view class="qian">
          <image
            class="img"
            src="../../static/old/price.png"
            mode="scaleToFill"
          />
          <view>代表高价值道具</view>
        </view>
        <view class="he">
          <image class="img" src="../../static/old/he.png" mode="scaleToFill" />
          <view>代表核心道具</view>
        </view>
        <view class="jue">
          <image
            class="img"
            src="../../static/old/jue.png"
            mode="scaleToFill"
          />
          <view>代表绝版道具</view>
        </view>
      </view>
    </uni-popup>

    <!-- 议价 -->
    <Bargain
      v-if="showBargain"
      :data-info="shopDetailJson"
      :max-price="maxHeightPrice"
      :history-top-price="historyTopPrice"
      @confirm="sureSubmit"
      @cancel="showBargain = false"
    />

    <!-- 选择定金支付方式 -->
    <payPopup
      v-if="showPayPopup"
      :price="pay_price"
      title="选择定金支付方式"
      @payNowFun="payNowFun"
      @close="showPayPopup = false"
    ></payPopup>

    <!-- 特点介绍 废 -->
    <uni-popup ref="tedianPeop" type="share" style="z-index: 999">
      <view class="tedian_desc_wrap">
        <view class="spaceStart" style="padding-bottom: 20rpx">
          <image
            class="tedian_desction_pic"
            src="../../static/old/price.png"
          ></image>
          <view class="tedian_desction_text one">代表高价值道具</view>
        </view>
        <view class="spaceStart" style="padding-bottom: 20rpx">
          <image
            class="tedian_desction_pic"
            src="../../static/old/he.png"
          ></image>
          <view class="tedian_desction_text two">代表核心道具</view>
        </view>
        <view class="spaceStart">
          <image
            class="tedian_desction_pic"
            src="../../static/old/jue.png"
          ></image>
          <view class="tedian_desction_text three">代表绝版道具</view>
        </view>
      </view>
    </uni-popup>

    <!-- 实名认证 -->
    <TipPanel
      v-if="showRealPop"
      :has-cancel="false"
      sub-title="实名认证"
      confirm-text="实名认证"
      @cancel="showRealPop = false"
      @confirm="goRealName"
    >
      <view
        class="second-tip-panel-txt pre"
        style="width: 80%; margin-left: auto; margin-right: auto"
        >尊敬的用户您好，根据相关法律法规要求，需实名认证方可进行售卖。
      </view>
    </TipPanel>
    <TipPanel
      v-if="showGoodsNum"
      sub-title="购买件数"
      confirm-text="确定"
      @cancel="showGoodsNum = false"
      @confirm="goConFirmOrder"
    >
      <view>
        <view class="spaceBetween goodsNumBox">
          <view class="title_box"
            >购买件数<text class="title_text"
              >（可购买<text>{{ goodsMaxNum }}</text
              >件）</text
            ></view
          >
          <uni-number-box
            :class="
              goodsCount === 1
                ? 'goodsNumInputDisbaleLeft'
                : goodsCount >= goodsMaxNum
                  ? 'goodsNumInputDisbaleRight'
                  : ''
            "
            :min="1"
            :max="goodsMaxNum"
            v-model="goodsCount"
            class="goodsNumInput"
            style="border: none"
          />
        </view>
      </view>
    </TipPanel>
    <!-- 客服 -->
    <KF ref="kf" />

    <RightFixNav></RightFixNav>
  </view>
</template>

<script>
import RightFixNav from '@/components/toolbar/RightFixNav.vue';
import uniPopup from '@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue';
import uniTable from '@/uni_modules/uni-table/components/uni-table/uni-table.vue';
import Watermark from './components/watermark.vue';
import Wechat from './components/wechat.vue';
// import list from './1.json'
import isLogin from '@/common/mixins/isLogin';
import { mapState } from 'vuex';
import {
  getSkinAndHero,
  getDetail,
  getDetailByCode,
  getPreview,
  readHistoryCreate,
  topOfferPrice,
} from '@/config/api/accountDetail.js';
import { getCertDetail } from '@/config/api/safeCenter.js';
import { getProductCategory } from '@/config/api/submitAccount.js';
import { getHelpDetail } from '@/config/api/help';
import { generateKKOrder2,productConsultationRecords,freeNegoOffer} from '@/config/api/confirmOrder.js';
import ScrollNav from './components/ScrollNav.vue';

// import Bargain from './components/bargain.vue';
import Bargain from '@/components/bargain/index.vue'
import payPopup from '@/components/popup/PayPopup';
import KF from '@/components/Chat/kf.vue';
import FlowAgreement from './components/flowAgreement.vue';
import Collect from './components/collect.vue';
import Gfyh from './components/gfyh.vue';
import { getWxQrcode } from '@/config/api/main.js';
import goWxMixi from '@/common/mixins/goWx';
const OPTSOUT = {
  '1': '面板属性',
  '2': '打造内功',
  '3': '天赏外观',
  '4': '普通外观',
  '5': '其他物品',
};
// const OPTSOUTSORT = {
//   '面板属性': 1,
//   '打造内功': 2,
//   '天赏外观': 3,
//   '普通外观': 4,
//   '其他物品': 5,
// };
const WZHERO = [
  '武则天',
  '敖隐',
  '嬴政',
  '娜可露露',
  '不知火舞',
  '橘右京',
  '艾琳',
  '夏洛特',
  '金蝉',
  '赵云',
  '韩信',
];

export default {
  components: {
    uniPopup,
    uniTable,
    RightFixNav,
    Watermark,
    ScrollNav,
    Bargain,
    payPopup,
    FlowAgreement,
    Collect,
    KF,
    Gfyh,
    Wechat,
  },
  mixins: [goWxMixi],
  onPageScroll(e) {
    if (e.scrollTop > 350) {
      this.showBackTop = true;
    } else {
      this.showBackTop = false;
    }
  },
  data() {
    return {
      shopNumber:'',
      consultationRecordsList:[],
      showRealPop: false, // 显示认证弹窗
      showBargain: false, //显示议价弹窗
      showPayPopup: false, // 显示支付弹窗
      showGoodsNum: false, // 选择商品数量
      productAttributeValueList: [],
      productAttributeList: [],
      custom2: {},
      area1List: [],
      area2List: [],
      area3List: [],
      area4List: [],
      area4Listzhms: [],
      introduceHtml: '',
      zhiye: '',
      xinbie: '',
      description: '',
      userInfo: {},
      custom: {},
      teams: {},
      tableData: [],
      tagsKK: '',
      isPreview: false,
      showBackTop: false, // 返回顶部
      indicatorDots: true,
      autoplay: true,
      interval: 3500,
      duration: 500,
      indicatorColor: '#e9e1e1',
      indicatorActiveColor: '#FC6116',
      bannerList: [],
      arrDtPic: [], // 详情图
      albumPicsByType: [],
      ntypeActive: '',
      productId: '', // 商品id
      shopDetailJson: { subTitle: '' },
      maxHeightPrice: 0, // 当前最高议价
      historyTopPrice: 0, // 历史最高议价
      bottomData: [], // 客服数据
      dicker_id: '', // 议价订单id
      pay_price: '', // 议价支付金额

      arrDtPicForShow: [],
      arrDtPicForShow2: [],
      arrDtPicForShow3: [],
      activeName: 'skin',
      activeType: 'career',
      wztype: '全部',
      wzList: ['全部', '射手', '法师', '坦克', '刺客', '战士', '辅助'],
      wzryId: '',
      autokanjia: false,
      productCategory: {},
      imUniLoading: false,
      toggleLineFlag: true,
      toggleLineFlag2: true,
      getToggleLine: '',
      getToggleLine2: '',
      saveCustom: {},
      qrcodeSize: 190,
      goodsCount: 1,
      goodsMaxNum: 2,

      gfyhData:[],
      showWzImg:false
    };
  },
  computed: {
    canBuy() {
      return (
        this.shopDetailJson.publishStatus == 1 &&
        this.saveCustom.buyType !== 'kefu' &&
        this.shopDetailJson.price &&
        this.shopDetailJson.price != 0 &&
        (this.shopDetailJson.stock === 9||this.shopDetailJson.stock === 8) &&
        this.shopDetailJson.memberId != this.$store.state.userInfo.id
      );
    },
    ifBuyBtn() {
      return (
        this.shopDetailJson.publishStatus == 1 &&
        this.shopDetailJson.price &&
        this.shopDetailJson.price != 0 &&
        (this.shopDetailJson.stock === 9||this.shopDetailJson.stock === 8) &&
        this.shopDetailJson.memberId != this.$store.state.userInfo.id
      );
    },
    ...mapState({
      userInfoDetail: (state) => state.userInfo,
    }),
    textTitleAll(){
      return this.description ? `【${this.description}】${this.getTdTxt(this.productAttributeValueList)}` : this.getTdTxt(this.productAttributeValueList);
    }
  },
  onShow() {
    if (isLogin()) {
      this.$store.dispatch('setUserInfoStore');
      // 实名信息
      getCertDetail().then((res) => {
        if (res.code == 200) {
          this.userInfo = res.data || {};
        }
      });
    }
  },
  mounted() {
    if (this.autokanjia == 1) {
      this.postPriceFun();
    }
    // && !this.$store.state.userInfo.isWxPush;
  },
  onLoad(e) {
    this.autokanjia = e.autokanjia;
    if (e.isPreview) {
      this.isPreview = true;
    }
    if (e.productId) {
      this.productId = e.productId;
      // uni.setStorageSync('guessProductId', this.productId);
      // this.flag_id = e.flag_id;
      this.getShopDtFun().then(() => {
        this.getJYXZInfo();
      });
    } else if (e.productSn) {
      // 支持编码搜索
      this.productSn = e.productSn;
      this.getShopDtFun();
    }
  },
  // 下拉刷新监听
  onPullDownRefresh() {
    setTimeout(() => {
      uni.stopPullDownRefresh();
    }, 1000);
    this.getShopDtFun();
  },
  methods: {
    goShopFun(){
      uni.navigateTo({
        url:'/pages/shop/shop?id='+this.shopNumber
      })
    },
    getTdTxt(item) {
      // if (item.productCategoryId != 75) {
      //   // 诛仙世界
      //   return item.description;
      // } else {
        // 逆水寒等
        const str1 = item
          .filter((ele) =>
            ['稀有外观', '天赏祥瑞', '天赏发型'].includes(ele.productAttributeName),
          )
          .map((ele) => ele.value)
          .join(' ');
        const str2 = item
          .filter(
            (ele) =>
              ['灵韵数量', '天霓染'].includes(ele.productAttributeName) &&
              ele.value &&
              ele.value !== '0',
          )
          .map((ele) => ele.productAttributeName + ele.value)
          .join(' ');
        return item.description?item.description:(str2 + ' ' + str1).replace('数量', '').replace(/,/g, ' ');
      // }
    },
    myTabClick(data){
      console.log(data,222222)
      if(this.teams.name && this.teams.list.length){
        if(data==4){
        productConsultationRecords({productSn: this.shopDetailJson.productSn}).then(res=>{
          this.consultationRecordsList=res.data
        })
      }
      }else{
        if(data==3){
        productConsultationRecords({productSn: this.shopDetailJson.productSn}).then(res=>{
        
          this.consultationRecordsList=res.data
        })
      }
      }
     
      if(data==1){
        setTimeout(()=>{
          this.showWzImg=true
        })
      }else{
        this.showWzImg=false
      }
    },
    productAttributeListName(name){
      const attribute = this.productAttributeList.find(attr => attr.name === name);
      return attribute ? attribute.selectType : null;
    },
    toggleLine() {
      this.toggleLineFlag = !this.toggleLineFlag;
    },
    toggleLine2() {
      if (this.getToggleLine2 === '') {
        this.getToggleLine2 = 'text_linThree';
      } else {
        this.getToggleLine2 = '';
      }
    },
    showHe() {
      this.$refs.showHePopup.open('center');
    },
    backFun() {
      uni.navigateBackCustom();
    },
    changeNtype(item) {
      this.ntypeActive = item.type;
      if (this.ntypeActive === '全部图片') {
        this.arrDtPicForShow = this.arrDtPic;
      } else {
        const findIt = this.albumPicsByType.find(
          (ele) => this.ntypeActive == ele.type,
        );
        this.arrDtPicForShow = findIt ? findIt.items || [] : [];
      }
    },
    tedianFilter(text) {
      if (!text) {
        return '';
      }
      return text
        .replace(/\[核\]/g, '')
        .replace(/\[绝\]/g, '')
        .replace(/\[钱\]/g, '');
    },
    formatValue(value) {
      if (!value) return '';
      return value
        .replace(/[,]/g, '，')
        .replace(
          /\[核\]/g,
          '<img class="tag_tedian_pic" src="../../static/old/he.png" />',
        )
        .replace(
          /\[绝\]/g,
          '<img class="tag_tedian_pic" src="../../static/old/jue.png" />',
        )
        .replace(
          /\[钱\]/g,
          '<img class="tag_tedian_pic" src="../../static/old/price.png" />',
        );
    },
    showTedianDesc() {
      this.$refs.tedianPeop.open('center');
    },
    iconFilter(v) {
      if (v.icon == '1') {
        return '../../static/old/jue.png';
      } else if (v.icon == '2') {
        return '../../static/old/price.png';
      } else if (v.icon == '3') {
        return '../../static/old/he.png';
      }
    },
    goQQ(date) {
      if (date.links) {
        // #ifdef H5
        window.location.href = date.links;
        // #endif
        // #ifdef APP-PLUS
        plus.runtime.openURL(date.links);
        // #endif
      } else if (date.url) {
        uni.previewImage({
          current: 0,
          urls: [date.url],
          indicator: 'number',
        });
      }
    },
    // 交易须知信息
    getJYXZInfo() {
      let id = 269058;
      if ([75, 128, 112].includes(this.shopDetailJson.productCategoryId)) {
        id = 161;
      }
      getHelpDetail(id).then((res) => {
        if (res.code == 200) {
          this.introduceHtml = res.data.detailHtml;
        }
      });
    },
    // 分享
    sharePage() {
      uni.navigateTo({
        url: '/pages/share/share?productId=' + this.productId,
      });
    },
    // 复制订单编号
    copyNews() {
      var str = this.shopDetailJson.productSn;
      uni.setClipboardData({
        data: `${str}`,
        success: function () {},
      });
    },
    getAttrTypeTxt(item, str) {
      const str1 = item
        .filter((ele) => [str].includes(ele.productAttributeName))
        .map((ele) => ele.value)
        .join(' ');
      return str1;
    },
    goConFirmOrder() {
      uni.navigateTo({
        url: `/pages/confirmOrder/confirmOrder?productId=${this.productId}&productCategoryId=${this.shopDetailJson.productCategoryId}&quantity=${this.goodsCount}`,
      });
    },
    // 获取客服onShow
    payNow() {
      if (!isLogin()) {
        uni.navigateTo({
          url: '/pages/login/login?isBack=1',
        });
        return;
      }
      if(this.shopDetailJson.stock === 8){
        uni.showToast({
              title: '非卖品，如有购买需求可咨询客服推荐其他的可购买商品.',
              icon: 'none',
            });
            return
      }
      if (this.saveCustom && this.saveCustom.goodsType === 'vgoods') {
        let pushAccountNum = this.getAttrTypeTxt(
          this.productAttributeValueList,
          '发布件数',
        );
        if (pushAccountNum && pushAccountNum > 1) {
          this.showGoodsNum = true;
          this.goodsMaxNum = Number(pushAccountNum);
          return;
        }
      }
      if (this.userInfo.defaultStatus != 2) {
        this.showRealPop = true;
        return;
      }
      if(this.saveCustom.buyType == 'kefu'){
        this.popUpCustorm();
        return
      }
      if (this.shopDetailJson.price >= 50000) {
        this.popUpCustorm();
      } else {
        uni.navigateTo({
          url: `/pages/confirmOrder/confirmOrder?productId=${this.productId}&productCategoryId=${this.shopDetailJson.productCategoryId}`,
        });
      }
    },
    goRealName() {
      this.showRealPop = false;
      uni.navigateTo({
        url: '/pages/identify/identify',
      });
    },
    // 客服弹层出来
    popUpCustorm() {
      this.$refs.kf.goChat({
        productId: this.productId,
        productCategoryId: this.shopDetailJson.productCategoryId,
        game: this.shopDetailJson.productCategoryName,
      });
    },
    // 点击某个会话，跳转到会话详情-客服
    async clickConversationKefu(id) {
      // 创建一个会话
      // var message = await IMSDK.asyncApi(
      //   IMMethods.GetOneConversation,
      //   IMSDK.uuid(),
      //   {
      //     sourceID: id,
      //     sessionType: 1,
      //   },
      // );
      // prepareConversationState(message.data);
    },
    // 出价-先获取最高议价
    postPriceFun() {
      if (!isLogin()) {
        uni.navigateTo({
          url: '/pages/login/login?isBack=1',
        });
      } else {
        topOfferPrice({
          productId: this.productId,
        })
          .then((res) => {
            if (res.code == 200) {
              const { hisTopPrice, nowTopPrice,offerPrice } = res.data;
              this.historyTopPrice = hisTopPrice.offerPrice || {};
              this.maxHeightPrice = nowTopPrice.offerPrice||'';
            }
          })
          .finally(() => {
            this.showBargain = true;
          });
      }
    },
    // 议价确定出价
    sureSubmit(price) {
      uni.showLoading({
        title: '拼命加载中',
      });
      let data = {
        offerPrice: price,
        productId: this.productId,
      };
      freeNegoOffer(data).then((res) => {
        uni.hideLoading();
        if (res.code == 200) {
          this.showBargain = false;
         
          uni.switchTab({ url: '/pages/tabBar/news/news' });
         setTimeout(()=>{
            uni.showToast({
              title: res.data || '获取数据失败.',
              icon: 'none',
            });
          })
        }
      });
      // let data = {
      //   buyType: 3,
      //   memberPrice: price,
      //   productId: this.productId,
      //   // #ifdef H5
      //   sourceType: 1,
      //   // #endif
      // };
      // generateKKOrder2(data).then((res) => {
      //   uni.hideLoading();
      //   if (res.code == 200) {
      //     this.showBargain = false;
      //     this.dicker_id = res.data.id;
      //     this.pay_price = res.data.payAmount;
      //     // this.$refs.popup.open('bottom');
      //     this.showPayPopup = true;
      //   }
      // });
    },
    // 定金支付
    payNowFun(payMethod) {
      uni.navigateTo({
        url:
          '/pages/payOrder/payOrder?order_id=' +
          this.dicker_id +
          '&method=' +
          payMethod +
          '&isDicker=1',
      });
    },
    formatAttr(productAttributeList, productAttributeValueList) {
      // this.tableData = [];
      productAttributeList.sort((a, b) => {
        return a.type - b.type;
      });
      productAttributeList.sort((a, b) => {
        return a.sort - b.sort;
      });

      productAttributeList.forEach((ele) => {
        const { selectType, inputType, name } = ele;
        const findIt = productAttributeValueList.find((item) => {
          if (ele.name == '营地ID' && item.productAttributeId === ele.id) {
            this.wzryId = item.value;
          }
          return (
            item.productAttributeId === ele.id &&
            (ele.type === 1 || ele.type === 2)
          );
        });
        if (findIt) {
          if (ele.ename === 'tagsKKList') {
            let value = findIt.value || '';
            this.tagsKK = value && value.split(',').join(' ');
          }
          // null,undefined,''都不行
          if (!_.isNil(findIt.value) && findIt.value !== '') {
            // if (selectType == 2 && inputType == 1) {
            //   // this.description = this.tedianFilter(
            //   //   `【${name}】${findIt.value}\n${this.description}`,
            //   // );
            // } else {
            if (ele.name == '职业') {
              this.zhiye = findIt.value;
            }
            if (ele.name == '性别') {
              this.xinbie = findIt.value;
            }
            // this.tableData.push({
            //   name: ele.name,
            //   value: findIt.value,
            // });
            // }
          }
        }
      });
    },
    groupPics() {
      const product = this.shopDetailJson;
      let albumPicsJson = product.albumPicsJson ? product.albumPicsJson : '[]';
      albumPicsJson = JSON.parse(albumPicsJson);
      this.albumPicsByType = this.groupAlbumPics(albumPicsJson);
      if (this.albumPicsByType.length) {
        this.ntypeActive = this.albumPicsByType[0].type;
      } else {
        this.ntypeActive = '全部图片';
      }
      this.changeNtype({
        type: this.ntypeActive,
      });
    },
    getProductCategory() {
      getProductCategory(this.shopDetailJson.productCategoryId).then((res) => {
        if (res.code == 200) {
          // 获取客服列表
          this.$refs.kf.getKfObj(this.productCategory.name);

          const custom = JSON.parse(res.data.custom || '{}');

          this.productCategory = res.data;
          this.custom = custom;
          this.saveCustom = custom;
          this.albumPicsTypeOptions = this.custom.albumPicsTypeOptions || [];

          // 详情图片整理
          this.groupPics();

          // 交易群
          this.teams = this.custom.teams || {};

          // app 刷新要先清空
          this.area1List = [];
          this.area2List = [];
          this.area3List = [];
          this.area4List = [];
          this.area4Listzhms = [];

          this.custom2 = JSON.parse(res.data.custom2 || '{}');
          const { h5_conf = {} } = this.custom2;
          if (h5_conf && h5_conf.area_1) {
            h5_conf.area_1.forEach((ele) => {
              const { attrName, name } = ele;
              const findIt = this.productAttributeValueList.find(
                (item) => item.productAttributeName === attrName,
              )||{};

              if (attrName == '区服') {
                let value = '暂无';
                if (findIt.value) {
                  if (findIt.value.indexOf('|') != -1) {
                    value = findIt.value.split('|')[1];
                  } else {
                    value = findIt.value;
                  }
                }
                this.area1List.push({
                  label: name,
                  value,
                });
              } else if (attrName == 'productSn') {
                this.area1List.push({
                  label: name,
                  value: this.shopDetailJson.productSn,
                });
              } else if (attrName == '议价') {
                this.area1List.push({
                  label: name,
                  value:
                    this.shopDetailJson.gameGoodsYijia == 1
                      ? '可议价'
                      : '不议价',
                });
              } else if (findIt) {
                let value = '暂无';
                if (findIt.value) {
                  if (findIt.value.indexOf('|') != -1) {
                    value = findIt.value.split('|')[1];
                  } else {
                    value = findIt.value;
                  }
                }
                this.area1List.push({
                  label: name,
                  value,
                });
              }
            });
          }

          if (h5_conf && h5_conf.area_2) {
            h5_conf.area_2.forEach((ele) => {
              const { attrName, name } = ele;
              const findIt = this.productAttributeValueList.find(
                (item) => item.productAttributeName === attrName,
              )||{};

              if (attrName == '区服') {
                let value = '暂无';
                if (findIt.value) {
                  if (findIt.value.indexOf('|') != -1) {
                    value = findIt.value.split('|')[1];
                  } else {
                    value = findIt.value;
                  }
                }
                this.area2List.push({
                  label: name,
                  value,
                });
              } else if (attrName == 'productSn') {
                this.area2List.push({
                  label: name,
                  value: this.shopDetailJson.productSn,
                });
              } else if (findIt) {
                if (findIt.value != '') {
                  this.area2List.push({
                    label: name,
                    value: findIt.value,
                  });
                }
              }
            });
          }
          // this.gfyhData=this.area2List.map(ele=>ele.value)
          // if(!this.gfyhData.length){
          //   this.gfyhData =[this.shopDetailJson.productSn,this.shopDetailJson.price]
          // }
          this.gfyhData = this.area2List
          .filter(item => item.value)
          .slice(0, 3);
          if(!this.gfyhData.length){
            // this.gfyhData =[{value:this.shopDetailJson.productSn,label:'商品编号'},{value:this.shopDetailJson.price,label:'商品价格'}]
            this.gfyhData =[]
          }
          console.log('this.gfyhData',this.gfyhData,this.area2List)
          if (h5_conf && h5_conf.area_3) {
            h5_conf.area_3.forEach((ele) => {
              const { attrName, name } = ele;
              const findIt = this.productAttributeValueList.find(
                (item) => item.productAttributeName === attrName,
              )||{};

              if (attrName == '区服') {
                let value = '暂无';
                if (findIt.value) {
                  if (findIt.value.indexOf('|') != -1) {
                    value = findIt.value.split('|')[1];
                  } else {
                    value = findIt.value;
                  }
                }
                this.area3List.push({
                  label: name,
                  value,
                });
              } else if (attrName == 'productSn') {
                this.area3List.push({
                  label: name,
                  value: this.shopDetailJson.productSn,
                });
              } else if (findIt) {
                if (findIt.value != '') {
                  this.area3List.push({
                    label: name,
                    value: findIt.value,
                  });
                }
              }
              
            }); 
          } else {
            this.setArea3ForAll();
          }

          if (h5_conf && h5_conf.area_4) {
            h5_conf.area_4.forEach((ele) => {
              const { attrName, name } = ele;
              const findIt = this.productAttributeValueList.find(
                (item) => item.productAttributeName === attrName,
              )||{};

              if (attrName == '账号描述') {
                this.setDesc();
              } else if (attrName == '区服') {
                this.area4List.push({
                  label: name,
                  value: findIt.value.split('|')[1],
                });
              } else if (attrName == 'productSn') {
                this.area4List.push({
                  label: name,
                  value: this.shopDetailJson.productSn,
                });
              } else if (findIt) {
                if (findIt.value != '') {
                  this.area4List.push({
                    label: name,
                    value: findIt.value,
                  });
                }
              }
            });
          } else {
            this.setDesc();
          }
        }
      });
    },
    setArea3ForAll() {
      let arr=this.productAttributeList
      arr.sort((a, b) => {
        return b.sort - a.sort;
      });
      arr.forEach((ele) => {
        const findIt = this.productAttributeValueList.find((item) => {
          return (
            item.productAttributeId === ele.id &&
            (ele.type === 1 || ele.type === 2)
          );
        });
        if (findIt && findIt.value != '') {
          this.area3List.push({
            label: findIt.productAttributeName,
            value: findIt.value,
          });
        }
      });
    },
    setDesc() {
      this.area4Listzhms.push({
        label: '卖家说',
        value: this.description,
      });
      setTimeout(() => {
        const query = uni.createSelectorQuery().in(this);
        query
          .select('#desc2')
          .boundingClientRect((data) => {
            const { height = 0 } = data || {};
            if (height > 80) {
              this.toggleLineFlag2 = true;
              this.getToggleLine2 = 'text_linThree';
            } else {
              this.toggleLineFlag2 = false;
              this.getToggleLine2 = '';
            }
          })
          .exec();
      }, 500);
    },
    getSkinAndHero() {
      if (this.wzryId) {
        getSkinAndHero(this.wzryId).then((res) => {
          this.wzData = res;
          // this.wzData = list
          this.initwz();
          this.initwzHero();
        });
      }
    },
    // 获取商品数据
    getShopDtFun() {
      if (this.isPreview) {
        return getPreview(this.productId).then((res) => {
          if (res.code == 200) {
            const { product, productAttributeList, productAttributeValueList } =
              res.data;
            this.description = product.description;
            this.productAttributeValueList = productAttributeValueList;
            
            this.productAttributeList = productAttributeList;
            this.formatAttr(productAttributeList, productAttributeValueList);
            product.subTitle = this.tedianFilter(product.subTitle);

            this.shopDetailJson = product;

            this.arrDtPic = product.albumPics
              ? product.albumPics.split(',')
              : [];
            this.arrDtPicForShow = this.arrDtPic;
            this.getSkinAndHero();
            this.getProductCategory();
            this.toggleLineFlag = true;
          }
        });
      }
      return (
        this.productId
          ? getDetail(this.productId)
          : getDetailByCode({ productSn: this.productSn })
      ).then((res) => {
        if (res.code == 200) {
          const { product, productAttributeList, productAttributeValueList,shopInfo } =
            res.data;
          this.productId = product.id;

          this.description = product.description;
          this.productAttributeValueList = productAttributeValueList;
          this.productAttributeList = productAttributeList;
          this.formatAttr(productAttributeList, productAttributeValueList);
          this.shopDetailJson = product;
          this.arrDtPic = product.albumPics ? product.albumPics.split(',') : [];
          this.arrDtPicForShow = this.arrDtPic;
          this.getSkinAndHero();
          this.getProductCategory();

          if (isLogin()) {
            this.setFooterMark(product);
          }
            if(shopInfo){
              this.shopNumber=shopInfo.shopNumber
            }
          this.toggleLineFlag = true;
        }
      });
    },
    changeWzTab(value) {
      this.activeName = value;
    },
    initwzHero() {
      this.arrDtPicForShow3 = [
        {
          name: '稀有英雄',
          list: [],
        },
        {
          name: '普通英雄',
          list: [],
        },
      ];
      this.wzData.heroList.forEach((ele) => {
        const { name } = ele;
        let obj = {
          ...ele,
          heroIcon: `https://images2.kkzhw.com/mall/statics/wzry/wzhero/${ele.heroId}.jpeg`,
        };
        if (WZHERO.includes(name)) {
          this.arrDtPicForShow3[0].list.push(obj);
        } else {
          this.arrDtPicForShow3[1].list.push(obj);
        }
      });
    },
    changeActiveType(type) {
      this.activeType = type;
      this.initwz();
    },
    changeWztype(item) {
      this.wztype = item;
      this.initwz();
    },
    initwz() {
      this.arrDtPicForShow2 = [];
      let tempList = [];
      tempList = this.wzData.skinList;
      if (this.activeName === 'skin') {
        if (this.activeType === 'career') {
          if (this.wztype != '全部') {
            tempList = tempList.filter((ele) => ele.heroType == this.wztype);
          }
          let tempObj = {};
          tempList.forEach((ele) => {
            let hero = tempObj[ele.heroId];
            let skinImg = `https://images2.kkzhw.com/mall/statics/wzry/wzskin/${ele.skinId}.jpeg`;
            let { skinName } = ele;
            if (!hero) {
              tempObj[ele.heroId] = {
                ...ele,
                heroIcon: `https://images2.kkzhw.com/mall/statics/wzry/wzhero/${ele.heroId}.jpeg`,
                list: [{ skinImg, skinName }],
              };
            } else {
              hero.list.push({ skinImg, skinName });
            }
          });
          this.arrDtPicForShow2 = Object.keys(tempObj).map(
            (key) => tempObj[key],
          );
        } else {
          let tempClassTypeObj = {};
          tempList.forEach((ele) => {
            let { classTypeName } = ele;

            if (classTypeName.length > 1) {
              classTypeName = [classTypeName[0]];
            }
            if (classTypeName.length === 0) {
              classTypeName = ['其他'];
            }
            classTypeName.forEach((item) => {
              let skinImg = `https://images2.kkzhw.com/mall/statics/wzry/wzskin/${ele.skinId}.jpeg`;
              let { skinName } = ele;
              if (!tempClassTypeObj[item]) {
                tempClassTypeObj[item] = {
                  name: item,
                  list: [{ skinImg, skinName }],
                };
              } else {
                tempClassTypeObj[item].list.push({ skinImg, skinName });
              }
            });
          });
          this.arrDtPicForShow2 = Object.keys(tempClassTypeObj).map(
            (key) => tempClassTypeObj[key],
          );
          this.arrDtPicForShow2.sort((a, b) => {
            return a.list.length - b.list.length;
          });
        }
      }
    },
    groupAlbumPics(items) {
      items = items.map((ele) => {
        // 如果是 type 数据先转 name
        if (!ele.hasOwnProperty('name')) {
          const { type } = ele;
          // 如果是 1-5 取固定值，否则用type
          ele.name = OPTSOUT[type] || type || '';
        }
        return ele;
      });
      // 使用一个对象来根据type分类
      let groupedByType = {};
      // 遍历数组，将元素按type分类
      items.forEach((item) => {
        let type = item.name;
        if (type) {
          if (!groupedByType[type]) {
            // 如果这个type的数组还不存在，则初始化它
            groupedByType[type] = [];
          }
          // 将元素添加到对应type的数组中
          groupedByType[type].push(item.url);
        }
      });

      // 如果你想将groupedByType对象转换为数组形式（可选）
      let groupedArray = [];
      Object.keys(groupedByType).forEach((key) => {
        if (key != 0) {
          groupedArray.push({
            type: key,
            items: groupedByType[key],
          });
        }
      });
      let OPTSOUTSORT = {};
      this.albumPicsTypeOptions.forEach((ele, index) => {
        OPTSOUTSORT[ele.name] = index + 1;
      });
      groupedArray.sort((a, b) => {
        let a0 = OPTSOUTSORT[a.type];
        let b0 = OPTSOUTSORT[b.type];
        return a0 - b0;
      });

      const findAllPic = this.albumPicsTypeOptions.find(
        (ele) => ele.name == '全部图片',
      );
      if (findAllPic) {
        groupedArray.unshift({
          type: '全部图片',
          items: this.arrDtPic,
        });
      }

      if (!items || items.length == 0) {
        groupedArray = [];
      }
      return groupedArray;
    },
    setFooterMark(product) {
      if (!isLogin()) {
        return;
      }
      if (product.price < 5000000) {
        readHistoryCreate({
          productId: product.id,
          categoryId: product.productCategoryId,
          productPrice: product.price,
        });
      }
    },
    // 图片放大显示
    previewImg(index) {
      uni.previewImage({
        current: index,
        urls: this.arrDtPicForShow,
        indicator: 'number',
      });
    },
    // 用户收藏
  },
};
</script>
<style lang="scss" scoped>
.g-bg {
  background: linear-gradient(
    180deg,
    #fdf5ed,
    #fff 100%,
    #ffe1c3 200%
  ) !important;
}

.fix-head {
  position: sticky;
  top: 80rpx;
  z-index: 9;
  background: #fff;
  padding: 44rpx 48rpx 28rpx 48rpx;
  border-radius: 20px;
}

.top-card-box {
  .account_card {
    margin-top: 10rpx;
    padding: 32rpx 28rpx;
    border: 1px solid #ffddbe;
    border-radius: 20rpx;
    background: #fffcf9;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      width: 110rpx;
      height: 114rpx;
      right: 30rpx;
      bottom: 28rpx;
      background: url('../../assets/imgs/logo_bg.png') no-repeat center;
      background-size: cover;
    }

    .sn {
      color: $uni-color-title;
      font-family: 'PingFang SC';
      font-size: 28rpx;
      font-weight: 500;
    }
    .price {
      color: $uni-color-primary;
      font-family: Inter;
      font-size: 40rpx;
      font-weight: 700;
    }

    .area1 {
      font-size: 24rpx;
      display: flex;
      flex-wrap: wrap;
      line-height: 48rpx;
      position: relative;

      .area1item {
        width: 33%;
        display: flex;
        height: 50rpx;
        margin-top: 26rpx;
        font-family: 'PingFang SC';

        .label {
          color: rgba(0, 0, 0, 0.4);
          font-size: 22rpx;
        }
        .value {
          text-align: center;
          color: #505050;
          font-size: 26rpx;
          font-weight: 500;
          margin-left: 6rpx;
        }
      }
    }
  }

  .acc-info {
    margin-top: 24rpx;
    margin-bottom: 24rpx;
    color: $uni-color-subtitle;
    font-size: 26rpx;
    font-weight: 500;
  }

  .midW_add {
    padding: 8rpx 66rpx;
    border-radius: 16rpx;
    background: #fbf9f7;

    view {
      font-family: YouSheBiaoTiHei;
      font-size: 20rpx;
      font-style: normal;
      font-weight: 400;
      letter-spacing: 0.2px;
      background: var(--linear-primary);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .midW_add_pic {
      width: 34rpx;
      margin-right: 10rpx;
    }
  }
}
.he_box {
  background-color: #fff;
  padding: 20rpx 50rpx;
  border-radius: 20rpx;
  .qian {
    display: flex;
    color: #ffbb05;
    padding: 20rpx 0;
  }
  .he {
    display: flex;
    color: #ff134b;
    padding: 20rpx 0;
  }
  .jue {
    display: flex;
    color: #08a2ff;
    padding: 20rpx 0;
  }
  .img {
    margin-right: 20rpx;
    width: 44rpx;
    height: 44rpx;
  }
}

.account_attr2 {
  color: $uni-color-paragraph;
  font-size: 22rpx;
  font-weight: 400;

  .attr {
    padding: 8rpx 0;
  }
  .updatetime {
  }
}
.descriptionBox {
  padding: 10rpx;
  white-space: pre-wrap;
  word-break: break-all;
}
.wzChangeBox {
  background: #fff;
  position: sticky;
  /* #ifdef H5 */
  top: 44px;
  /* #endif */
  /* #ifdef APP-PLUS */
  top: 0;
  /* #endif */
  z-index: 99;
}
.wztab_box {
  display: flex;
  justify-content: start;
  border-bottom: 2rpx solid #f4f4f4;
  margin-bottom: 30rpx;
  .wztab {
    position: relative;
    margin-right: 60rpx;
    font-size: 28rpx;
    color: #666;
  }
  .wztab::after {
    background-color: #e60f0f;
    bottom: 0;
    content: '';
    height: 2rpx;
    left: 50%;
    position: absolute;
    transform: translate(-50%);
    width: 26rpx;
    display: none;
  }
  .wztab.active::after {
    display: block;
  }
}
.hero_box {
  font-size: 28rpx;
  .name {
    width: 300rpx;
    border-radius: 20rpx;
    background: #f4f4f4;
    text-align: center;
    margin: 20rpx 0 20rpx 0;
    font-size: 24rpx;
  }
  .heros {
    flex-wrap: wrap;
    .hero_warp {
      position: relative;
      .hero_name {
        position: absolute;
        bottom: 6rpx;
        text-align: center;
        color: #fff;
        width: 100%;
        line-height: 40rpx;
        font-size: 24rpx;
      }
    }
    .hero {
      width: 100rpx;
      height: 100rpx;
      margin: 10rpx 16rpx 0 0; /* 默认无右margin */
    }
    .hero:nth-child(5n) {
      margin-right: 0; /* 每第五项或5的倍数取消右margin */
    }
  }
}

.acDt_body {
  border-radius: 24rpx;
  overflow: hidden;
  .picmain {
    height: auto;
    width: 100%;
    margin-bottom: 16rpx;
    border-radius: 16rpx;
    overflow: hidden;
  }
  image {
    width: 100%;
  }
  .rich-box {
    display: flex;
    align-items: center;
  }
  /deep/.uni-table {
    background-color: transparent;
  }
  /deep/ .uni-table-td {
    font-size: 28rpx;
    padding: 6rpx 16rpx;
    vertical-align: top;
    border-color: rgba(0, 0, 0, 0.2);
    color: #222;
    line-height: 32rpx; /* 160% */

    &.title {
      font-size: 24rpx;
      font-weight: 500;
      width: 240rpx;
    }
    &.title_text{
      color: #9a9a9a!important;
    }
  }
  /deep/ .tag_tedian_pic {
    width: 28rpx;
    height: 28rpx;
    margin-left: 6rpx;
    position: relative;
    top: 4rpx;
  }

  .wztype {
    margin-top: 20rpx;
    font-size: 24rpx;
    font-weight: 400;
    .item {
      margin-right: 30rpx;
      cursor: pointer;
    }
    .active {
      font-weight: 700;
    }
  }

  .up2 {
    text-align: right;
    color: rgba(0, 0, 0, 0.6);
  }

  .skinname {
    position: absolute;
    bottom: 0;
    color: #fff;
    font-size: 20rpx;
    text-align: center;
    width: 100%;
  }

  .skin_box {
    align-items: flex-start;
    font-size: 28rpx;
    text-align: center;
    margin-top: 20rpx;
    .skin_right {
      flex: 1;
      display: flex;
      justify-content: flex-start;
      overflow-y: auto;
      .skinimg {
        position: relative;
      }
    }
    .hero {
      font-size: 24rpx;
      .imgbox {
        width: 96rpx;
        height: 96rpx;
        .img {
          width: 96rpx;
          height: 96rpx;
        }
      }
      .name {
        font-size: 20rpx;
        line-height: 30rpx;
        color: #333;
      }
      .namenum {
        font-size: 20rpx;
        line-height: 30rpx;
        color: #333;
      }
    }
    .skin {
      margin-left: 12rpx;
      .skinimg {
        margin-bottom: 10rpx;
        width: 116rpx;
        height: 180rpx;
      }
    }
  }

  .skin_box2 {
    font-size: 24rpx;
    .skins {
      flex-wrap: wrap;
    }
    .name {
      width: 200rpx;
      border-radius: 20rpx;
      background: #f4f4f4;
      text-align: center;
      margin: 20rpx 0 0 0;
      font-size: 24rpx;
    }
    .skin {
      width: 128rpx;
      height: 198rpx;
      margin: 20rpx 0 0 12rpx;
      position: relative;
    }
  }

  .type_btn {
    background: #f4f4f4;
    border-radius: 30rpx;
    color: #666;
    cursor: pointer;
    display: block;
    font-weight: 400;
    width: 110rpx;
    text-align: center;
    font-size: 24rpx;
    line-height: 26px;
    margin-right: 20px;

    &.active,
    &:hover {
      background: #fee;
      color: #ff6917;
      font-weight: 500;
    }
  }

  .qqlist_item {
    padding: 30rpx;
    background: #fffcf9;
    border: solid 1rpx #ffddbe;
    border-radius: 24rpx;
    box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
    position: relative;

    .title {
      font-family: YouSheBiaoTiHei;
      font-size: 24rpx;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
    .qqcode {
      font-size: 28rpx;
      font-weight: 500;
      color: #ff7a00;
    }
  }

  .tedian_desc_wrap {
    width: 400rpx;
    background-color: #fff;
    border-radius: 8rpx;
    box-sizing: border-box;
    padding: 30rpx;
  }
  .tedian_desction_pic {
    width: 44rpx;
    height: 44rpx;
    margin-right: 20rpx;
  }
  .tedian_desction_text {
    font-size: 28rpx;
    font-weight: 500;
  }
  .tedian_desction_text.one {
    color: #ffbb05;
  }
  .tedian_desction_text.two {
    color: #ff134b;
  }
  .tedian_desction_text.three {
    color: #08a2ff;
  }
}

.accountDt_footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
  box-sizing: border-box;

  .accountDt_footer_box {
    background-color: rgba(255, 255, 255, 0.8);
    padding: 44rpx 20rpx;
    box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.1);
    stroke-width: 1px;
    stroke: #fff6eb;
    box-shadow: 1px 2px 3px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(20px);
    -webkit-box-shadow: (1px 2px 3px rgba(0, 0, 0, 0.05));
  }

  .accountDt_footerItem {
    color: rgba(0, 0, 0, 0.4);
    text-align: center;
    font-family: YouSheBiaoTiHei;
    font-size: 24rpx;
    line-height: 16px; /* 133.333% */
  }
}
.goodsNumBox {
  margin: 40rpx 0px 60rpx 0px;
  .title_box {
    font-size: 28rpx;
    color: #1b1b1b;
    font-family: PingFang Sc;
    font-weight: 500;
    .title_text {
      font-size: 24rpx;
      color: #9a9a9a;
      text {
        color: #ff720c;
      }
    }
  }
}
.goodsNumInput {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 99;
}
.goodsNumInput /deep/ .uni-numbox__value {
  background: transparent !important;
  border: none !important;
}
.goodsNumInput /deep/ .uni-numbox-btns {
  width: 40rpx;
  height: 40rpx;
  padding: 0px;
  border-radius: 50%;
  display: block;
  text-align: center;
  background: #ff720c !important;
  .uni-numbox--text {
    color: #fff !important;
    line-height: 32rpx;
    margin-bottom: 0px;
  }
}
.goodsNumInputDisbaleLeft /deep/ .uni-numbox__minus {
  background: #ff720c82 !important;
}
.goodsNumInputDisbaleRight /deep/ .uni-numbox__plus {
  background: #ff720c82 !important;
}
</style>
