<template>
  <view class="scroll-nav">
    <scroll-view  :scroll-left="scrollLeft" class="content-scroll" scroll-x style="white-space: nowrap;">
      <text
        v-for="(item, index) in navList"
        :key="item[labelName]"
        :class="activeIndex === index ? 'selected' : 'gradient-main-primary'"
        class="scroll-item"
        @click="doClick(item, index)"
      >
        {{ item[labelName] }}
    </text>
    </scroll-view>
  </view>
</template>
<script>
export default {
  name: 'GradientBorder',
  props: {
    navList: {
      type: Array,
      default: () => [],
    },
    labelName: {
      type: String,
      default: 'label',
    },
  },
  data() {
    return {
      activeIndex: 0,

      scrollLeft:0,
      contentScrollW:0,
      category:[]
    };
  },
  watch: {},
  mounted() {
    this.getScrollW()
  },
  methods: {
    getScrollW(){
     const query = uni.createSelectorQuery().in(this)
     query.select('.content-scroll').boundingClientRect(data=>{
      this.contentScrollW = data.width
     }).exec()

     query.selectAll('.scroll-item').boundingClientRect(data=>{
      this.category =  data.map((ele) => {
        return{
          left:ele.left,
          width:ele.width
        }
      });
      console.log(this.category)
     }).exec()

    },
    doClick(item, index) {
      if(this.category[index]){
        const{left,width} = this.category[index]
        this.scrollLeft = left-this.contentScrollW/2+width/2
      }

      this.activeIndex = index;
      this.$emit('tabClick', item);
    },
  },
};
</script>

<style lang="scss" scoped>
.scroll-nav {
  background:#fff;
  overflow-x: scroll;
}
.nav-box{
 display: flex;
  justify-content: flex-start;
  align-items: center;
}
.scroll-item {
  color: $uni-color-primary;
  font-size: 24rpx;
  font-weight: 500;
  padding: 16rpx 16px;
  white-space: nowrap;
  display: inline-block;

  &.selected {
    border-radius: 48rpx;
    background: var(
      --Main-color,
      radial-gradient(
        238.39% 44.19% at 96.59% 31.25%,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0) 100%
      ),
      radial-gradient(
        182.56% 55.34% at 5.68% 100%,
        rgba(246, 251, 34, 0.31) 0%,
        rgba(255, 158, 69, 0) 100%
      ),
      radial-gradient(
        137.51% 118.3% at 32.95% 0%,
        rgba(255, 137, 137, 0.83) 21.25%,
        rgba(255, 169, 106, 0.51) 88.62%
      ),
      radial-gradient(
        178.09% 220.16% at 94.89% -132.81%,
        #ff7a00 67.59%,
        rgba(255, 199, 0, 0.38) 100%
      ),
      #fff500
    );
    box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
    color: #fff;
  }
}
</style>
