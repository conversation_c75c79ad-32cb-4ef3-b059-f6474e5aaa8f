<template>
  <!-- 收藏功能 -->
  <view class="accountDt_footerItem" @click="collectFun">
    <view style="margin-bottom: 10rpx;">
      <IconFont
        :size="20"
        :class="isCollect == 1 ? 'c-primary' : ''"
        icon="collect"
      />
    </view>
    <view>{{ isCollect ? '取消收藏' : '添加收藏' }}</view>
  </view>
</template>
<script>
import isLogin from '@/common/mixins/isLogin';
import {
  productCollectionAdd,
  productCollectionDetele,
} from '@/config/api/accountDetail.js';

export default {
  props: {
    shopDetailJson: {
      type: Object,
      default: () => {},
    },
    productId: {
      type: [String, Number],
      default: '',
    },
  },
  data() {
    return {
      collects: uni.getStorageSync('collects'),
    };
  },
  computed: {
    isCollect() {
      if (this.collects.includes(this.productId)) {
        return true;
      }
      return false;
    },
  },
  mounted() {
    if (isLogin) {
      let collects = uni.getStorageSync('collects');
      this.collects = collects ? JSON.parse(collects) : [];
    }
  },
  methods: {
    // 用户收藏
    collectFun() {
      if (!isLogin()) {
        uni.navigateTo({
          url: '/pages/login/login?isBack=1',
        });
        return;
      }
      if (!this.shopDetailJson.productSn) {
        return;
      }
      uni.showLoading({
        title: '拼命加载中',
      });

      if (this.isCollect == 1) {
        // 取消收藏
        productCollectionDetele({
          productId: this.productId,
        }).then((res) => {
          if (res.code == 200) {
            this.collects = this.collects.filter((ele) => {
              return ele != this.productId;
            });
            uni.setStorageSync('collects', JSON.stringify(this.collects));
            uni.showToast({
              title: '取消成功！',
              icon: 'none',
            });
            // this.getShopDtFun();
            this.$emit('refresh');
          }
          uni.hideLoading();
        });
      } else {
        // 收藏
        productCollectionAdd({
          productId: this.productId,
          productCategoryId: this.shopDetailJson.productCategoryId,
          productCategoryName: this.shopDetailJson.productCategoryName,
          productName: this.shopDetailJson.name,
          productPic: this.shopDetailJson.pic,
          productPrice: this.shopDetailJson.price,
          productSn: this.shopDetailJson.productSn,
          productSubTitle: this.shopDetailJson.subTitle,
        }).then((res) => {
          if (res.code == 200) {
            this.collects.push(this.productId);
            uni.setStorageSync('collects', JSON.stringify(this.collects));
            uni.showToast({
              title: '收藏成功！',
              icon: 'none',
            });
            setTimeout(() => {
              // this.getShopDtFun();
              this.$emit('refresh');
            }, 400);
          }
          uni.hideLoading();
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.accountDt_footerItem {
  color: rgba(0, 0, 0, 0.4);
  text-align: center;
  font-family: YouSheBiaoTiHei;
  font-size: 24rpx;
  line-height: 16px; /* 133.333% */

  margin-right: 20rpx;
}

.accountDt_footerPic {
  width: 44rpx;
  height: 44rpx;
  margin: 0 auto;
}
</style>
