<template>
  <view v-if="gfyhData.length"  class="yhbj-box">
    <!-- <view class="yhbj-title"> -->
      <!-- <text>官方验号报告</text> -->
      <!-- <text class="yhbj-sub-title">信息准确更安全</text> -->
    <!-- </view> -->

    <view :class="['yhbj-list', gfyhData.length === 1 ? 'spaceCenter' : 'spaceAround']">
      <view class="yhbj-line-box">
        <view class="line">{{gfyhData[0]?gfyhData[0].value:''}}</view>
        <view class="line-label">{{gfyhData[0]?gfyhData[0].label:''}}</view>
      </view>

      <view v-if="gfyhData[1]" class="yhbj-line-box">
        <view class="line">{{gfyhData[1]?gfyhData[1].value:''}}</view>
        <view class="line-label">{{gfyhData[1]?gfyhData[1].label:''}}</view>
      </view>
      <view v-if="gfyhData[2]" class="yhbj-line-box">
        <view class="line">{{gfyhData[2]?gfyhData[2].value:''}}</view>
        <view class="line-label">{{gfyhData[2]?gfyhData[2].label:''}}</view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  props:{
    gfyhData:{
      type:Array,
      default:()=>[]
    }
  },
  data() {
    return{
    }
  },
}
</script>
<style lang="scss" scoped>
.yhbj-box {
  margin-top: 24rpx;
  padding:54rpx 24rpx 26rpx 24rpx;
  border-radius: 24rpx;
  background: linear-gradient(94deg, #ffddbe 5%, #ffc085 97.55%);
  -webkit-border-radius: 24rpx;
  -moz-border-radius: 24rpx;
  -ms-border-radius: 24rpx;
  -o-border-radius: 24rpx;

  background: url(../../../assets/imgs/acc_detail_yh_bg.png) center no-repeat;
  background-size: cover;
}
.yhbj-title {
  font-family: YouSheBiaoTiHei;
  font-size: 32rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 125% */
  background: linear-gradient(180deg, #ffb74a 0%, #ff7a00 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.yhbj-sub-title {
  font-family: "PingFang SC";
  font-size: 20rpx;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-left: 12rpx;
}
.yhbj-list{
  border-radius: 20rpx;
  border: 1px solid var(--BG-Light-Orange, #FFF);
  background: linear-gradient(180deg, #FFF 9.09%, #FFE1C3 234.55%);
  -webkit-border-radius: 20rpx;
  -moz-border-radius: 20rpx;
  -ms-border-radius: 20rpx;
  -o-border-radius: 20rpx;

  // padding: 28rpx 56rpx;
  padding: 14rpx 0rpx;
  margin-top: 20rpx;
  color: #1B1B1B;
  font-size: 20rpx;
  .yhbj-line-box{
    display: inline-block;
    // width: 33.33%;
    text-align: center;
    .line-label{
      color:#999999
    }
  }
  // text{
  //   display: inline-block;
  //   width: 33.33%;
  //   text-align: center;
  // }

}
.yhbj-list .line{
  position: relative;

}
// .yhbj-list .line::before{
//   content: '';
//   position: absolute;
//   right: 0;
//   top: 50%;
//   height: 16rpx;
//   width: 2rpx;
//   background: linear-gradient(180deg, #ffb74a 0%, #ff7a00 100%);
//   transform: translateY(-50%);
// }
</style>