<template>
  <view :style="styleStr" class="water"></view>
</template>
<script>
export default {
  data() {
      return{
        styleStr:''
      }
  },
  mounted(){
    this.styleStr = 'background:'+this.createImg('kkzhw.com')
    console.log(this.styleStr)
  },
  methods:{
    createImg(str){
      const can = document.createElement('canvas')
      can.width = 400 // 设置每一块的宽度
      can.height = 300 // 高度
      can.size= 20
      const cans = can.getContext('2d') // 获取canvas画布
      cans.rotate(-20 * Math.PI / 180) // 逆时针旋转π/9  cans.font = '20px Vedana' // 设置字体
      cans.fillStyle = 'rgba(0, 0, 0,0.08)' // 设置字体的颜色
      cans.textAlign = 'left' // 文本对齐方式

      cans.font = '20px Vedana' //水印字体样式
      cans.textBaseline = 'Middle' // 文本基线
      cans.fillText('看看账号网', 0, can.height / 3) // 绘制文字

      cans.font = '26px Vedana' //水印字体样式
      cans.fillText('kkzhw.com',160, can.height / 2) // 绘制文字


      return 'url(' + can.toDataURL('image/png') + ') left top repeat'
    }
  },
}
</script>
<style scoped>
.water{
  position: absolute;
  left: 0;
  top: 0rpx;
  bottom: 0rpx;
  right: 0;
  z-index:1;
  background: red;
  pointer-events: none;
}
</style>