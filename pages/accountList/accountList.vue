<template>
  <view class="pageCommon" style="background: #fdf5ed; min-height: 100vh">
    <PageNav :right-width="0" background-color="#fff" theme="black" shadow>
      <!-- 搜索栏 -->
      <MySearchBar
        v-model="keyword"
        type="gray"
        size="small"
        @search="doSearch"
      >
        <template slot="right">
          <view
            class="spaceStart"
            style="font-size: 24rpx; margin-left: 16rpx;color:#999;line-height: 28rpx;"
            @click="chooseListStyle"
          >
            <IconFont
              :size="12"
              icon="image-text"
              style="margin-right: 4rpx; display: inline-block; height: 26rpx;"
            />
            <text>切换{{ isShowBigItem ? '小图' : '大图' }}</text>
          </view>
        </template>
      </MySearchBar>
    </PageNav>

    <!-- 筛选栏 -->
      <FilterNav
        id="selectbox"
        :pop-up-style="qufuSearchStyle"
        :filter-setting="filterSetting"
        @search="handelFilterSearch"
      >
        <template v-slot:otherPanel="slotProps">
            <OtherFilterPanel
              :cate-list="cateList"
              :check-box-attr-group="checkBoxAttrGroup"
              :query-int-params="queryIntParams"
              :top-h="topH"
              :productCategoryId="productCategoryId"
              @close="slotProps.hidePopup('other')"
              @search="handelOtherSearch"
              @change="handelChange"
              @reset="handelOtherReset"
              @wzSearchType="wzSearchType"
              :wzSearchTypeNum="wzSearchTypeNum"
            />
        
        </template>
      </FilterNav>
    <view v-if="pmsSearchTags.length" class="spaceStart searchTagBox">
      <view class="tags spaceCenter" :class="item.isActive?'active':''" @click="handleTagsClick(item)" v-for="(item,index) in pmsSearchTags" :key="index">
        {{item.tagName}}
      </view>
    </view>
    <!-- active -->
    <!-- style="padding: 24rpx 0" -->
    <!--账号列表数据-->
    <accountList
      :is-loading="isLoading"
      :banner-list="bannerList"
      :total="total"
      :account-shop-date="accountShopList"
      :is-show-big="isShowBigItem"
      :goods-type="goodsType"
      :style="{padding:pmsSearchTags.length?'94rpx 0px':'24rpx 0px'}"
      @goGameDetail="accountDetail"
    />
  </view>
</template>

<script>
import _ from 'lodash';

import accountList from '@/components/accountList/index.vue';
import FilterNav from '@/components/toolbar/FilterNav.vue';
import OtherFilterPanel from './components/OtherFilterPanel.vue';
import uniNavBar from '@/uni_modules/uni-nav-bar/components/uni-nav-bar/uni-nav-bar.vue';

import {
  searchProductList2,
  getProductAttribute,
  getProductAttribute3
} from '@/config/api/search.js';
import { getCategoryAdverList2 } from '@/config/api/kf.js';

import { getProductCategory } from '@/config/api/submitAccount.js';
import { DEFAULT_SORT_DATA } from '@/utils/const';
import { mapState } from 'vuex';

// #ifdef H5
const innerHeight = window.innerHeight;
// #endif

export default {
  components: {
    uniNavBar,
    accountList,
    FilterNav,
    OtherFilterPanel,
  },

  data() {
    return {
      goodsType:'',
      
      filterSetting: [
        // 筛选配置
        {
          name: '区服',
          dataList: [],
          parentOptions: [],
          key: 'gameAccountQufu',
          selectValue: '',
          defaultValue:'',
          scrollH: 500,
        },
        {
          name: '账号专区',
          dataList: [],
          key: 'zhzq',
          defaultValue:'',
          selectValue: '',
        },
        {
          name: '排序',
          dataList: DEFAULT_SORT_DATA,
          key: 'sort',
          defaultValue: DEFAULT_SORT_DATA[0].value,
          selectValue: DEFAULT_SORT_DATA[0].value,
          scrollH: 500,
        },
        {
          name: '筛选',
          key: 'other',
          selectValue: '',
        },
      ],
      productCategoryId: '',

      isLoading: true,

      catePic: '',

      keyword: '',

      searchParam: {},

      cateList: [], // 属性列表
      checkBoxAttrGroup: [], // 属性分组
      queryIntParams: [], //通用价格

      isShowBigItem: true, // 是否展示横版

      qufuSearchStyle: '',
      topH: '',

      status: 'more',
      totalPage: 1, //
      total: 0,
      accountShopList: [], // 商品数据

      bannerList: [],
      productCategory: {},

      pmsSearchTags:[],
      checkedTags:[],
      attributeData:[],
      harborConfigList:[],
      hookHeroZhanli:'',
      wzSearchTypeNum:0,
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo,
    }),
  },
  watch:{
    cateList:{
      immediate: true,
      deep:true,
      handler(v) {
        const hasValue =  this.cateList.filter(ele=>ele.selectValue && ele.selectValue?.length).length+this.queryIntParams?.length
        this.setFilterSettingValue(!!hasValue,'other')
      },
    },
    queryIntParams:{
      immediate: true,
      deep:true,
      handler(v) {
        const hasValue =this.cateList.filter(ele=>ele.selectValue && ele.selectValue?.length).length +  v?.length
        this.setFilterSettingValue(!!hasValue,'other')
      },
    }
  },
  mounted() {},
  onShow() {
    let value = uni.getStorageSync('ACCOUNT_IS_BIG_SHOW');
    if (value !== false) {
      value = true;
    }
    this.isShowBigItem = value;
  },
  onReady() {
    const view = uni.createSelectorQuery().in(this).select('#selectbox');
    view
      .boundingClientRect((data) => {
        const { height, top } = data;
        this.topH = height + top;
        this.qufuSearchStyle = 'top:' + this.topH + 'px';
      })
      .exec();
  },
  onLoad(e) {
    if (e.productCategoryId) {
      this.productCategoryId = e.productCategoryId;
      this.getCateDetail(this.productCategoryId);
    }

    this.initSearchParams();
    // 搜索过来的
    if (e.keyword) {
      this.keyword = e.keyword;
    }
    // this.getShopListFun();
    this.loadCateList().then(() => {
      if (e.initParam) {
        if (e.initParam == 2) {
          let findIt = this.cateList.find((ele) => ele.name == '价格');
          findIt.selectValue = ['150000', '750000'];
        }
      } else if (e.zq) {
        if (e.zq == 0) {
          let findIt = this.cateList.find((ele) => ele.name == '内功');
          findIt.selectValue = ['灵韵'];
        } else if (e.zq == 1) {
          let findIt = this.cateList.find((ele) => ele.ename == 'tagsKKList');
          findIt.selectValue = ['外观号'];
        } else if (e.zq == 2) {
          let findIt = this.cateList.find((ele) => ele.name == '价格');
          findIt.selectValue = ['1', '300'];
        }
      }
      this.searchByCate(null, 'first');
    });
  },
  onReachBottom() {
    if (this.searchParam.pageNum < this.totalPage) {
      this.status = 'loading';
      this.searchParam.pageNum++;
      this.formatParamsAndSearch('add');
    } else {
      this.status = 'loading';
      setTimeout(() => {
        this.status = 'noMore';
        // uni.showToast({
        //   title: '已全部加载完毕',
        //   icon: 'none',
        // });
      }, 1000);
    }
  },
  methods: {
    // handleTagsClick(val){
    //   this.queryIntParams=[]
    //   this.checkedTags=[]
    //   let index=this.pmsSearchTags.findIndex((item)=>val.tagName==item.tagName)
    //   //重置所有isActive 后续可能要去除
    //   const wasActive = this.pmsSearchTags[index].isActive;
    //   this.pmsSearchTags.forEach(item => {
    //     this.$set(item, "isActive", false);
    //   });
    //   this.$set(this.pmsSearchTags[index], "isActive", !wasActive);
    //   const allInactive = this.pmsSearchTags.every(item => !item.isActive);
    //   //如果全部为false 则认为没有勾选项目 重置搜索
    //   if(allInactive){
    //     this.keyword=''
    //     this.harborConfigList=[]
    //     this.queryIntParams=[]
    //     this.$set(this.filterSetting[0], 'selectValue', "在售专区");
    //     this.handelOtherReset()
    //     return
    //   }
    //   this.searchConfigList=[]
    //   let pmsSearchTags=JSON.parse(JSON.stringify(this.pmsSearchTags))
    //   let searchConditions=JSON.parse(pmsSearchTags[index].searchConditions)
    //   if(this.searchConfigList.length==0){
    //     this.searchConfigList=searchConditions.attrValueList
    //     this.keyword=searchConditions.keyword||''
    //   }
    //   //隐藏属性
    //   this.harborConfigList=[]
    //   let attributeData = JSON.parse(JSON.stringify(this.attributeData));
    //   const hasPriceInOriginal = attributeData.some(item => item.name === '价格');
    //   const priceItemInSearch = this.searchConfigList.find(j => j.name === '价格');
    //   //要塞入一个价格 方便赋值
    //   if (!hasPriceInOriginal&&priceItemInSearch) {
    //     this.queryIntParams=[
    //       {
    //         key:'price',
    //         min :priceItemInSearch.selectValue[0]||'',
    //         max : priceItemInSearch.selectValue[1]||'',
    //       }
    //     ]
    //   }
    //   //匹配到的就赋值 未匹配到就置空
    //   attributeData.forEach(item => {
    //     const matchedConfig = this.searchConfigList.find(j => j.name === item.name);
    //     //获取隐藏属性
    //     const allUnmatchedItems = this.searchConfigList.filter(config => {
    //       return !attributeData.some(item => item.name === config.name);
    //     });
    //     if (matchedConfig) {
          
    //       item.selectValue = matchedConfig.selectValue;
    //       item.valueSearchType = matchedConfig.valueSearchType;
    //     } else {
    //       item.selectValue = [];
    //     }
    //     if(allUnmatchedItems.length){
    //       this.harborConfigList=allUnmatchedItems
    //     }
    //   });
    //   this.getSearchConfig(attributeData)
    //   this.formatParamsAndSearch()
    // },
    handleTagsClick(item) {
      // 清空查询条件
      this.queryIntParams = [];
      this.checkedTags = [];

      // 获取当前点击标签的索引
      let index = this.pmsSearchTags.findIndex((tag) => item.tagName === tag.tagName);

      const isUniqueTag = this.pmsSearchTags[index].isUnique === 1;

      if (isUniqueTag) {
        // isUnique=1 的标签互斥，但不影响 isUnique=0 的标签
        this.pmsSearchTags.forEach((tag, idx) => {
          if (tag.isUnique === 1 && tag.isActive && idx !== index) {
            this.$set(this.pmsSearchTags, idx, { ...tag, isActive: false });
          }
        });
      }

      // 切换当前标签的 isActive 状态
      const wasActive = this.pmsSearchTags[index].isActive;
      this.$set(this.pmsSearchTags[index], "isActive", !wasActive);

      // 检查是否所有标签都未选中
      const allInactive = this.pmsSearchTags.every(tag => !tag.isActive);
      if (allInactive) {
        this.keyword = '';
        this.harborConfigList = [];
        this.queryIntParams = [];
        this.$set(this.filterSetting[0], 'selectValue', "在售专区");
        this.handelOtherReset();
        return;
      }

      // 合并所有已选中的标签的 searchConditions
      this.searchConfigList = [];
      let selectedTags = this.pmsSearchTags.filter(tag => tag.isActive);

      let mergedAttrValueList = [];
      let mergedKeyword = '';
      selectedTags.forEach(tag => {
        const searchConditions = JSON.parse(tag.searchConditions);
        if (searchConditions.attrValueList && searchConditions.attrValueList.length) {
          mergedAttrValueList = mergedAttrValueList.concat(searchConditions.attrValueList);
        }
        if (searchConditions.keyword) {
          mergedKeyword = searchConditions.keyword; // 如果多个keyword，可以自己决定要不要拼接
        }
      });
     
      // this.searchConfigList = mergedAttrValueList;
      // 合并相同name的selectValue
      const mergedMap = new Map();
      mergedAttrValueList.forEach(item => {
        if (mergedMap.has(item.name)) {
          const existing = mergedMap.get(item.name);
          existing.selectValue = [...new Set([...existing.selectValue, ...item.selectValue])];
        } else {
          mergedMap.set(item.name, {...item});
        }
      });
      this.searchConfigList = Array.from(mergedMap.values());
      this.keyword = mergedKeyword;

      // 更新 checkedTags
      this.checkedTags = selectedTags.map(tag => tag.tagName);

      // 隐藏属性逻辑
      this.harborConfigList = [];
      let attributeData = JSON.parse(JSON.stringify(this.attributeData));
      const hasPriceInOriginal = attributeData.some(item => item.name === '价格');
      const priceItemInSearch = this.searchConfigList.find(item => item.name === '价格');

      if (!hasPriceInOriginal && priceItemInSearch) {
        this.queryIntParams = [
          {
            key: 'price',
            min: priceItemInSearch.selectValue[0] || '',
            max: priceItemInSearch.selectValue[1] || '',
          },
        ];
      }
      console.log(this.searchConfigList,111111)
      // 匹配属性并更新配置
      attributeData.forEach(item => {
        const matchedConfig = this.searchConfigList.find(config => config.name === item.name);
        const allUnmatchedItems = this.searchConfigList.filter(config => !attributeData.some(attr => attr.name === config.name));

        if (matchedConfig) {
          item.selectValue = matchedConfig.selectValue;
          item.valueSearchType = matchedConfig.valueSearchType;
        } else {
          item.selectValue = [];
        }

        if (allUnmatchedItems.length) {
          this.harborConfigList = allUnmatchedItems;
        }
      });
      console.log(attributeData,22222)
      // 更新筛选配置
      this.getSearchConfig(attributeData);
      this.formatParamsAndSearch();
    },
    setFilterSettingValue(v, key) {
      const index = this.filterSetting.findIndex((e) => e.key === key);
      this.$set(this.filterSetting[index], 'selectValue', v || this.filterSetting[index].defaultValue );
    },
    handelFilterSearch(v, key) {
      this.setFilterSettingValue(v, key);
      this.formatParamsAndSearch();
    },
    handelChange(name, value,flag) {
      //其它筛选值变动
      const index = this.cateList.findIndex((e) => e.name === name);
      if (index > -1) {
        if(flag){
          this.$set(this.cateList[index], 'valueSearchType', value || []);
        }else{
          this.$set(this.cateList[index], 'selectValue', value || []);
        }
      }
    },
    wzSearchType(item){
      let obj={
        hookHeroZhanli:item
      }
      this.wzSearchTypeNum=item
      this.hookHeroZhanli=JSON.stringify(obj)
    },
    handelOtherSearch(queryIntParams) {
      // 其它筛选确认
      this.queryIntParams = queryIntParams;
      this.formatParamsAndSearch();
    },
    handelOtherReset() {
      // 其它筛选重置
      this.pmsSearchTags.forEach(item => {
        this.$set(item, "isActive", false);
      });
      this.cateList = this.cateList.map((i) => ({ ...i, selectValue: i.defaultValue||[] }));
      this.queryIntParams = [];
      this.harborConfigList=[]
      this.keyword=''
      this.$set(this.filterSetting[0], 'selectValue', "在售专区");
      this.formatParamsAndSearch();
    },
    getCateDetail(id) {
      getProductCategory(id).then((res) => {
        if (res.code === 200) {
          this.productCategory = res.data;
          const {custom} = res.data
          const {goodsType} =  JSON.parse(custom||'')
          this.goodsType = goodsType
          
          this.setBannerList();
          this.catePic = res.data.icon;
        }
      });
    },
    setBannerList() {
      let custom = this.productCategory.custom || '{}';
      custom = JSON.parse(custom);
      if (custom.adList2 && custom.adList2.length > 0) {
        // const type = findKey.split('_')[1];
        getCategoryAdverList2({
          ids: custom.adList2.join(','),
        }).then((res) => {
          if (res.code == 200) {
            this.bannerList = res.data;
          }
        });
      }
    },
    // 初始化搜索对象
    initSearchParams() {
      this.searchParam = {
        productCategoryId: this.productCategoryId,
        pageNum: 1,
        pageSize: 10,
      };
    },
    // 根据筛选条件查询
    formatParamsAndSearch(add) {
      this.searchByCate(add);
      if (!add) {
        uni.pageScrollTo({
          scrollTop: 0,
          duration: 50,
        });
      }
    },
    checkSn(list) {
      return false;
      // let reg = /^[a-zA-Z][a-zA-Z0-9]*[0-9]$/;
      // if (
      //   reg.test(this.keyword) &&
      //   list.length === 1 &&
      //   this.searchParam.pageNum === 1
      // ) {
      //   const { id } = list[0];
      //   this.keyword = '';
      //   uni.navigateTo({
      //     url: `/pages/accountDetail/accountDetail?productId=${id}`,
      //   });
      //   return true;
      // }
      // return false;
    },
    // 全文检索
    searchByCate(add, first) {
      uni.showLoading({
        title: '数据加载中~',
      });
      this.isLoading = true;

      // let attrValueList = [];
      // let queryIntParams = undefined;

      // 搜索传参数
      let data = {
        memberId: this.userInfo.id || undefined,
        keyword: this.keyword,
        order: '',
        sort: '',
        custom:this.hookHeroZhanli,
        queryStrParams: [], // ename为'gameAccountQufu'的一些数据
        queryIntParams: this.queryIntParams, // 放价格等通用属性
        attrValueList: [], // 灵活属性
      };

      this.filterSetting.forEach((e) => {
        // 区服属性
        if (e.key === 'gameAccountQufu') {
          if (e.attrObj && e.attrObj.ename && e.attrObj.sort === 32306) {
            if (e.selectValue) {
              data.queryStrParams.push({
                key: e.key,
                value: e.selectValue,
              });
            }
          } else {
            const arr = (e.selectValue || '').split('|');
            e.attrObj.value = arr[arr.length - 1] || undefined;
            delete e.attrObj.inputList;
            data.attrValueList.push(e.attrObj);
          }
        } else if (e.attrObj && e.name === '账号专区') {
          e.attrObj.value = e.selectValue;
          data.attrValueList.push(e.attrObj);
        } else if (e.name === '排序') {
          const [sort, order] = e.selectValue.split('-');
          data.sort = sort;
          data.order = order;
        }
      });
      if(this.harborConfigList.length){
        data.attrValueList=data.attrValueList.concat(this.harborConfigList)
      }
      this.cateList.forEach((ele, index) => {
        let query = {
          ...ele,
        };

        if (query.inputType === 0 && query.searchType === 2) {
          // 输入框
          const [min = '', max = ''] = query.selectValue;
          query.value = '';
          if (min !== '' || max !== '') {
            query.value = [
              min === '' ? 0 : parseInt(min, 10),
              max === '' ? 9999999 : parseInt(max, 10),
            ].join('-');
          }

          // let tempvalue = [...query.selectValue];
          // if (!query.ispublic) {
          //   if (!tempvalue[0] && !tempvalue[1]) {
          //     query.value = '';
          //   } else {
          //     if (tempvalue[0] || tempvalue[1]) {
          //       if (tempvalue[0] === '') {
          //         tempvalue[0] = 0;
          //       }
          //       if (tempvalue[1] === '') {
          //         tempvalue[1] = 9999999;
          //       }
          //     }
          //     query.value = tempvalue.join('-');
          //   }
          // }
        } else {
          query.value = query.selectValue.join(',');
        }

        if (!query.isjilian2) {
          data.attrValueList.push(query);
        }
      });

      // 属性
      // let attrValueListCopy = _.cloneDeep(attrValueList);
      // // 32306专用
      // let dataParamsForQFCopy = _.cloneDeep(this.dataParamsForQF);

      // let queryStrParams = [];

      // if (this.ename === 'defaultEname') {
      //   // 32306没有ename，因为显示的是否被踢出属性列表了，要加回到属性里来
      //   let findSortMax = _.cloneDeep(this.findSortMax);
      //   delete findSortMax.inputList;
      //   findSortMax.value = dataParamsForQFCopy['defaultEname'] || undefined;
      //   attrValueListCopy.push(findSortMax);
      // } else {
      //   // 如果32306有ename，要放到外层
      //   Object.keys(dataParamsForQFCopy).forEach((key) => {
      //     let value = dataParamsForQFCopy[key];
      //     if (value && value.length && value[value.length - 1] == '|') {
      //       value = value.slice(0, -1);
      //     }
      //     if (value !== undefined && value !== '') {
      //       queryStrParams.push({
      //         key,
      //         value: value,
      //       });
      //     }
      //   });
      // }

      // let data = Object.assign(
      //   comprehensiveData,
      //   {
      //     queryStrParams,
      //     queryIntParams,
      //     keyword: this.keyword,
      //   },
      //   {
      //     attrValueList: attrValueListCopy,
      //   },
      // );
      // if (this.stockQuery) {
      //   let queryIntParams = data.queryIntParams;
      //   if (queryIntParams) {
      //     data.queryIntParams = data.queryIntParams.concat(
      //       this.stockQuery.queryIntParams,
      //     );
      //   } else {
      //     data.queryIntParams = this.stockQuery.queryIntParams;
      //   }
      // }

      // if (first == 'first') {
      //   this.firstSearchData = _.cloneDeep(data);
      // } else {
      //   // 比较他有没有改筛选
      //   let copyFirstSearchData = _.cloneDeep(this.firstSearchData);
      //   delete copyFirstSearchData.keyword;
      //   delete copyFirstSearchData.sort;
      //   delete copyFirstSearchData.order;
      //   delete copyFirstSearchData.queryStrParams;
      //   copyFirstSearchData.attrValueList =
      //     copyFirstSearchData.attrValueList.filter((ele) => {
      //       return ele.name != '账号专区';
      //     });
      //   let copyData = _.cloneDeep(data);
      //   delete copyData.keyword;
      //   delete copyData.sort;
      //   delete copyData.order;
      //   delete copyData.queryStrParams;
      //   copyData.attrValueList = copyData.attrValueList.filter((ele) => {
      //     return ele.name != '账号专区';
      //   });
      //   if (_.isEqual(copyFirstSearchData, copyData)) {
      //     this.hasFilterValue = false;
      //   } else {
      //     this.hasFilterValue = true;
      //   }
      // }

      // 筛选重置页面
      if (!add) {
        this.searchParam.pageNum = 1;
      }
      this.searchParam.productCategoryId = parseInt(
        this.searchParam.productCategoryId,
        10,
      );

      searchProductList2(this.searchParam, data)
        .then((response) => {
          if (response && response.code == 200) {
            let list = response.data.list || [];
            if (this.checkSn(list)) {
              return;
            }
            this.totalPage = response.data.totalPage;
            if (this.searchParam.pageNum >= this.totalPage) {
              this.status = 'nomore';
            }
            if (add) {
              this.accountShopList = this.accountShopList.concat(list);
            } else {
              this.accountShopList = response.data.list;
            }
            this.accountShopList.forEach((ele) => {
              const findtss = ele.attrValueList.find((item) => {
                return item.name === '已使用天赏石';
              });
              const findtss2 = ele.attrValueList.find((item) => {
                return item.name === '未使用天赏石';
              });
              ele.tssnum = 0;
              if (findtss) {
                ele.tssnum = findtss.intValue;
              }
              if (findtss2) {
                ele.tssnum = ele.tssnum + findtss2.intValue;
              }
            });
            this.total = response.data.total;
          }
        })
        .finally(() => {
          uni.hideLoading();
          this.isLoading = false;
        });
    },

    // 加载筛选条件
    loadCateList() {
      return getProductAttribute3(this.productCategoryId).then((response) => {
        if (response.code == 200) {
          if(response.data.pmsSearchTags&&response.data.pmsSearchTags.length){
            this.pmsSearchTags=response.data.pmsSearchTags.sort((a, b) => a.sort - b.sort);
          }
          return this.getSearchConfig(response.data.productAttributeList)
          
        }
      });
    },
    getSearchConfig(list){
      this.attributeData=JSON.parse(JSON.stringify(list))
      const data = (list || [])
            .filter((ele) => ele.type !== 0 && ele.searchType !== 0)
            .sort((a, b) => {
              return b.sort - a.sort;
            });

          // 设置综合筛选项属性列表
          const checkBoxAttrGroup = []; // checkBox按照nameGroup分组
          this.cateList = data
            .filter((ele) => ele.name !== '账号专区' && ele.sort !== 32306)
            .map((ele) => {
              const { selectType, nameGroup, inputType, inputList, name,custom ,type} = ele;
              const customObj = JSON.parse(custom||'{}')

              let newInputList;
              if (selectType === 3) {
                // 级联
                const inputList = JSON.parse(ele.inputList);

                newInputList = inputList
                  .map((item) => {
                    return item.childList;
                  })
                  .flat();

                console.log('级联数据', newInputList);
              } else if (selectType === 1 || selectType === 2) {
                newInputList = inputList ? inputList.split(',') : [];
              }
              if(this.productCategoryId==82){
                const heroTypes = ['战士', '刺客', '法师', '射手', '辅助', '坦克'];
                if (inputType !== 0&&!heroTypes.includes(nameGroup||name)) {
                  // if (!heroTypes.includes(nameGroup||name)) {
                  //非输入框
                  ele.nameGroup = nameGroup || name;
                  if(inputType !== 0) {
                    checkBoxAttrGroup.push(ele.nameGroup);
                  } else {
                    // checkBoxAttrGroup.push(ele.nameGroup);
                  }
                }
              }else{
                if (inputType !== 0) {
                  //非输入框
                  ele.nameGroup = nameGroup || name;
                  checkBoxAttrGroup.push(ele.nameGroup);
                }
              }
              // if(type==2){
              //   ele.valueSearchType='must'
              // }
          
              return {
                ...ele,
                inputList:
                  newInputList &&
                  newInputList.filter((item) => item !== '未知'),
                  valueSearchType:ele.valueSearchType||ele.selectType == 2?'should':'must',
                selectValue: ele.selectValue?.length?ele.selectValue:customObj.sdefault?[customObj.sdefault] :[],
                defaultValue:customObj.sdefault?[customObj.sdefault] :[],
              };
            });
            // if(this.productCategoryId==82){
            //   checkBoxAttrGroup.push('按英雄')
            // }
           
          this.checkBoxAttrGroup = [...new Set(checkBoxAttrGroup)];
          console.log('this.checkBoxAttrGroup ', this.checkBoxAttrGroup);
         
          // 找到区服和账号专区
          let QFObj = undefined,
            ZHZQObj = undefined;

          data.forEach((ele) => {
            if (ele.sort === 32306) {
              // 区服
              QFObj = ele;
            }
            if (ele.name == '账号专区') {
              ZHZQObj = ele;
            }
          });

          // 设置账号专区的筛选配置
          if (ZHZQObj) {
            const customObj = JSON.parse(ZHZQObj.custom||'{}')
            const index = this.filterSetting.findIndex((e) => e.key === 'zhzq');
            const zhzqDataList = (ZHZQObj.inputList || '')
              .split(',')
              .map((e) => ({
                value: e,
                label: e,
              }));
            this.filterSetting[index] = {
              ...this.filterSetting[index],
              dataList: zhzqDataList,
              selectValue:ZHZQObj.selectValue&&ZHZQObj.selectValue.length?ZHZQObj.selectValue[0]:customObj.sdefault || zhzqDataList[0].value,
              defaultValue: customObj.sdefault || zhzqDataList[0].value,
              attrObj: ZHZQObj, // 储存起来，到时候搜索时入参需要包装进去
            };
          } else {
            this.filterSetting = this.filterSetting.filter(
              (e) => e.key !== 'zhzq',
            );
          }
          // 设置区服的筛选配置
          this.formatServerData(QFObj);

          // 设置排序的筛选配置
          this.formatClasData();
          return true;
    },
    // 提取区服筛选条件
    formatServerData(findServer) {
      if (!findServer) {
        this.filterSetting = this.filterSetting.filter(
          (e) => e.key !== 'gameAccountQufu',
        );
        return;
      }
      const serveSetting = this.filterSetting.find(
        (e) => e.key === 'gameAccountQufu',
      );
      console.log('区服数据', serveSetting, findServer);

      const { ename, name, selectType, inputList } = findServer;

      serveSetting.attrObj = findServer; // 储存起来筛选使用
      serveSetting.name = name + '选择'; // 不一定叫区服，用后端返回

      if (selectType === 3) {
        // 级联
        const serverDataList = JSON.parse(inputList);
        const newServerDataList = serverDataList.map((e) => {
          return {
            label: e.parent_name,
            value: e.parent_name,
            childList: e.childList.map((i) => ({ value: i, label: i })),
          };
        });
        serveSetting.parentOptions = newServerDataList;
      } else {
        // 一级数据，多选或单选
        // 多选
        const serverDataList = inputList ? inputList.split(',') : [];
        const newServerDataList = serverDataList.map((e) => {
          return {
            label: e,
            value: e,
          };
        });
        serveSetting.dataList = newServerDataList;
      }

      // 赋默认值
      const customObj = JSON.parse(findServer.custom||'{}')
      serveSetting.defaultValue = customObj.sdefault
    },
    // 综合排序提取
    formatClasData() {
      const defaultData = DEFAULT_SORT_DATA;
      let newData = [];
      this.cateList
        .filter((e) => {
          const custom = JSON.parse(e.custom||'{}');
          return e.searchSort && custom.searchSortWeight;
        })
        .forEach((ele) => {
          const { name, searchSort,custom } = ele;
          const {searchSortWeight}= JSON.parse(custom||'{}');
          newData.push({
            value: `asc-${searchSort}`,
            selected: false,
            label: `${name}低到高`,
            searchSort,
            searchSortWeight
          });

          newData.push({
            value: `desc-${searchSort}`,
            selected: false,
            label: `${name}高到低`,
            searchSort,
            searchSortWeight
          });
        });

      newData.sort((a, b) => {
        return b.searchSortWeight - a.searchSortWeight;
      });

      const index = this.filterSetting.findIndex((i) => i.key === 'sort');
      this.filterSetting[index].dataList = defaultData.concat(newData);
    },
    // 列表布局
    chooseListStyle() {
      this.isShowBigItem = !this.isShowBigItem;
      uni.setStorageSync('ACCOUNT_IS_BIG_SHOW', this.isShowBigItem);
    },
    // 搜索框搜索
    doSearch(keyword) {
      this.keyword = keyword;
      this.formatParamsAndSearch();
    },
    // 商品详情
    accountDetail(productId) {
      uni.navigateTo({
        url: '/pages/accountDetail/accountDetail?productId=' + productId,
      });
    },
  },
  onHide() {
    // #ifdef H5
    window.removeEventListener('resize', this.doResize);
    // #endif
  },
};
</script>

<style scoped lang="scss">
.searchTagBox{
  width: calc(100% - 40rpx);
  position: fixed;
  left: 0;
  right: 0;
  z-index: 2;
  height: 76rpx;
  overflow-x: auto; 
  white-space: nowrap;
  padding-left: 40rpx;
  // margin-top: 20rpx;
  background-color: #fff;
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  .tags{
    margin-right: 12rpx;
    font-size: 24rpx;
    border-radius: 8rpx;
    background: #f9f9f9;
    padding: 10rpx 16rpx;
    color: #999999;
  }
  .active{
    background: #ff720c;
    color: #fff;
  }
}
</style>
