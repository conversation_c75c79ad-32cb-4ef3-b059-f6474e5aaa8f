<template>
  <view>
    <!-- 第一层 -->
    <view v-if="dataList.length > 1">
      <view style="display: flex; overflow-x: auto; white-space: nowrap">
        <view
          v-for="(opt, optindex) in dataList.filter(
            (item) => item.nameGroup === nameGroup,
          )"
          :key="optindex + 'group'"
        >
          <view v-if="opt.name !== nameGroup">
            <view
              v-if="opt.name !== nameGroup"
              @click="clickGroupName(opt.name)"
              
              style="margin-right: 20rpx;margin-left: 20rpx;"
              class="opt-box-title"
              >
              <view class="spaceCenter">
                <view class="badgeActive" v-if="opt.selectValue.length"></view>
                <view style="min-width: 50rpx;" :class="groupName == opt.name ? 'group-box-title-active' : ''">
                  {{ opt.name }}
                </view>
              </view>
              </view
            >
          </view>
        </view>
      </view>
      <view v-if="groupName">
        <view
          v-for="(opt, optindex) in cateListChildList"
          :key="optindex + 'childGroup'"
        >
        <view  v-if="opt.selectType == 2"  class="spaceBetween" style="margin-bottom: 20rpx;">
          <view class="spaceCenter" @click="checkboxChange(opt, opt.inputList, opt.selectValue && opt.selectValue.length === opt.inputList.length && opt.selectValue.every(item => opt.inputList.includes(item)))">
            <checkbox 
              color="#fff" 
              class="wz-checkbox-cl" 
              value="cb" 
              :checked="opt.selectValue && opt.selectValue.length === opt.inputList.length && opt.selectValue.every(item => opt.inputList.includes(item))"
            />全选
          </view>
          <view v-if="opt.selectType == 2" class="child-value-box">
            <view class="select-type-box spaceCenter">
              <view
                @click="fulfillmentClick(opt, 'must')"
                :class="
                  opt.valueSearchType && opt.valueSearchType == 'must'
                    ? 'active'
                    : ''
                "
                >全部满足</view
              >
              <view
                @click="fulfillmentClick(opt, 'should')"
                :class="
                  opt.valueSearchType && opt.valueSearchType == 'should'
                    ? 'active'
                    : ''
                "
                >满足一个</view
              >
            </view>
          </view>
         </view>
          <view class="item-content">
            <view
              v-for="(item, index) in opt.inputList.slice(
                0,
                opt.isHide ? 6 : opt.inputList.length,
              )"
              :key="index"
              :class="
                (opt.selectValue || []).includes(item)
                  ? 'opt-box opt-box-selected'
                  : 'opt-box'
              "
              class="opt-box opt-box-child-item"
              @click="handelChildClick(item, opt)"
            >
              {{ fakeName(item) }}
              <image
                v-if="iconFilter(item)"
                :src="iconFilter(item)"
                class="tag_tedian_pic"
              ></image>
            </view>
            <view
              v-if="opt.inputList.length > 6"
              class="open"
              @click="changeItem(opt)"
            >
              <text>{{ opt.isHide ? '展开' : '收起' }}</text>
              <IconFont
                :size="12"
                :class="opt.isHide ? '' : 'rotate-180'"
                style="margin-left: 10rpx"
                icon="arrow-d"
            /></view>
          </view>
        </view>
      </view>

      <view
        v-for="(opt, optindex) in dataList.filter(
          (item) => item.nameGroup === nameGroup,
        )"
        :key="optindex + 'noGroup'"
      >
        <view v-if="opt.name !== nameGroup"></view>
        <view v-else>
          <!-- 第二层 -->
        <view  v-if="opt.selectType == 2"  class="spaceBetween" style="margin-bottom: 20rpx;">
          <view class="spaceCenter" @click="checkboxChange(opt, opt.inputList, opt.selectValue && opt.selectValue.length === opt.inputList.length && opt.selectValue.every(item => opt.inputList.includes(item)))">
            <checkbox 
              color="#fff" 
              class="wz-checkbox-cl" 
              value="cb" 
              :checked="opt.selectValue && opt.selectValue.length === opt.inputList.length && opt.selectValue.every(item => opt.inputList.includes(item))"
            />全选
          </view>
          <view class="child-value-box">
            <view class="select-type-box spaceCenter">
              <view
                @click="fulfillmentClick(opt, 'must')"
                :class="
                  opt.valueSearchType && opt.valueSearchType == 'must'
                    ? 'active'
                    : ''
                "
                >全部满足</view
              >
              <view
                @click="fulfillmentClick(opt, 'should')"
                :class="
                  opt.valueSearchType && opt.valueSearchType == 'should'
                    ? 'active'
                    : ''
                "
                >满足一个</view
              >
            </view>
          </view>
        </view>
          <!-- <view v-if="opt.selectType == 2" class="child-value-box">
            <view class="select-type-box spaceCenter">
              <view
                @click="fulfillmentClick(opt, 'must')"
                :class="
                  opt.valueSearchType && opt.valueSearchType == 'must'
                    ? 'active'
                    : ''
                "
                >全部满足</view
              >
              <view
                @click="fulfillmentClick(opt, 'should')"
                :class="
                  opt.valueSearchType && opt.valueSearchType == 'should'
                    ? 'active'
                    : ''
                "
                >满足一个</view
              >
            </view>
          </view> -->
          <view class="item-content">
            <view
              v-for="(item, index) in opt.inputList.slice(
                0,
                opt.isHide ? 6 : opt.inputList.length,
              )"
              :key="index"
              :class="
                (opt.selectValue || []).includes(item)
                  ? 'opt-box opt-box-selected'
                  : 'opt-box'
              "
              class="opt-box opt-box-child-item"
              @click="handelChildClick(item, opt)"
            >
              {{ fakeName(item) }}
              <image
                v-if="iconFilter(item)"
                :src="iconFilter(item)"
                class="tag_tedian_pic"
              ></image>
            </view>
            <view
              v-if="opt.inputList.length > 6"
              class="open"
              @click="changeItem(opt)"
            >
              <text>{{ opt.isHide ? '展开' : '收起' }}</text>
              <IconFont
                :size="12"
                :class="opt.isHide ? '' : 'rotate-180'"
                style="margin-left: 10rpx"
                icon="arrow-d"
            /></view>
          </view>
        </view>
        <!-- <IconFont :size="10" icon="arrow-down" style="margin-left: 8rpx" /> -->
      </view>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    nameGroup: {
      type: String,
      default: '',
    },
    dataList: {
      type: Array,
      default: () => [],
    },
    defaultName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      groupName: '',
    };
  },
  watch: {
    defaultName: {
      handler(newVal) {
        if (!this.groupName) {
          this.groupName = newVal;
        }
      },
      immediate: true,
    },
  },
  computed: {
    cateListChildList() {
      const skuList = this.dataList.find(
        (item) => item.name === this.groupName,
      );

      return [skuList] || [];
    },
  },
  methods: {
    checkboxChange(opt,item,isActive){
      console.log(opt,item,isActive);
      // item.forEach(element => {
        this.$emit('batchChange', item, opt,isActive);
      // });
      
    },
    clickGroupName(name) {
      this.groupName = name;
    },
    fulfillmentClick(item, opt) {
      this.$emit('fulfillmentClick', item, opt);
    },
    changeItem(opt) {
      this.$emit('open', opt);
    },
    handelChildClick(item, opt) {
      this.$emit('change', item, opt);
    },
    fakeName(name) {
      if (!name) return '';
      return name.replace(/\[[^\]]*\]/, '');
    },
    iconFilter(name) {
      if (name.indexOf('[绝]') !== -1) {
        return '../../../static/old/jue.png';
      } else if (name.indexOf('[钱]') !== -1) {
        return '../../../static/old/price.png';
      } else if (name.indexOf('[核]') !== -1) {
        return '../../../static/old/he.png';
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.item-content {
  display: flex;
  justify-content: flex-start;
  flex-flow: wrap;
}
.opt-box-title {
  width: fit-content;
  margin: 20rpx 0;
  margin-top: 0rpx;
  position: relative;
}
.open {
  width: 100%;
  padding: 10rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: rgba(0, 0, 0, 0.4);
  /deep/.iconfont {
    margin-top: 3rpx;
  }
  .rotate-180 {
    transform: rotate(180deg);
  }
}
.opt-box {
  padding: 20rpx 10rpx;
  background: #f4f5f6;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-sizing: border-box;
  margin-bottom: 20rpx;
  margin-right: 20rpx;
  color: rgba(0, 0, 0, 0.4);
  font-size: 20rpx;
  font-weight: 400;
  position: relative;
  white-space: nowrap;

  .mini {
    display: inline-block;
    min-width: 100rpx;
    padding: 0 12rpx;

    height: 48rpx;
    line-height: 48rpx;
    margin-top: 10rpx;
  }

  .close {
    position: absolute;
    right: 0rpx;
    top: -5rpx;
  }
}
.opt-box-child-item {
  width: calc(33% - 13rpx);
  // word-break: break-word;
  white-space: wrap;
  text-wrap: wrap;
  &:nth-child(3n) {
    margin-right: 0rpx;
  }
}
.opt-box-selected {
  background: $uni-color-primary;
  // background: #fde801;
  // border-color: $uni-color-primary;
  color: #fff;
  font-weight: 500;
}

.level {
  margin-bottom: 10rpx;
  .opt-box {
    height: 50rpx;
    line-height: 50rpx;
    padding: 0rpx 14rpx;
    border-radius: 8rpx;
    background: #f4f5f6;

    color: rgba(0, 0, 0, 0.4);
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    letter-spacing: 0.48px;

    .opt-box-selected {
      border-color: #ff720c;
      color: #ff720c;
      background: #f4f5f6;

      .iconfont {
        transform: rotate(180deg);
      }
    }
  }
}

.child-value-box {
  display: flex;
  justify-content: end;
  .select-type-box {
    width: 256rpx;
    background: #f4f5f6;
    padding: 4rpx;
    // margin-bottom: 20rpx;
    view {
      width: 50%;
      text-align: center;
      color: rgba(0, 0, 0, 0.4);
    }
    .active {
      background: #fff;
      color: #ff720c;
    }
  }
}
.group-box-title-active {
  position: relative;
  &::after {
    content: '';
    position: absolute;
    bottom: -4rpx;
    left: 50%;
    top:40rpx;
    width: 60rpx;
    height: 4rpx;
    background: #ff720c;
    transform: translateX(-50%);
  }
}
.badgeActive{
  position: absolute;
  left: -16rpx;
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  background: #ff720c;
}
.wz-checkbox-cl{
  transform:scale(0.5);
  margin-right: -10rpx;
  /deep/.uni-checkbox-input-checked{
    background: #ff720c!important;
  }
}
</style>
