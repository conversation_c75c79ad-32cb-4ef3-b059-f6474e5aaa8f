<template>
  <view :style="boxStyle" class="popupProjress">
    <view style="height: 100%" class="cate-content">
      <!-- 顶部区块 -->
      <view
        style="

          position: relative;
          z-index: 99;
          box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
        "
      >
        <!-- 搜索区块 -->
        <!-- <view style="margin-bottom: 24rpx; padding: 0 24rpx">
          <MySearchBar
            v-model="cateListKeyword"
            style="margin-bottom: 12rpx"
            placeholder="请输入搜索内容"
            size="small"
            @change="handelSearchByKeyword"
          />

          <view
            v-if="cateListKeyword && !cateListSearchResult.length"
            class="empty"
            >暂无符合条件的筛选项</view
          >

          <view v-else-if="cateListSearchResult.length" class="list-item-box">
            <view class="item-content">
              <view
                v-for="(opt, index) in cateListSearchResult"
                :key="index"
                :class="
                  cateSelectedList.find(
                    (item) =>
                      item.value === opt.value && item.name === opt.name,
                  )
                    ? 'opt-box miniSearch line opt-box-selected'
                    : 'opt-box miniSearch line'
                "
                style="display: inline-block; border-radius: 24rpx"
                @click="handelSelect(opt)"
              >
                {{ fakeName(opt.value) }}
              </view>
            </view>
          </view>
        </view> -->

        <!-- 已选项展示 -->
        <view class="item-box">
          <view style="color: #2d2d2d; margin-bottom: 16rpx;margin-top: 10rpx;"
            >已选({{ cateSelectedList.length }})：</view
          >
          <view class="item-content">
            <scroll-view
              scroll-x="true"
              scroll-left="120"
              style="white-space: nowrap"
            >
              <view
                v-for="(opt, index) in cateSelectedList"
                :key="index"
                class="opt-box mini"
                @click="handelCancel(opt)"
              >
                <text class="ellipsis" style="width: 100rpx">
                  {{ fakeName(opt.value) }}
                </text>
                <IconFont
                  :size="10"
                  icon="close"
                  style="color: #2d2d2d; margin-left: 8rpx"
                />
              </view>
            </scroll-view>
          </view>
        </view>
      </view>

      <view
        :style="{
          display: 'flex',
          flex:1,
          overflow:'auto',
          
        }"
      >
        <view
          style="
            width: 160rpx;
            background: #f7f7f7;
            display: flex;
            flex-direction: column;
          "
        >
          <scroll-view
            :scroll-into-view="leftId"
            :scroll-with-animation="true"
            scroll-y
            class="cate-list"
            @touchmove.stop
          >
            <view
              style="
                width: 160rpx;
                box-sizing: border-box;
                height: 80rpx;
                text-align: center;
                padding: 0 20rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #2d2d2d;
                font-size: 24rpx;
                font-weight: 500;
              "
              v-for="item in leftRightList"
              :style="{ background: item.id == leftIdStyle ? '#fff' : '' }"
              :id="item.id"
              @click="scrollToAnchor(item.id)"
              >{{ item.name }}</view
            >
          </scroll-view>
        </view>
        <view
          style="
            display: flex;
            flex-direction: column;
            width: calc(100vw - 160rpx);
          "
        >
          <scroll-view
            :scroll-into-view="scrollIntoViewId"
            :scroll-with-animation="true"
            @scroll="scrollClick"
            scroll-y
            class="cate-list"
            @touchmove.stop
          >
            <Card
              v-if="priceItem"
              id="priceItem"
              :title="priceItem.name"
              class="item-box"
            >
              <InputGroup
                :value="priceItem.selectValue"
                style="margin: 0"
                @change="($event) => updateSelectValue(priceItem, $event)"
              />
            </Card>
            <view
              v-for="(nameGroup, index) in checkBoxAttrGroup"
              :id="'nameGroup' + index"
              :key="nameGroup"
            >
              <Card :title="nameGroup" class="item-box">
                <CheckBoxList
                  :name-group="nameGroup"
                  :data-list="checkBoxCateList"
                  :defaultName="defaultName(nameGroup)"
                  @change="selectOpt"
                  @fulfillmentClick="fulfillmentClick"
                  @batchChange="batchChange"
                />
              </Card>
            </view>
            <view id="nameGroupWzHeroe1">
              <Card
                v-if="productCategoryId == 82"
                title="职业和英雄"
                class="item-box"
              >
                <WzHeroeBoxList
                  :data-list="wzHeroesList"
                  @change="selectOpt"
                  @batchChange="batchChange"
                  @fulfillmentClick="fulfillmentClick"
                  @wzSearchType="wzSearchType"
                  :wzSearchTypeNum="wzSearchTypeNum"
                />
              </Card>
            </view>
            <view
              v-for="(item, index) in inputCateList"
              :key="index"
              :id="'inputCateList' + index"
            >
            <Card v-if="item.nameGroup" :title="item.nameGroup"  class="item-box">
                <view  v-for="(j,index) in item.list"  >
                  <div style="padding: 20rpx;">{{j.name}}</div>
                  <InputGroup
                  style="margin: 0"
                  :value="j.selectValue"
                  @change="($event) => updateSelectValue(j, $event)"
                />
                </view>
            </Card>
            <Card v-else :title="item.name"  class="item-box">
                  <InputGroup
                  style="margin: 0"
                  :value="item.selectValue"
                  @change="($event) => updateSelectValue(item, $event)"
                />
            </Card>
            </view>
          </scroll-view>
        </view>
      </view>
      <view class="spaceBetween popUp_bottom">
      <view class="kk-btn line" style="width: 45%" @click="reset"
        ><span>重置</span></view
      >
      <view class="kk-btn primary" style="width: 45%" @click="confirm"
        >确认</view
      >
    </view>
    </view>
   
  </view>
</template>
<script>
import CheckBoxList from './CheckBoxList.vue';
import InputGroup from './InputGroup.vue';
import WzHeroeBoxList from './wzHeroeBoxList.vue';

export default {
  components: {
    CheckBoxList,
    InputGroup,
    WzHeroeBoxList,
  },
  props: {
    topH: {
      type: Number,
      default: 0,
    },
    cateList: {
      type: Array,
      default: () => [],
    },
    checkBoxAttrGroup: {
      type: Array,
      default: () => [],
    },
    queryIntParams: {
      type: Array,
      default: () => [],
    },
    productCategoryId: {
      type: [Number, String],
      default: 0,
    },
    wzSearchTypeNum: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {
      cateListKeyword: '', //综合排序搜索关键字
      cateListSearchResult: [], // 综合排序搜索结果
      scrollIntoViewId: 'priceItem',
      leftId: 'priceItem',
      leftIdStyle: 'priceItem',
      scrollFlag: false,
      priceItem: {
        key: 'price',
        name: '价格',
        selectValue: [],
      },
      boxStyle: '',
      scrollTimes: null,
    };
  },
  computed: {
    inputCateList() {
      const inputList = this.cateList.filter((ele) => ele.inputType === 0);
      const groupedList = inputList.reduce((acc, curr) => {
        if (curr.nameGroup) {
          const existingGroup = acc.find(group => group.nameGroup === curr.nameGroup);
          if (existingGroup) {
            existingGroup.list.push(curr);
          } else {
            acc.push({
              nameGroup: curr.nameGroup,
              list: [curr]
            });
          }
        } else {
          acc.push(curr);
        }
        return acc;
      }, []);
      console.log(groupedList,33333333);
      return groupedList
      // return Object.values(groupedList).flat();
      
      return this.cateList.filter((ele) => ele.inputType === 0);
    },
    checkBoxCateList() {
      return this.cateList.filter((ele) => ele.inputType !== 0);
    },
    wzHeroesList() {
      const heroTypes = ['战士', '刺客', '法师', '射手', '辅助', '坦克'];
      let arr = this.cateList.filter((ele) =>
        heroTypes.includes(ele.nameGroup || ele.name),
      );
      let heroList = [
        { name: '战士', arr: [] },
        { name: '刺客', arr: [] },
        { name: '法师', arr: [] },
        { name: '射手', arr: [] },
        { name: '辅助', arr: [] },
        { name: '坦克', arr: [] },
      ];
      heroList.forEach((item) => {
        arr.forEach((j) => {
          if (item.name == j.nameGroup) {
            item.arr.push(j);
          }
        });
      });

      return heroList;
    },
    leftRightList() {
      let arr = [{ name: '价格', id: 'priceItem' }];
      this.checkBoxAttrGroup.forEach((item, index) => {
        arr.push({ name: item, id: 'nameGroup' + index });
      });
      if (this.productCategoryId == 82) {
        arr.push({ name: '职业和英雄', id: 'nameGroupWzHeroe1' });
      }
      // this.cateList
      //   .filter((ele) => ele.inputType === 0)
      //   .forEach((item, index) => {
      //     arr.push({ name: item.name, id: 'inputCateList' + index });
      //   });

      this.cateList
        .filter((ele) => ele.inputType === 0)
        .forEach((item, index) => {
          const name = item.nameGroup || item.name;
          const existingItem = arr.find(existing => existing.name === name);
          if (!existingItem) {
            arr.push({ name, id: 'inputCateList' + index });
          }
        });
        console.log(arr,889911);
        
      return arr;
    },
    cateSelectedList() {
      let arr = [];
      this.cateList.forEach((ele) => {
        if (
          !ele.selectValue ||
          !ele.selectValue.length ||
          ele.inputType === 0
        ) {
          // 输入框
          return;
        } else {
          const valueArr = ele.selectValue.map((v) => ({
            value: v,
            name: ele.name,
          }));
          arr = arr.concat(valueArr);
        }
      });
      return arr;
    },
  },
  created() {
    // #ifdef APP-PLUS
    this.boxStyle = `height:calc( 100vh - ${this.topH}px );top:${this.topH}px`;
    // #endif
  },
  mounted() {
    // 回显价格
    const { min, max } = this.queryIntParams[0] || {};
    if (min || max) {
      this.priceItem.selectValue = [min, max];
    }
    // #ifdef H5
    uni.onWindowResize((res) => {
      this.resize();
    });
    this.resize();
    // #endif
  },
  methods: {
    defaultName(name) {
      let arr = this.checkBoxCateList.filter((item) => item.nameGroup === name);

      if (arr.length > 1) {
        return arr[0].name;
      }
    },
    scrollToAnchor(anchorId) {
      this.scrollIntoViewId = anchorId;
      // this.leftId=anchorId
      this.leftIdStyle = anchorId;
      this.scrollFlag = true;
    },
    // throttle(func, delay) {
    //   let timer = null;
    //   return function(...args) {
    //     if (!timer) {
    //       func.apply(this, args);
    //       timer = setTimeout(() => {
    //         timer = null;
    //       }, delay);
    //     }
    //   };
    // },
    // scrollClick(e,v,c){
    //   console.log(e,v,c)
    //   const scrollTop = e.detail.scrollTop;
    //   const items = document.querySelectorAll('.item-box');
    //   items.forEach((item, index) => {
    //     if (scrollTop >= item.offsetTop && scrollTop < item.offsetTop + item.offsetHeight) {
    //       // this.visibleItem = item.id; // 设置当前可见的元素id
    //       if(item.id || item.parentNode.id){
    //         this.leftId = item.id || item.parentNode.id;
    //         // this.debouncedSetLeftId(item.id || item.parentNode.id);
    //       }
    //     }
    //   });
    // },
    // throttledScrollClick(e) {
    //   this.throttle(this.scrollClick, 1000)(e);
    // },
    scrollClick(e) {
      if (this.scrollTimes) {
        clearTimeout(this.scrollTimes);
      }
      this.scrollTimes = setTimeout(() => {
        const scrollTop = e.detail.scrollTop;
        const items = this.$el.querySelectorAll('.item-box');
        items.forEach((item, index) => {
          if (
            scrollTop >= item.offsetTop &&
            scrollTop < item.offsetTop + item.offsetHeight
          ) {
            if (this.scrollFlag) {
              this.leftIdStyle = item.id || item.parentNode.id;
              this.scrollFlag = false;
            } else {
              this.leftId = item.id || item.parentNode.id;
              this.leftIdStyle = item.id || item.parentNode.id;
              this.scrollFlag = false;
            }
          }
        });
      }, 100);
    },
    resize() {
      uni.getSystemInfo({
        success: (info) => {
          // 获取当前屏幕的宽度
          var windowWidth = info.windowWidth;
          // 获取当前屏幕的高度
          var windowHeight = info.windowHeight;
          // 当前一屏的高度可以通过屏幕高度和屏幕宽度的比例来计算
          var screenHeight = (windowHeight / windowWidth) * info.screenWidth;

          console.log('当前屏幕的高度：' + screenHeight);
          this.boxStyle = `height:${screenHeight - this.topH}px;top:${this.topH}px`;
        },
      });
    },
    fulfillmentClick(item, value) {
      this.$emit('change', item.name, value, true);
    },
    //全选反选
    batchChange(value, item, isActive) {
      if (isActive) {
        this.$emit('change', item.name, '');
        return;
      }
      const { filterType, selectType, selectValue } = item;
      let v = JSON.parse(JSON.stringify(value));
      this.$emit('change', item.name, v);
    },
    wzSearchType(item) {
      this.$emit('wzSearchType', item);
    },
    // 筛选的单选和多选选择值
    selectOpt(value, item) {
      const { filterType, selectType, selectValue } = item;
      let v = JSON.parse(JSON.stringify(selectValue));

      //单选处理 selectType 1 单选，2 多选，3 级联
      if (filterType !== 1) {
        if (selectValue.includes(value)) {
          v = [];
        } else {
          v = [value];
        }
      } else {
        // 多选处理 filterType=1 表示多选
        if (selectValue.includes(value)) {
          v = selectValue.filter((e) => e !== value);
        } else {
          v.push(value);
        }
      }

      this.$emit('change', item.name, v);
      // selectType 1 单选，2 多选，3 级联
      // if (selectType === 3) {
      //   // 级联的就保持单选
      //   if (item.selectValue[0] === value) {
      //     // 取消
      //     item.selectValue = [];
      //     this.cateList[index + 1].inputList = [];
      //   } else {
      //     item.selectValue = [value];
      //     if (item.saveInputList) {
      //       const findIt = item.saveInputList.find(
      //         (ele) => ele.parent_name == value,
      //       );
      //       this.cateList[index + 1].inputList = findIt.childList;
      //       this.cateList[index + 1].selectValue = [];
      //     }
      //   }
      // } else {
      //   filterType表示筛选的属性可能是多选;
      //   if (filterType === 1) {
      //     let findOld = item.selectValue.findIndex((ele) => ele === value);
      //     if (findOld === -1) {
      //       item.selectValue.push(value);
      //     } else {
      //       item.selectValue.splice(findOld, 1);
      //     }
      //   } else {
      //     if (item.selectValue[0] === value) {
      //       // 取消
      //       item.selectValue = [];
      //     } else {
      //       item.selectValue = [value];
      //     }
      //   }
      // }
    },
    fakeName(name) {
      return name.replace(/\[[^\]]*\]/, '');
    },
    // getTitle(item) {
    //   const { filterType } = item;
    //   if (filterType === 1) {
    //     return `${item.name}(可多选)`;
    //   } else {
    //     return item.name;
    //   }
    // },
    handelSearchByKeyword(cateListKeyword) {
      // 综合排序搜索
      if (!cateListKeyword) {
        this.cateListSearchResult = [];
        return;
      }
      let arr = [];

      this.cateList.forEach((ele) => {
        // 不为输入框
        if (ele.inputType !== 0) {
          let data = ele.inputList || [];
          // if (ele.selectType === 3) {
          //   // 级联数据
          //   data = ele.inputList.map((item) => item.childList).flat(); // 取出所有子级数据
          // }
          // console.log(data,cateListKeyword,111111)
          const result = data
            .filter((e) => e !== undefined && e.includes?.(cateListKeyword))
            .map((e) => ({ name: ele.name, value: e }));

          arr = arr.concat(result);
        }
      });
      console.log(this.cateListSearchResult, arr, 22222);
      this.cateListSearchResult = arr;
    },
    // 搜索展示项点击
    handelSelect(opt) {
      const obj = this.cateList.find((item) => item.name === opt.name);
      if (obj) {
        this.selectOpt(opt.value, obj);
      }
    },
    handelCancel(opt) {
      // 已选项取消
      const { selectValue = [] } =
        this.cateList.find((e) => e.name === opt.name) || {};
      const newSelectValue = selectValue.filter((e) => e !== opt.value);
      this.$emit('change', opt.name, newSelectValue);
    },
    reset() {
      this.addClassName('remove');
      // 公用属性
      this.priceItem.selectValue = [];

      this.$emit('close');
      this.$emit('reset');
    },
    confirm() {
      this.addClassName('remove');
      let queryIntParams;
      const [min = '', max = ''] = this.priceItem.selectValue;
      if (min !== '' || max !== '') {
        queryIntParams = [
          {
            min: min === '' ? undefined : parseInt(min, 10),
            key: 'price',
            max: max === '' ? undefined : parseInt(max, 10),
          },
        ];
      }

      this.$emit('close');
      this.$emit('search', queryIntParams);
    },
    addClassName(e) {
      try {
        if (e == 'add') {
          document.querySelector('body').classList.add('no-scroll');
        } else if (e == 'remove') {
          document.querySelector('body').classList.remove('no-scroll');
        }
      } catch {
        console.warn('未获取到 body 元素，无法添加或移除类名');
      }
    },
    // 筛选框输入框值变更
    updateSelectValue(item, value) {
      // 公用属性
      if (item.name === '价格') {
        item.selectValue = value;
        return;
      }
      this.$emit('change', item.name, value);
    },
  },
};
</script>
<style lang="scss" scoped>
.popupProjress {
  position: relative;
  bottom: 0;
  background: #fff;
  height: 100vh;
  overflow: auto;
  box-sizing: border-box;
}
.item-content {
  display: flex;
  justify-content: flex-start;
  flex-flow: wrap;
  .opt-box {
    padding: 10px 26px;
    border-radius: 40rpx;
    // border: 1rpx solid rgba(0, 0, 0, 0.1);
    border: 1rpx solid #ff720c;
    background: #fff;

    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;

    height: 66rpx;
    line-height: 66rpx;
    box-sizing: border-box;

    margin: 0 20rpx 20rpx 0;

    color: rgba(0, 0, 0, 0.4);
    font-size: 24rpx;
    font-weight: 400;
    position: relative;

    &.line {
      // background: none;
      // border: solid 1px #999;
    }
    &.miniSearch {
      display: flex !important;
      align-items: center;
    }
    &.mini {
      display: inline-block;
      min-width: 100rpx;
      padding: 0 12rpx;

      height: 48rpx;
      line-height: 48rpx;
      margin-top: 10rpx;
    }

    .close {
      position: absolute;
      right: 0rpx;
      top: -5rpx;
    }
  }
  .opt-box-selected {
    background: $uni-color-primary;
    border-color: $uni-color-primary;
    color: #fff;
    font-weight: 500;
  }

  &.level {
    margin-bottom: 10rpx;
    .opt-box {
      height: 66rpx;
      line-height: 66rpx;
      padding: 16rpx 20rpx;
      border-radius: 48rpx;
      border: solid 2rpx #969696;
      background: #fff;

      color: rgba(0, 0, 0, 0.4);
      font-size: 22rpx;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      letter-spacing: 0.48px;

      &.opt-box-selected {
        border-color: #ff720c;
        color: #ff720c;
        background: #fbf9f7;

        .iconfont {
          transform: rotate(180deg);
        }
      }
    }
  }
}
.cate-content {
  background: #fff;
  width: 100%;
  // border-top-right-radius: 20rpx;
  // border-top-left-radius: 20rpx;
  // padding-bottom: 140rpx;
  padding-top: 24rpx;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  box-sizing: border-box;

  .empty {
    text-align: center;
    color: #999;
    font-size: 24rpx;
  }

  .cate-list {
    display: flex;
    flex-direction: column;
    height: 1rpx;
    flex: 1;
  }
  .item-box {
    margin: 0 30rpx 0 30rpx;
    font-size: 24rpx;
  }
}
.popUp_bottom {
  // position:fixed;
  // left: 0;
  // bottom: 0;
  // right: 0;
  height:100rpx;
  border-top: 1rpx solid #f3f3f3;
  background: #fff;
  box-shadow: -1px -2px 3px 0px rgba(0, 0, 0, 0.05);
  padding: 16rpx 40rpx;
}
</style>
