<template>
  <view>
    <!-- 第一层   v-for="(opt, optindex) in dataList.filter(
            (item) => item.nameGroup === nameGroup,
          )"-->
          <view >
          <view class="wz-select-title" :class="wzSearchTypeNum==1?'wz-select-title-active':''" @click="wzSearchTitleClick(1)">包含已选英雄战力标的账号</view>
          <!-- <view class="wz-select-title" :class="wzSearchType.includes(2)?'wz-select-title-active':''" @click="wzSearchTitleClick(2)">包含已选英雄战力标的账号</view> -->
        </view>
    <view style="display: flex" v-if="dataList.length > 1">
      <view v-for="(opt, optindex) in dataList" :key="optindex">
        <view
          class="wz-box-title"
          :class="titleName == opt.name ? 'wz-box-title-active' : ''"
          @click="clickTitle(opt.name)"
          >
            <view class="spaceCenter">
              <view v-if="opt.arr && opt.arr.some(item => item.selectValue && item.selectValue.length > 0)" class="badgeActive"></view>
              <view>{{ opt.name }}</view>
            </view>
          </view
        >
      </view>
    </view>
    <view v-if="titleName">
      <view v-for="(opt, optindex) in cateListChildList">
        <view class="spaceBetween" style="margin-bottom: 20rpx;">
          <view class="spaceCenter" @click="checkboxChange(opt, opt.inputList, opt.selectValue && opt.selectValue.length === opt.inputList.length && opt.selectValue.every(item => opt.inputList.includes(item)))">
            <checkbox 
              color="#fff" 
              class="wz-checkbox-cl" 
              value="cb" 
              :checked="opt.selectValue && opt.selectValue.length === opt.inputList.length && opt.selectValue.every(item => opt.inputList.includes(item))"
            />{{ opt.name }}
          </view>
          <view class="wz-child-value-box">
            <view class="select-type-box spaceCenter">
              <view
                @click="fulfillmentClick(opt, 'must')"
                :class="
                  opt.valueSearchType && opt.valueSearchType == 'must'
                    ? 'active'
                    : ''
                "
                >全部满足</view
              >
              <view
                @click="fulfillmentClick(opt, 'should')"
                :class="
                  opt.valueSearchType && opt.valueSearchType == 'should'
                    ? 'active'
                    : ''
                "
                >满足一个</view
              >
            </view>
          </view>
        </view>
        <view class="item-content">
          <view
            v-for="(item, index) in opt.inputList"
            :key="index"
            :class="
              (opt.selectValue || []).includes(item)
                ? 'opt-box opt-box-selected'
                : 'opt-box'
            "
            class="opt-box opt-box-child-item"
            @click="handelChildClick(item, opt)"
          >
            {{ item }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    nameGroup: {
      type: String,
      default: '',
    },
    dataList: {
      type: Array,
      default: () => [],
    },
    wzSearchTypeNum: {
      type: [String,Number],
      default: 0,
    },
  },
  data() {
    return {
      titleName: '战士',
    };
  },
  computed: {
    cateListChildList() {
      const targetHero = this.dataList.find(
        (item) => item.name === this.titleName,
      );
      return targetHero?.arr || [];
    },
  },
  methods: {
    wzSearchTitleClick(v){
      this.$emit('wzSearchType',this.wzSearchTypeNum==0?1:0)
    },
    checkboxChange(opt,item,isActive){
      console.log(opt,item,isActive);
      // item.forEach(element => {
        this.$emit('batchChange', item, opt,isActive);
      // });
      
    },
    clickTitle(name) {
      this.titleName = name;
    },
    fulfillmentClick(item, opt) {
      this.$emit('fulfillmentClick', item, opt);
    },
    handelChildClick(item, opt) {
      console.log(item,opt);
      this.$emit('change', item, opt);
    },
  },
};
</script>
<style lang="scss" scoped>
.item-content {
  display: flex;
  justify-content: flex-start;
  flex-flow: wrap;
}
.wz-box-title {
  width: fit-content;
  margin: 20rpx 20rpx;
  margin-top: 0rpx;
  position: relative;
}
.wz-box-title-active {
  border-bottom: 4rpx solid #ff720c;
}
.open {
  width: 100%;
  padding: 10rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: rgba(0, 0, 0, 0.4);
  /deep/.iconfont {
    margin-top: 3rpx;
  }
  .rotate-180 {
    transform: rotate(180deg);
  }
}
.opt-box {
  padding: 20rpx 10rpx;
  background: #f4f5f6;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-sizing: border-box;
  margin-bottom: 20rpx;
  margin-right: 20rpx;
  color: rgba(0, 0, 0, 0.4);
  font-size: 20rpx;
  font-weight: 400;
  position: relative;
  white-space: nowrap;

  .mini {
    display: inline-block;
    min-width: 100rpx;
    padding: 0 12rpx;

    height: 48rpx;
    line-height: 48rpx;
    margin-top: 10rpx;
  }

  .close {
    position: absolute;
    right: 0rpx;
    top: -5rpx;
  }
}
.opt-box-child-item {
  width: calc(33% - 13rpx);
  // word-break: break-word;
  white-space: wrap;
  text-wrap: wrap;
  &:nth-child(3n) {
    margin-right: 0rpx;
  }
}
.opt-box-selected {
  background: $uni-color-primary;
  // background: #fde801;
  // border-color: $uni-color-primary;
  color: #fff;
  font-weight: 500;
}

.level {
  margin-bottom: 10rpx;
  .opt-box {
    height: 50rpx;
    line-height: 50rpx;
    padding: 0rpx 14rpx;
    border-radius: 8rpx;
    background: #f4f5f6;

    color: rgba(0, 0, 0, 0.4);
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    letter-spacing: 0.48px;

    .opt-box-selected {
      border-color: #ff720c;
      color: #ff720c;
      background: #f4f5f6;

      .iconfont {
        transform: rotate(180deg);
      }
    }
  }
}

.wz-child-value-box {
  display: flex;
  justify-content: end;
  .select-type-box {
    width: 256rpx;
    background: #f4f5f6;
    padding: 4rpx;
    view {
      width: 50%;
      text-align: center;
      color: rgba(0, 0, 0, 0.4);
    }
    .active {
      background: #fff;
      color: #ff720c;
    }
  }
}
.wz-checkbox-cl{
  transform:scale(0.5);
  margin-right: -10rpx;
  /deep/.uni-checkbox-input-checked{
    background: #ff720c!important;
  }
}
.badgeActive{
  position: absolute;
  left: -16rpx;
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  background: #ff720c;
}
.wz-select-title{
  width: 300rpx;
  background: #f4f5f6;
  padding: 10rpx 5rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
}
.wz-select-title-active{
  background: #FF720C;
    color: #fff;
    font-weight: 500;
}
</style>
