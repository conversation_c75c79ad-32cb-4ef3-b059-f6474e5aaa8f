<template>
  <view>
    <uni-nav-bar
      :status-bar="true"
      :border="false"
      :fixed="true"
      left-width="40rpx"
      color="#333333"
      background-color="#f3f3f3"
      left-icon="left"
      @clickLeft="backFun"
    >
      <view @click="backFun">
        <image :src="catePic" class="catePic" mode="scaleToFill" />
      </view>
      <uni-search-bar
        v-model="keyword"
        :radius="6"
        @confirm="doSearch"
        style="width: 100%; padding: 10rpx 0 0"
        bg-color="#ffffff"
        placeholder="请输入搜索内容"
        cancel-button="none"
      >
      </uni-search-bar>
      <view class="search-btn" @click="doSearch">搜索</view>
    </uni-nav-bar>

    <view
      class="pageCommon"
      style="background-color: #f3f3f3; min-height: 100vh; padding-top: 0"
    >
      <view>
        <swiper
          class="swiper swiper_detail_banner"
          :indicator-dots="indicatorDots"
          :autoplay="autoplay"
          :interval="interval"
          :duration="duration"
          :indicator-color="indicatorColor"
          :indicator-active-color="indicatorActiveColor"
        >
          <swiper-item v-for="(item, index) in bannerList" :key="index">
            <image
              @click="pageBannerGo(item)"
              class="banner_pic"
              :src="item.pic"
              mode="widthFix"
            ></image>
          </swiper-item>
        </swiper>
      </view>
      <view class="spaceBetween bar">
        <view class="element-item" @click="goNext(0)">
          <image
            class="pic"
            mode="aspectFill"
            src="../../static/old/nsh11.jpg"
          ></image>
          <view class="note">账号交易</view>
        </view>
        <view class="element-item" @click="goNext(1)">
          <image
            class="pic"
            mode="aspectFill"
            src="../../static/old/nsh22.jpg"
          ></image>
          <view class="note">天赏石</view>
        </view>
        <view class="element-item" @click="goNext(2)">
          <image
            class="pic"
            mode="aspectFill"
            src="../../static/old/nsh33.jpg"
          ></image>
          <view class="note">顶级账号</view>
        </view>
        <view class="element-item" @click="goNext(3)">
          <image
            class="pic"
            mode="aspectFill"
            src="../../static/old/nsh44.jpg"
          ></image>
          <view class="note">游戏交流</view>
        </view>
      </view>
      <view class="tit">热门专区</view>
      <view class="spaceBetween hot">
        <view class="element-item" @click="goZq(0)">
          <image
            class="pic"
            mode="aspectFill"
            src="../../static/old/nsh4.png"
          ></image>
          <view class="note">战力号专区</view>
        </view>
        <view class="element-item" @click="goZq(1)">
          <image
            class="pic"
            mode="aspectFill"
            src="../../static/old/nsh2.png"
          ></image>
          <view class="note">外观号专区</view>
        </view>
        <view class="element-item" @click="goZq(2)">
          <image
            class="pic"
            mode="aspectFill"
            src="../../static/old/nsh3.png"
          ></image>
          <view class="note">低价专区专区</view>
        </view>
        <view class="element-item" @click="goZq(3)">
          <image
            class="pic"
            mode="aspectFill"
            src="../../static/old/nsh1.png"
          ></image>
          <view class="note">已成交专区</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import uniNavBar from '@/uni_modules/uni-nav-bar/components/uni-nav-bar/uni-nav-bar.vue';
import uniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue';

import uniSearchBar from '@/uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar.vue';

import { getProductCategory } from '@/config/api/submitAccount.js';
import { getCategoryAdverList } from '@/config/api/kf.js';

export default {
  components: {
    uniIcons,
    uniNavBar,
    uniSearchBar,
  },
  data() {
    return {
      qqlist: [],
      bannerList: [],
      keyword: '',
      catePic: '',
      indicatorDots: true,
      autoplay: true,
      interval: 3500,
      duration: 500,
      indicatorColor: '#e9e1e1',
      indicatorActiveColor: '#FC6116',
    };
  },
  onShow() {},
  onReady() {},
  onLoad(e) {
    if (e.productCategoryId) {
      this.productCategoryId = e.productCategoryId;
      this.getCateDetail(this.productCategoryId);
    }
  },
  onReachBottom() {},
  // 下拉刷新监听
  onPullDownRefresh() {},
  methods: {
    goNext(index) {
      if (index == 3) {
        // 打开 qq 群
        if (this.qqlist[0]) {
          let url = this.qqlist[0].url;
          uni.previewImage({
            urls: [url],
            success: (result) => {},
            fail: (error) => {},
          });
        }
      } else if (index == 1) {
        uni.navigateTo({
          url: `/pages/accountList/accountList?productCategoryId=134`,
        });
      } else {
        uni.navigateTo({
          url: `/pages/accountList/accountList?productCategoryId=${this.productCategoryId}&initParam=${index}`,
        });
      }
    },
    pageBannerGo(date) {
      const { h5url = '', appurl = '' } = date;
      // #ifdef H5
      if (h5url.indexOf('http') == 0) {
        window.open(h5url);
      } else {
        uni.navigateTo({
          url: h5url,
        });
      }
      // #endif
      // #ifdef APP-PLUS
      if (appurl.indexOf('http') == 0) {
        plus.runtime.openURL(appurl);
      } else {
        uni.navigateTo({
          url: appurl,
        });
      }
      // #endif
    },
    getCateDetail(id) {
      getProductCategory(id).then((res) => {
        if (res.code === 200) {
          let custom = res.data.custom || '{}';
          custom = JSON.parse(custom);
          let teams = custom.teams || {};
          this.qqlist = teams.list || [];
          this.catePic = res.data.icon;
          const findKey = Object.keys(custom).find(
            (ele) => ele.indexOf('advertise') == 0,
          );
          if (findKey) {
            // const type = findKey.split('_')[1];
            this.getAd(id, 2);
          }
        }
      });
    },
    getAd(categoryId, type) {
      getCategoryAdverList({
        categoryId,
        type,
      }).then((res) => {
        if (res.code == 200) {
          this.bannerList = res.data;
        }
      });
    },
    backFun() {
      uni.navigateBackCustom();
    },
    doSearch() {
      uni.navigateTo({
        url: `/pages/accountList/accountList?productCategoryId=${this.productCategoryId}&keyword=${this.keyword}`,
      });
    },
    goZq(index) {
      uni.navigateTo({
        url: `/pages/accountList/accountList?productCategoryId=${this.productCategoryId}&zq=${index}`,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
uni-swiper {
  height: 120rpx;
}
.pageCommon {
  .swiper {
    padding-top: 20rpx;
  }
  .tit {
    color: #333;
    font-size: 36rpx;
    font-weight: 500;
    margin: 60rpx 0 30rpx;
  }
  .bar {
    margin-top: 60rpx;
    .element-item {
      text-align: center;
      .pic {
        width: 132rpx;
        height: 132rpx;
        margin-bottom: 20rpx;
      }
      .note {
        font-size: 26rpx;
      }
    }
  }
  .hot {
    flex-wrap: wrap;
    .element-item {
      text-align: center;
      margin-bottom: 40rpx;
      .pic {
        border-radius: 10rpx;
        width: 340rpx;
        height: 200rpx;
        margin-bottom: 20rpx;
      }
      .note {
        font-size: 28rpx;
      }
    }
  }
}
.catePic {
  width: 68rpx;
  height: 68rpx;
  margin-right: 12rpx;
  padding: 10rpx 0;
}
.search-btn {
  width: 100rpx;
  text-align: center;
  line-height: 88rpx;
  font-size: 28rpx;
  color: red;
  padding-left: 20rpx;
}
/deep/ .uni-navbar__header-container {
  padding: 0;
}
/deep/ .uni-navbar__header-btns-right {
  display: none;
}
.banner_pic {
  border-radius: 20rpx;
  width: 100%;
}
</style>
