<template>
  <view
    style="background-color: #fdf5ed; box-sizing: border-box; min-height: 100vh"
  >
    <PageNav
      title="我的收藏"
      background-color="#fff"
      theme="black"
      shadow
    ></PageNav>
    <!-- <SearchMenu :list-data="PRODUCT_STATUS" @change="statusSearch" /> -->

    <view
      v-if="collectList.length > 0"
      style="margin-top: 24rpx"
      class="pageCommon"
    >
      <view v-for="item in collectList" :key="item.id">
        <OrderCard
          v-bind="{
            time: item.createTime,
            name: item.productCategoryName,
            desc: item.productName,
            pic: item.productPic,
            price: item.newProductPrice,
            historyPrice:
              item.productPrice !== item.newProductPrice
                ? item.productPrice
                : 0,
          }"
          @toPage="accountDetail(item)"
        >
          <view
            slot="head-right"
            :class="(PRODUCT_STATUS_OBJ[item.productStatus]||{}).class"
            >{{ getProductStatus(item) }}</view
          >
          <view style="padding: 18rpx 0" class="spaceEnd">
            <view class="gray-text-btn" @click="deletCollect(item)"
              >取消收藏</view
            >
            <view
              v-if="(PRODUCT_STATUS_OBJ[item.productStatus] || {}).canBuy"
              class="kk-btn orange"
              style="margin-left: 40rpx"
              @click="accountDetail(item)"
              >立即购买</view
            >
          </view>
        </OrderCard>
      </view>
      <view>
        <uniLoadMore :status="status" icon-type="snow" color="#666666" />
      </view>
    </view>

    <KKEmpty v-else style="margin:40rpx" />

    <!-- 二次询问弹窗 -->
    <TipPanel
      v-if="showConfirmPop"
      @cancel="showConfirmPop = false"
      @confirm="active"
    >
      <view class="second-tip-panel-txt"
        >您确认是否要操作？确认后不可撤销。
      </view>
    </TipPanel>
  </view>
</template>

<script>
import uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue';
import { mapState } from 'vuex';
import { PRODUCT_STATUS, PRODUCT_STATUS_OBJ } from '@/utils/const';

import {
  getProductCollectionList,
  productCollectionDetele,
} from '@/config/api/accountDetail.js';
import OrderCard from '@/components/product/OrderCard.vue';

export default {
  components: {
    uniLoadMore,
    OrderCard,
  },
  data() {
    return {
      status: 'more',
      page: 1,
      totalPage: 1,
      collectList: [],

      PRODUCT_STATUS,
      PRODUCT_STATUS_OBJ,
      showConfirmPop: false,
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo,
    }),
  },
  onReachBottom() {
    if (this.page < this.totalPage) {
      this.status = 'loading';
      this.page++;
      this.getCollectList('add');
    } else {
      this.status = 'loading';
      setTimeout(() => {
        this.status = 'noMore';
        // uni.showToast({
        //   title: '已全部加载完毕',
        //   icon: 'none',
        // });
      }, 1000);
    }
  },
  // 下拉刷新监听
  onPullDownRefresh() {
    setTimeout(() => {
      uni.stopPullDownRefresh();
    }, 1000);
    this.page = 1;
    this.getCollectList();
  },
  onShow() {
    this.$store.dispatch('setUserInfoStore');
    this.page = 1;
    this.totalPage = 1;
    this.getCollectList();
  },
  methods: {
    getProductStatus(item) {
      if (item.productStatus == 1 || item.productStatus == -1) {
        return item.productStatusTxt;
      } else {
        return '已售';
      }
    },
    // 初始化列表
    getCollectList(str) {
      uni.showLoading({
        title: '拼命加载中',
      });
      getProductCollectionList({
        pageNum: this.page,
        pageSize: 10,
        status: this.status,
      })
        .then((res) => {
          if (res.code == 200) {
            this.totalPage = res.data.totalPage;
            if (str == 'add') {
              this.collectList = this.collectList.concat(res.data.list);
            } else {
              this.collectList = res.data.list;
            }
            if (this.page == this.totalPage) {
              this.status = 'noMore';
            }
          }
        })
        .finally(() => {
          uni.hideLoading();
        });
    },
    // 取消收藏
    deletCollect(item) {
      this.showConfirmPop = true;
      this.activeItem = item;
    },
    active() {
      const { productId } = this.activeItem;
      uni.showLoading({
        title: '拼命加载中',
      });
      productCollectionDetele({
        productId,
      })
        .then((res) => {
          if (res.code == 200) {
            let collects = uni.getStorageSync('collects');
            collects = collects ? JSON.parse(collects) : [];
            collects = collects.filter((ele) => {
              return ele != productId;
            });
            uni.setStorageSync('collects', JSON.stringify(collects));
            uni.showToast({
              title: '取消成功！',
              icon: 'none',
            });
            this.refresh();
          }
          uni.hideLoading();
        })
        .finally(() => {
          this.showConfirmPop = false;
        });
    },
    statusSearch(status) {
      this.status = status;
      this.refresh();
    },
    refresh() {
      this.page = 1;
      this.getCollectList();
    },
    // 商品详情
    accountDetail(date) {
      uni.navigateTo({
        url: '/pages/accountDetail/accountDetail?productId=' + date.productId,
      });
    },
  },
};
</script>
