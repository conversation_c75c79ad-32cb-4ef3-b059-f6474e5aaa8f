<template>
  <view>
    <view
      :class="[isAddType ? 'add-type' : '', checked ? 'active' : '']"
      class="baojia_item_box"
      @click="handelConfirm"
    >
      <view class="baojia_item g-logo-bg-gray">
        <view class="spaceBetween pre" style="margin-bottom: 40rpx">
          <view class="name-text">
            {{ item.value }}
            <IconFont
              v-if="showExplain"
              :size="12"
              :class="isAddType ? 'c-red' : 'c-primary'"
              icon="remind"
              style="margin-left: 8rpx;display: inline-block;margin-top: -8rpx;height: 28rpx;"
              @click.native.stop="showBaopeiDetail"
            />
          </view>
          <view
            :class="isAddType ? 'gradient-red' : 'gradient-primary'"
            class="price-text"
            >¥{{ isNaN(item.productPrice)? getPrice2(item):item.productPrice.toFixed(2) }}</view
          >
        </view>

        <view class="pre">
          <view style="margin-right: 120rpx">
            <view class="rule-text">
              <text>最高赔付</text>
              <text
                :class="isAddType ? 'gradient-red' : 'gradient-primary'"
                class="payPrice"
                >{{ getPrecent(item) }}%，¥{{ getPrice(item) }}</text
              >
            </view>
            <rich-text :nodes="getRuler(item)" class="bp_note"></rich-text>
          </view>
          <view  style="position: absolute; right: 0; bottom: 0">
            <IconFont
              v-if="checked" 
              :size="16"
              :class="isAddType ? 'c-red' : 'c-primary'"
              icon="list"
            />
            <view v-else style="width:15px;height:15px;border-radius: 16px;border: solid 1px #969696;"></view>
          </view>
        </view>
      </view>
    </view>

    <TipPanel
      v-if="baopeiDetailShow"
      :has-cancel="false"
      :confirm-btn-style="{ width: '340rpx' }"
      sub-title="包赔服务"
      confirm-text="选择此赔付服务"
      @cancel="baopeiDetailShow = false"
      @confirm="handelConfirm('pop')"
    >
      <view class="bp_text">
        <view class="bp_title">包赔规则</view>
        <rich-text :nodes="item.detail"></rich-text>
        <view class="bp_title" style="margin-top: 40rpx">注意事项</view>
        <rich-text :nodes="item.note"></rich-text>
      </view>
    </TipPanel>
  </view>
</template>
<script>
import util from '@/utils/util';

export default {
  props: {
    index: {
      type: Number,
      default: 0,
    },
    item: {
      type: Object,
      default: () => {},
    },
    checked: {
      type: Boolean,
      default: false,
    },
    showExplain: {
      type: Boolean,
      default: true,
    },
    gamePrice: {
      type: Number,
      default: 0,
    },
    isAddType: {
      // 是否增值服务
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      baopeiDetailShow: false,
    };
  },
  methods: {
    showBaopeiDetail() {
      this.baopeiDetailShow = true;
    },
    getPrice() {
      return util.times(this.gamePrice, this.item.topRepay);
    },
    getPrice2() {
      return util.times(this.gamePrice, this.item.price);
    },
    getPrecent() {
      return util.times(this.item.topRepay, 100, 0);
    },
    getRuler() {
      let { ruler, topRepay } = this.item;
      if (topRepay == -1) {
        const result = ruler.replace(
          '全额退回',
          `<span class="gradient-primary">全额退回</span>`,
        );
        return result;
      } 
      return ruler;
    },
    handelConfirm(str) {
      this.baopeiDetailShow = false;
      this.$emit('choose', str);
    },
  },
};
</script>
<style lang="scss" scoped>
.c-red {
  color: #ff002e;
}
.baojia_item_box {
  width: 100%;
  box-sizing: border-box;
  border-radius: 20rpx;
  background: rgba(166, 166, 166, 0.4);
  padding: 2rpx;
  overflow: hidden;

  margin-bottom: 16rpx;

  &.active {
    background: linear-gradient(180deg, #ffb74a 0%, #ff7a00 100%);

    .baojia_item {
      background: #fffcf9;
      &::before {
        background: url('@/assets/imgs/logo_bg.png') center no-repeat;
        background-size: cover;
      }
    }
  }

  &.add-type.active {
    background: linear-gradient(87deg, #ff002e 3.31%, #ffc0c0 142.11%);

    .baojia_item {
      background: #fffdfd;
      &::before {
        background: url('@/assets/imgs/logo_bg_red.png') center no-repeat;
        background-size: cover;
      }
    }
  }
}
// 包赔选项框
.baojia_item {
  width: 100%;
  box-sizing: border-box;
  padding: 26rpx 32rpx;
  border-radius: 19rpx;
  background: #fbfbfb;

  color: #969696;
  font-size: 20rpx;
  font-weight: 400;

  // &.active {
  //   border: 1px solid #ffb74a;
  //   background: #fffcf9;

  //   &.add-type {
  //     border: 1px solid #ffb74a;
  //   }
  // }

  .name-text {
    color: #000;
    font-size: 28rpx;
    font-weight: 500;
  }
  .price-text {
    text-align: right;
    font-family: Inter;
    font-size: 32rpx;
    font-weight: 600;
  }

  .rule-text {
    color: #505050;
    font-size: 24rpx;
    margin-bottom: 8rpx;

    .payPrice {
      margin-left: 4rpx;
      margin-bottom: 4rpx;
      font-family: YouSheBiaoTiHei;
      font-size: 32rpx;
    }
  }

  .bp_note {
    width: 400rpx;
    font-size: 24rpx;
  }
}

// 包赔规则介绍弹窗
.bp_text {
  color: #969696;
  font-size: 20rpx;
  font-weight: 400;
  line-height: 18px;

  margin-bottom: 40rpx;
  margin-top: 40rpx;

  .bp_title {
    color: #1b1b1b;
    font-family: YouSheBiaoTiHei;
    font-size: 24rpx;
    font-weight: 400;
    margin-bottom: 16rpx;
  }
}
</style>
