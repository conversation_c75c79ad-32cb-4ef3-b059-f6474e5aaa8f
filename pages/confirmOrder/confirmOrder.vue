<template>
  <view
    class="pageCommon g-bg"
    style="box-sizing: border-box; padding-bottom: 28rpx"
  >
    <PageNav title="确认订单" background-color="#FFF" theme="black" shadow />

    <view style="height: 24rpx"></view>

    <GamePropsCard  v-if="isVgoodsType" :item="gameDt2"/>
    <OrderCard
      v-else
      v-bind="{
        desc: gameDt.productSubTitle,
        pic: gameDt.productPic,
        price: gameDt.price,
      }"
    >
      <view slot="head-left">商品信息</view>
    </OrderCard>

    <Card v-if="baseBPList.length" style="margin-top: 40rpx;">
      <view slot="title" class="spaceStart">
        <view class="baojia_item_title gradient-primary">基础</view>
        <view>包赔服务（单选，97%已选择）</view>
      </view>
      <BpCard
        v-for="(item, index) in baseBPList"
        :key="item.id"
        v-bind="{
          isAddType: false,
          checked: baojiaIndex == index,
          gamePrice: gameDt.price,
          showExplain: true,
          item,
        }"
        @choose="(str) => chooseBao(index, item, str)"
      />
    </Card>

    <Card v-if="addBPList.length&&faceFlag" style="margin-top: 56rpx">
      <view slot="title" class="spaceStart">
        <view class="baojia_item_title gradient-red">增值</view>
        <view>包赔权益（多选，95%已选择）</view>
      </view>

      <BpCard
        v-for="item in addBPList.filter((item) => comparePrice(item))"
        :key="item.id"
        v-bind="{
          isAddType: true,
          checked: hasAdd(item),
          gamePrice: gameDt.price,
          showExplain: true,
          item,
        }"
        @choose="(str) => chooseBaoAdd(item, str)"
      />
    </Card>

    <Card class="couponCardBox" style="margin-top: 40rpx">
      <VCoupon :product-id="productId" @check="checkCouponItem"></VCoupon>
    </Card>
    <Card v-if="isVgoodsType" title="信息填写" style="margin-top: 40rpx">
      <MyForm
        ref="identifyForm"
        :label-width="88"
        :data-json="formDataJson"
        :has-submit-btn="false"
      >
        <view slot="quantity-item" class="payCountText"><text class="c-primary">{{gameDt.quantity}}</text>件</view> 
      </MyForm>
    </Card>

    <view style="height: 180rpx"></view>

    <!-- 底部栏 -->
    <view class="accountDt_footer">
      <view class="accountDt_footer_agree spaceStart" @click="toggelAgree">
        <view style="height: 28rpx; line-height: 24rpx">
          <IconFont v-show="!checked" :size="14" icon="unchecked" />
        </view>
        <image
          v-show="checked"
          src="../../assets/imgs/agreement_checked.svg"
          mode="heightFix"
          style="width: 14px; height: 14px"
        />
        <view style="margin-left: 4rpx">
          我已阅读并同意
          <span
            style="color: #ffb74a; text-decoration: underline"
            @click.stop="payAgree"
            >买家交易规则</span
          >
        </view>
      </view>
      <view
        class="spaceBetween"
        style="margin-top: 24rpx; color: rgba(0, 0, 0, 0.4); font-size: 24rpx"
      >
        <view v-if="type == 1" class="spaceStart">
          <text>支付定金:</text
          ><text class="payPrice gradient-primary">￥200000</text>
        </view>
        <view v-else class="spaceStart">
          <text>实付款:</text
          ><text class="payPrice gradient-primary">￥{{ totalPrice }}</text>
        </view>
        <view class="spaceStart">
          <view
            class="kk-btn line"
            style="margin-right: 28rpx; width: 180rpx"
            @click="checkDetail"
          >
            <span>订单详情</span>
          </view>
          <view
            class="kk-btn orange"
            style="width: 180rpx"
            @click="isVgoodsType ? submitForm() : payNow()"
            >提交订单</view
          >
        </view>
      </view>
    </view>

    <!-- 支付方式 -->
    <payPopup
      v-if="showPayPopup"
      :order-detail="orderDetail"
      @payNowFun="payNowFun"
      @close="payClose"
    ></payPopup>

    <!-- 订单明细 -->
    <uni-popup ref="popupDetail" type="center" style="z-index: 10">
      <view class="popupDetail">
        <view class="spaceEnd">
          <IconFont
            :size="18"
            icon="close"
            style="margin-right: -66rpx"
            @click="closeDetail"
          />
        </view>
        <view class="tit">订单明细</view>
        <view class="detail">
          <view class="spaceBetween detail_one">
            <view>单价</view>
            <view class="price">¥{{ gameDt.price }}</view>
          </view>
          <view
            v-for="(item, index) in baopeiDetailList"
            :key="index"
            class="spaceBetween detail_one"
          >
            <view>{{ item.value }}</view>
            <view v-if="item.value !== '代金券'" class="price">¥{{ getPrice2(item) }}</view>
            <view v-else class="value">-¥{{ item.price }}</view>
          </view>
          <view class="spaceEnd">
            <view v-if="type !== 1" style="font-size: 32rpx">
              <text class="strong">总价</text
              ><text class="price">¥{{ totalPrice }}</text>
            </view>
            <view v-else style="font-size: 32rpx">
              <text class="strong">支付定金</text
              ><text class="price">¥200000</text>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
    <!-- <view v-if="popupSw" class="dailog_wrap">
      <view class="dailog_wrapBox">
        <view class="dailog_tit">责任须知</view>
        <view class="zeren_needKnow">
          <p>普通包赔：号价10%，账号如发生找回未追回，赔付100%号价。</p>
          <p>
            双倍包赔：号价20%，账号如发生找回未追回，赔付200%号价。因为普通包赔只赔付号款，双倍包赔是针对想对账号进行大额投入的买家（如购买天赏，大额充值，代练代肝）特地推出的，可以保障您后续的心血投入。
          </p>
          <p>如不选择包赔服务，买家请知晓并同意以下风险：</p>
          <p>
            1.账号如发生找回，因未签订合同，涉及个人隐私，平台方无法提供卖家的详细信息，但会指导帮助您该如何维权。
          </p>
          <p>
            2.如有期望在平台二次出售该账号，新买家若有购买包赔意向，账号如发生找回，将由您承担责任。
          </p>
        </view>
        <view class="dialog-footer">
          <view v-if="popupTime > 0" class="dailog_btnB disabled">
            我知道了{{ popupTime > 0 ? '（' + popupTime + '秒）' : '' }}
          </view>
          <view v-else class="dailog_btnB" type="success" @click="popupHide"
            >我知道了</view
          >
        </view>
      </view>
    </view> -->
  </view>
</template>

<script>
import {
  generateKKConfirmOrder,
  generateKKOrder2,
  getOrderSku,
} from '@/config/api/confirmOrder.js';
import { getProductCategory } from '@/config/api/submitAccount.js';
import {
  generateKKConfirmOrderMyAssess,
  generateKKOrderMyAssess2,
} from '@/config/api/myAssess.js';
import { mapState } from 'vuex';
import util from '@/utils/util';
import payPopup from '@/components/popup/PayPopup';
import Card from './components/card.vue';
import BpCard from './components/bpCard.vue';
import OrderCard from '@/components/product/OrderCard.vue';
import GamePropsCard from '@/components/accountList/GamePropsCard.vue';
import VCoupon from '@/components/v-coupon/v-coupon.vue';
export default {
  components: { payPopup, Card, BpCard, OrderCard,GamePropsCard,VCoupon },
  data() {
    return {
      isVgoodsType: false, // 是否道具
      faceFlag:true,
      from: '',
      orderDetail: {},
      showPayPopup: false,
      // pmsSkuStockList: [],
      productId: '',
      baojiaIndex: null, // 保价选择的下标
      // baojiaAddIndex: null,
      // flag_id: '',
      gameDt: {},
      gameDt2: {},

      checked: false, // 支付协议
      totalPrice: 0, // 总价
      ensure: 1, // 是否报价,1保价 0不保价
      ensure_price: 0, // 保价的金额
      ensure_price_add: 0,
      type: 2, // 1 预定 2 全款
      method: 2, // 1 微信支付 2 支付宝支付
      order_id: 1509,
      popupTime: 1,
      popupSw: false,
      showBaopeiIndex: 0,
      showBaopeiItem: '',
      baseBPList: [],
      addBPList: [],
      baojiaAddList: [],
      baopeiDetailList: [],

      formDataJson: [],
      buyerAttr: '',
      quantity:'',
      isbaopeiForce:false,
      selectCouponItem: {},

    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo,
    }),
  },
  onShow() {
    this.$store.dispatch('setUserInfoStore');
  },
  onLoad(e) {
    this.productCategoryId = e.productCategoryId;
    this.quantity = e.quantity;
    if (e.productId) {
      this.productId = e.productId;
      this.getShopDtFun();
    }
    if (e.from === 'myAssess') {
      this.from = e.from;
      this.negoId = e.negoId;
      this.getGenerateKKConfirmOrderMyAssess();
    }
  },
  methods: {
    checkCouponItem(item) {
      this.selectCouponItem = item;
      // this.changeBaoPeiDetailsList();
      this.computePrice();
    },
    closeDetail() {
      this.$refs.popupDetail.close();
    },
    checkDetail() {
      this.baopeiDetailList = [];
      if (this.baojiaIndex || this.baojiaIndex == 0) {
        this.baopeiDetailList.push(this.baseBPList[this.baojiaIndex]);
      }
      if (this.baojiaAddList && this.baojiaAddList.length) {
        this.baopeiDetailList = this.baopeiDetailList.concat(
          this.baojiaAddList,
        );
      }
      if (this.selectCouponItem.id) {
        this.baopeiDetailList.push({
          value: '代金券',
          price: this.selectCouponItem.amount,
        });
      }
      this.$refs.popupDetail.open('bottom');
    },
    getGenerateKKConfirmOrderMyAssess() {
      generateKKConfirmOrderMyAssess({ negoId: this.negoId }).then((res) => {
        if (res.code == 200) {
          this.formatRes(res);
          this.getBaopei();
        }
      });
    },
    payClose() {
      this.showPayPopup = false;
    },
    // 责任须知
    // popupShow() {
    //   this.popupSw = true;
    //   clearInterval(this.popupTimer);
    //   this.popupTimer = setInterval(() => {
    //     if (this.popupTime <= 0) {
    //       clearInterval(this.popupTimer);
    //     } else {
    //       this.popupTime--;
    //     }
    //   }, 1000);
    // },
    popupHide() {
      this.popupTime = 0;
      this.popupSw = false;
      clearInterval(this.popupTimer);
    },
    // 支付协议
    payAgree() {
      uni.navigateTo({
        url: '/pages/noticeDetail/noticeDetail?id=323&from=helper',
      });
    },
    // 支付协议勾选
    toggelAgree(e) {
      this.checked = !this.checked;
    },
    // 支付方式变化
    changePay(e) {
      this.method = e.detail.value;
    },

    submitForm() {
      if (this.isVgoodsType) {
        this.$refs.identifyForm.submit((formData) => {
          this.buyerAttr = this.buyerAttr.map((ele) => {
            return {
              ...ele,
              value: formData[ele.id],
            };
          });
          this.payNow();
        });
      }
    },
    // 创建订单
    payNow() {
      if (!this.checked) {
        uni.showToast({
          title: '请勾选买家交易规则',
          icon: 'none',
        });
        return false;
      }
      if (this.from === 'myAssess') {
        this.payNowMyAssess();
      } else {
        this.payNowNormal();
      }
    },
    payNowMyAssess() {
      uni.showLoading({
        title: '拼命加载中',
      });
      let baopeiTypes = [];
      if (this.baojiaItem) {
        baopeiTypes.push(this.baojiaItem.id);
      }
      this.baojiaAddList.forEach((ele) => baopeiTypes.push(ele.id));
      let data = {
        baopeiTypes: baopeiTypes.join(','),
        buyType: baopeiTypes.length ? 1 : 0,
        negoId: this.negoId,
        quantity:this.quantity,
        // #ifdef APP-PLUS
        sourceType: 1,
        // #endif
        // #ifdef H5
        sourceType: 2,
        // #endif
      };
      generateKKOrderMyAssess2(data).then((res) => {
        uni.hideLoading();
        if (res.code == 200) {
          this.formatOrder(res);
        }
      });
    },
    formatOrder(res) {
      this.order_id = res.data.id;

      this.orderDetail = {
        id: this.order_id,
        payAmount: res.data.payAmount,
      };
      this.showPayPopup = true;
    },
    getPrice2(item) {
      return util.times(this.gameDt.price, item.price);
    },
    comparePrice(item) {
      const result = util.comparedTo(this.gameDt.price, item.lowprice);
      if (result === -1) {
        return false;
      } else {
        return true;
      }
    },
    payNowNormal() {
      uni.showLoading({
        title: '拼命加载中',
      });
      let baopeiTypes = [];
      if (this.baojiaItem) {
        baopeiTypes.push(this.baojiaItem.id);
      }
      this.baojiaAddList.forEach((ele) => baopeiTypes.push(ele.id));

      let data = {
        baopeiTypes,
        buyType: baopeiTypes.length ? 1 : 0,
        productId: this.productId,
        couponCode: this.selectCouponItem.couponCode || undefined,
        buyerAttr: this.isVgoodsType ? JSON.stringify(this.buyerAttr) : undefined,
        quantity:this.quantity,
        // #ifdef APP-PLUS
        sourceType: 1,
        // #endif
        // #ifdef H5
        sourceType: 2,
        // #endif
      };

      generateKKOrder2(data).then((res) => {
        uni.hideLoading();
        if (res.code == 200) {
          this.formatOrder(res);
        }
      });
    },
    // 立即支付
    payNowFun(method, id) {
      this.showPayPopup = false;
      uni.navigateTo({
        url: '/pages/payOrder/payOrder?order_id=' + id + '&method=' + method,
      });
    },
    // 获取商品数据
    getShopDtFun() {
      generateKKConfirmOrder({
        buyType: 0, // 随便传个值
        productId: this.productId,
        quantity:this.quantity
      }).then((res) => {
        if (res.code == 200) {
          const faceObj=res.data.goodsProduct.productAttributeValueList.find(item=>{
            return item.productAttributeName=='人脸包赔'
          })
          if(faceObj&&faceObj.value=='不支持人脸包赔'){
            this.faceFlag=false
          }
          this.formatRes(res);
          this.getBaopei();
        }
      });
    },
    getBaopei() {
      getProductCategory(this.productCategoryId).then((res) => {
        if (res.code == 200) {
          if (res.data.custom) {
            let custom = JSON.parse(res.data.custom);
            this.baseBPList = [];
            this.addBPList = [];
            if(custom.isbaopeiForce){
              this.isbaopeiForce=true
            }
            if (custom.baopei && custom.baopei.length) {
              custom.baopei.forEach((ele) => {
                if (ele.type == 'BASIC_COMPENSATION') {
                  this.baseBPList.push(ele);
                }
                if (ele.type == 'VALUE_ADD_COMPENSATION') {
                  this.addBPList.push(ele);
                }
              });
            }
            let item = this.baseBPList[0];
            if (item) {
              this.chooseBao(0, item);
            } else {
              this.ensure_price_add = 0;
              this.ensure_price = 0;
              this.ensure = 0;
            }
            this.computePrice();

            if (custom.goodsType === 'vgoods')
              getOrderSku(res.data.attriCateId).then((res2) => {
                if (res2.code === 200) {
                  this.formatVgoodsForm(res2.data);
                }
              });
          }
        }
      });
    },
    formatVgoodsForm(data) {
      this.buyerAttr = data;
      this.formDataJson =[{
        name: 'quantity',
        type: 'text',
        placeholder: '请输入',
        label: '购买件数',
      }].concat(data.map((ele) => {
        const custom = JSON.parse(ele.custom || '{}');
        return {
          name: ele.id + '',
          type: 'input',
          placeholder: custom.placeholder || '请输入',
          label: ele.name,
          required: ele.handAddStatus === 1,
          rules:
            ele.handAddStatus === 1
              ? [
                  {
                    required: true,
                    errorMessage: '请输入' + ele.name,
                  },
                ]
              : [],
        };
      }));
      this.isVgoodsType = true;
    },
    formatRes(res) {
      let calcAmount=res.data.calcAmount
      let obj={
        ...res.data.cartPromotionItemList[0],
        price:calcAmount.payAmount
      }
      // this.gameDt = res.data.cartPromotionItemList[0];
      this.gameDt=obj

      // 组装道具产品信息
      const {product,productAttributeValueList=[]} = res.data.goodsProduct||{}
      this.gameDt2={
        ...product,
        attrValueList:productAttributeValueList.map(ele=>({...ele,name:ele.productAttributeName}))
      }
      // 默认第一个
    },
    // 总价计算
    // 总价计算
    computePrice() {
      this.totalPrice = util.add(this.gameDt.price, this.ensure_price || 0);
      this.totalPrice = util.add(this.totalPrice, this.ensure_price_add || 0);
      this.totalPrice = util.add(
        this.totalPrice,
        -this.selectCouponItem.amount || 0,
      );
      if (this.totalPrice > 200000) {
        this.type = 1;
      } else {
        this.type = 2;
      }
    },
    hasAdd(item) {
      const findIt = this.baojiaAddList.find((ele) => ele.id === item.id);
      return !!findIt;
    },

    chooseBaoAdd(item, flag) {
      if (this.baojiaIndex == null) {
        uni.showToast({
          title: '须选择基础包赔后才可加购增值包赔',
          icon: 'none',
        });
        return;
      }
      const findIndex = this.baojiaAddList.findIndex(
        (ele) => ele.id === item.id,
      );
      if (findIndex !== -1) {
        if (flag !== 'pop') {
          this.baojiaAddList.splice(findIndex, 1);
        }
      } else {
        this.baojiaAddList.push(item);
      }
      this.ensure_price_add = 0;

      this.baojiaAddList.forEach((ele) => {
        let ratio = ele.price;
        let price = util.times(this.gameDt.price, ratio);
        this.ensure_price_add = util.add(this.ensure_price_add, price);
      });
      this.computePrice();
    },

    // 保价选择
    chooseBao(num, item, flag) {
      if (this.baojiaIndex == num && flag !== 'pop'&&!this.isbaopeiForce) {
        this.baojiaIndex = null;
        this.baojiaItem = null;
        this.ensure_price = 0;
        this.ensure_price_add = 0;
        this.ensure = 0;
        this.baojiaAddList = [];
      } else {
        this.baojiaItem = item;
        this.baojiaIndex = num;
        let ratio = item.price;
        this.ensure = parseInt(num) + 1;
        this.ensure_price = util.times(this.gameDt.price, ratio);
      }
      this.computePrice();
    },
  },
};
</script>

<style lang="scss" scoped>
// 商品信息
.accountO_pic {
  width: 192rpx;
  height: 170rpx;
  flex-shrink: 0;
  border-radius: 12rpx;
  flex-shrink: 0;
  margin-right: 20rpx;
  overflow: hidden;
}
.accountO_pic > image {
  height: 100%;
}
.accO_tit {
  color: #000;
  font-size: 24rpx;
  font-weight: 500;
}
.accO_price {
  color: #ff720c;
  font-size: 28rpx;
  font-weight: 600;

  margin-top: 36rpx;
  text-align: right;
}
.baojia_item_title {
  font-family: YouSheBiaoTiHei;
  font-size: 32rpx;
  margin-right: 4rpx;
}

// 底部框
.accountDt_footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 90;
  background-color: #fff;
  box-sizing: border-box;
  fill: rgba(255, 255, 255, 0.8);
  box-shadow: (1px 2px 3px rgba(0, 0, 0, 0.05));
  backdrop-filter: blur(20px);

  padding: 20rpx 48rpx 44rpx 48rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.1);

  .accountDt_footer_agree {
    font-size: 20rpx;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.4);
  }

  .payPrice {
    font-family: Inter;
    font-size: 32rpx;
    font-weight: 600;
  }
}

// 定金支付提醒
.dingjin_pay {
  font-size: 26rpx;
  font-weight: 600;
  color: #f7423f;
  line-height: 38rpx;
  text-align: center;
  padding: 40rpx 30rpx 30rpx 40rpx;
}
// 订单明细
.popupDetail {
  background: #fff;
  padding: 26rpx 88rpx 26rpx 50rpx;
  padding-bottom: 260rpx;

  color: #000;
  font-weight: 400;
  font-size: 24rpx;

  .tit {
    font-size: 32rpx;
    margin-bottom: 40rpx;
  }
  .detail {
    .detail_one {
      margin-bottom: 48rpx;
    }
    .price {
      color: rgba(0, 0, 0, 0.6);
    }
    .strong {
      font-size: 32rpx;
      margin-right: 22rpx;
    }
  }
}
.payCountText{
  color: #9a9a9a;
  font-size: 24rpx;
  font-weight: 400;
}
.couponCardBox{
 /deep/ .card-head-box{
    background: #fff!important;
    display: none;
  } 
}
</style>
