<template>
  <view class="pageCommon g-bg" style="padding-bottom: 40rpx">
    <PageNav title="订单详情" background-color="#fff" theme="black"></PageNav>

    <view @click="goProduct(productDetail)">
      <OrderCard
        v-if="orderDetail.accountItem"
        v-bind="{
          orderTypeName: orderDetail.orderTypeName,
          orderSn: orderDetail.orderSn,
          name: orderDetail.productCategoryName,
          desc: orderDetail.accountItem.productName,
          pic: orderDetail.accountItem.productPic,
          gameName: orderDetail.accountItem.productBrand,
          price: productDetail.realAmount,
          hasBtns: false,
        }"
        style="margin-top: 40rpx"
      >
        <view slot="head-right">{{
          ORDER_STATUS_OBJ[orderDetail.orderStatus].label
        }}</view>
      </OrderCard>
    </view>
    <Card class="detail-box">
      <view slot="title" class="title">订单明细</view>
      <view class="spaceBetween" style="margin-bottom: 24rpx"
        ><text>单价</text><text>￥{{ productDetail.realAmount }}</text></view
      >
      <view v-if="productDetail.couponAmount" class="spaceBetween" style="margin-bottom: 24rpx"
        ><text>代金券</text><text>-￥{{ productDetail.couponAmount }}</text></view
      >
      <view
        v-for="item in buyerAttri"
        :key="item.id"
        class="spaceBetween"
        style="margin-bottom: 24rpx"
        ><text>{{ item.name }}</text
        ><text>{{ item.value }}</text></view
      >

      <view
        v-if="bpfwList.length"
        class="spaceBetween"
        style="margin-bottom: 24rpx"
        @click="showBpBox = !showBpBox"
        ><text
          >增值服务
          <IconFont
            :size="12"
            :class="showBpBox ? 'rotate180' : ''"
            icon="arrow-down"
            style="margin-left: 4rpx" /></text
        ><text>￥{{ orderDetail.bpTotalPrice }}</text></view
      >

      <!-- 包赔卡片 -->
      <template v-if="showBpBox">
        <BpCard
          v-for="(item) in bpfwList"
          :key="item.id"
          v-bind="{
            isAddType: item.type==='VALUE_ADD_COMPENSATION',
            checked: true,
            gamePrice: orderDetail.accountItem.productPrice,
            showExplain: false,
            item,
          }"
        />
      </template>

      <view
        class="spaceBetween"
        style="font-size: 24rpx; color: rgba(0, 0, 0, 0.4); margin-top: 24rpx"
        ><text>创建时间：</text
        ><text>{{ orderDetail.createTime | timeformat }}</text></view
      >
      <view
        class="spaceEnd"
        style="margin-top: 96rpx; margin-bottom: 28rpx; font-size: 32rpx"
        >总价 ￥{{ orderDetail.payAmount }}</view
      >
      <view class="btns-box">
        <OrderBtns :order-item="orderDetail" @refresh="goBack" />
      </view>
    </Card>
  </view>
</template>

<script>
import { getOrderDetail } from '@/config/api/confirmOrder.js';
import { getProductCategory } from '@/config/api/submitAccount.js';
import util from '@/utils/util';
import { ORDER_STATUS_OBJ } from '@/utils/const';

import OrderCard from '@/components/product/OrderCard.vue';
import OrderBtns from '@/components/product/OrderBtns.vue';
import Card from './components/card.vue';
import BpCard from './components/bpCard.vue';

export default {
  name: 'OrderDetail',
  components: { OrderCard, Card, BpCard, OrderBtns },
  data() {
    return {
      orderDetail: {},
      order_id: '',
      productDetail: {},
      fieldList: [],
      timer: '',
      showPayPopup: false,
      imUniLoading: false,

      ORDER_STATUS_OBJ,
      bpfwList: [],
      showBpBox: true,

      buyerAttri:[]
    };
  },
  onLoad(e) {
    this.order_id = e.order_id || '';
    this.getOrderDetail();
  },
  methods: {
    goBack() {
      uni.navigateBackCustom();
    },
    getBaopei(productCategoryId) {
      getProductCategory(productCategoryId).then((res) => {
        if (res.code == 200) {
          let bpList=[]
          if (res.data.custom) {
            bpList = JSON.parse(res.data.custom||'{}').baopei||[];
          }

          // 包赔列表数据
          let { orderItemList } = this.orderDetail;
          this.bpfwList = orderItemList
          .filter((ele) => {
            return ele.itemType === 1;
          })
          .map((item) => {
            const { productAttr,productName } = item;
            const attr = JSON.parse(productAttr)[0] || {};
            const obj = bpList.find(ele=>ele.value===productName && ele.type === attr.type)||{}

             // 包赔卡片数据做兼容
            return {
              ...item,
              ...attr,
              ...obj
            };
          });

          // 包赔服务总价
          this.orderDetail.bpTotalPrice = this.bpfwList.reduce((cur, item) => {
            return util.add(cur, item.productPrice || 0);
          }, 0);

          console.log('this.bpfwList',this.bpfwList)
        }
      });
    },
    getOrderDetail() {
      getOrderDetail(this.order_id).then((res) => {
        if (res.code == 200) {
          this.orderDetail = res.data;

          // 包赔服务数据
          this.getBaopei(this.orderDetail.productCategoryId)

          this.buyerAttri = JSON.parse(res.data.buyerAttri || '[]');

          this.fieldList = [];
          let { orderItemList } = this.orderDetail;

          this.productDetail = orderItemList.find((ele) => {
            return ele.itemType === 0;
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.detail-box {
  margin-top: 24rpx;

  color: #000;
  font-size: 28rpx;
  font-weight: 400;

  .title {
    font-family: YouSheBiaoTiHei;
    font-size: 32rpx;
    color: #000;
  }

  .content {
    padding: 40rpx;
  }
}

.btns-box {
  border-top: solid 1px rgba(0, 0, 0, 0.1);
  padding: 20rpx 0;
}

.rotate180 {
  transform: rotate(180deg);
}
</style>
