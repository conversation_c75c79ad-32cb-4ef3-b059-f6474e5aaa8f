<template>
  <view
    style="background-color: #fdf5ed; box-sizing: border-box; min-height: 100vh"
  >
    <PageNav
      title="代金券详情"
      background-color="#fff"
      theme="black"
      shadow
    ></PageNav>
    <view class="coupon-content">
      <VCouponItem
        :is-show-btn="false"
        :item="couponItem"
        class="coupon-item"
      />
      <view class="coupon-content-item">
        <view class="label">代金券名称：</view>
        <view class="value">{{ couponItem.couponName }}</view>
      </view>
      <view class="coupon-content-item">
        <view class="label">金额：</view>
        <view class="value">￥{{ couponItem.amount }}</view>
      </view>
      <view class="coupon-content-item">
        <view class="label">状态：</view>
        <view class="value">
          <text v-if="status === 0">待使用</text>
          <text v-else-if="status === 2">已失效</text>
          <text v-else>已使用</text>
        </view>
      </view>
      <view class="coupon-content-item">
        <view class="label">领取时间：</view>
        <view class="value">{{
          couponItem.createTime | timeformat('YYYY.MM.DD HH:mm:ss')
        }}</view>
      </view>
      <view class="coupon-content-item">
        <view class="label">生效时间：</view>
        <view class="value">{{
          couponItem.startTime | timeformat('YYYY.MM.DD HH:mm:ss')
        }}</view>
      </view>
      <view class="coupon-content-item">
        <view class="label">失效时间：</view>
        <view class="value">{{
          couponItem.endTime | timeformat('YYYY.MM.DD HH:mm:ss')
        }}</view>
      </view>
      <view class="coupon-content-item">
        <view class="label">使用规则：</view>
        <view class="value">单笔订单最多同时使用一张</view>
      </view>
      <view class="coupon-content-item">
        <view class="label">退款规则：</view>
        <view class="value">
          <text v-if="couponItem.returnType === 0">退款不退券</text>
          <text v-else>退款退券</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getCouponList } from '@/config/api/coupon.js';
import VCouponItem from './modules/coupon-item.vue';

export default {
  components: {
    VCouponItem,
  },
  data() {
    return {
      couponId: '',
      status: 0,
      couponItem: {},
    };
  },
  computed: {},
  async onLoad(e) {
    this.couponId = e.id;
    this.status = parseInt(e.status || '0');
    await this.initDate(this.status);
  },
  methods: {
    // 初始化数据
    async initDate(useStatus) {
      await getCouponList({
        useStatus,
      }).then((res) => {
        if (res.code === 200) {
          if (!res.data.length) return;
          this.couponItem = res.data.find((item) => item.id == this.couponId);
        } else {
          uni.showToast({
            title: res.message,
            icon: 'none',
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.coupon-content {
  padding: 20rpx 24rpx;
  .coupon-item {
    margin-bottom: 40rpx;
  }
  .coupon-content-item {
    display: flex;
    align-items: center;
    font-size: 26rpx;
    margin-bottom: 20rpx;
    &:last-of-type {
      margin-bottom: 0;
    }
    .label {
      width: 156rpx;
      color: #9a9a9a;
    }
    .value {
      color: #1f1f1f;
      margin-left: 46rpx;
    }
  }
}
</style>