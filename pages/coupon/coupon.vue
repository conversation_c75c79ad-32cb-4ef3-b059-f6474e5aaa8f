<template>
  <view
    style="background-color: #fdf5ed; box-sizing: border-box; min-height: 100vh"
  >
    <div>
      <PageNav
        title="我的代金券"
        background-color="#fff"
        theme="black"
        shadow
      ></PageNav>
      <view class="tabs">
        <view
          v-for="(item, index) in COUPON_STATUS"
          :key="index"
          :class="{ 'tabs-item-actived': item.value === status }"
          class="tabs-item"
          @click="changeStatus(item)"
          >{{ item.label }}</view
        >
      </view>
      <view class="coupon-content">
        <view v-if="couponList.length">
          <VCouponItem
            v-for="(item, index) in couponList"
            :key="index"
            :item="item"
            @click="toDetails"
            @use="toBuy"
            :status="status==0?'1':'0'"
          ></VCouponItem>
        </view>
        <view style="text-align: center;" v-else>
            暂无相关代金券
        </view>
      </view>
    </div>
  </view>
</template>

<script>
import { getCouponList } from '@/config/api/coupon.js';
import VCouponItem from './modules/coupon-item.vue';

export default {
  components: {
    VCouponItem,
  },
  data() {
    return {
      refresherTriggered: false,
      status: 0,
      couponList: [],
      COUPON_STATUS: [
        {
          value: 0,
          label: '待使用',
        },
        {
          value: 2,
          label: '已失效',
        },
        {
          value: 1,
          label: '已使用',
        },
      ],
    };
  },
  computed: {},
  onShow() {
    this.initDate();
  },
  methods: {
    async scrollRefresh() {
      this.refresherTriggered = true;
      await this.initDate();
      this.refresherTriggered = false;
    },
    // 初始化数据
    async initDate() {
      this.couponList = [];
      await getCouponList({
        useStatus: this.status,
      }).then((res) => {
        if (res.code === 200) {
          this.couponList = res.data;
        } else {
          uni.showToast({
            title: res.message,
            icon: 'none',
          });
        }
      });
    },
    async changeStatus(val) {
      this.status = val.value;
      await this.initDate();
    },
    toBuy() {
      uni.switchTab({
        url: '/pages/tabBar/buyer/buyer',
      });
    },
    toDetails(item) {
      uni.navigateTo({
        url: `/pages/coupon/coupon-details?id=${item.id}&status=${this.status}`,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.tabs {
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: space-around;
  //   border-top: 1px solid #f4f4f4;
  background: #fff;
  .tabs-item {
    font-size: 28rpx;
    color: #9a9a9a;
    position: relative;
  }
  .tabs-item-actived {
    font-size: 32rpx;
    color: #1f1f1f;
    font-weight: 600;
    &::after {
      content: '';
      position: absolute;
      bottom: -10rpx;
      left: 50%;
      transform: translateX(-50%);
      width: 36rpx;
      height: 6rpx;
      background: #ff720c;
      border-radius: 10rpx;
    }
  }
}
.coupon-content {
  padding: 20rpx 24rpx;
}
</style>
