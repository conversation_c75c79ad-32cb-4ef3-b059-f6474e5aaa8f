<template>
  <view
    :class="`coupon-item-${status == 1 ? item.useStatus : 1}`"
    class="coupon-item"
    @click="handleClick(item)"
  >
    <view class="price">
      <text style="font-size: 28rpx">￥</text>
      <text class="text">{{ item.amount }}</text>
    </view>
    <view class="info-box">
      <view class="info">
        <view class="title">{{ item.couponName }}</view>
        <view class="time"
          >有效期：{{ item.startTime | timeformatday('YYYY.MM.DD') }}-{{
            item.endTime | timeformatday('YYYY.MM.DD')
          }}</view
        >
      </view>
      <view
        v-if="isShowBtn && type === 'btn'"
        class="btn"
        @click.stop="toUse(item)"
      >
        <text v-if="item.useStatus === 0">去使用</text>
        <text v-else-if="item.useStatus === 2">已失效</text>
        <text v-else>已使用</text>
      </view>
      <view v-if="type === 'check' && item.useStatus === 0&&status=='1'" class="check-box">
        <!-- <IconFont v-if="selectItem.id === item.id" :size="12" class="icon" icon="remind"/> -->
        <img
          v-if="selectItem.id === item.id"
          class="icon"
          src="@/assets/imgs/coupon/actived.png"
          alt=""
        />
        <img
          v-else
          class="icon"
          src="@/assets/imgs/coupon/no-actived.png"
          alt=""
        />
      </view>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    selectItem: {
      type: Object,
      default: () => {},
    },
    type: {
      type: String,
      default: 'btn',
    },
    isShowBtn: {
      type: Boolean,
      default: true,
    },
    item: {
      type: Object,
      default: () => {},
    },
    status:{
      type: [Number, String],
      default: '1',
    }
  },
  dsta() {
    return {};
  },
  methods: {
    toUse(item) {
      if (this.item.useStatus !== 0||this.status==0) {
        return;
      }
      this.$emit('use', item);
    },
    handleClick(item) {
      if (this.type === 'check' && this.item.useStatus === 0&& this.status==1) {
        this.$emit('check', item);
      } else {
        this.$emit('click', item);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.coupon-item {
  display: flex;
  align-items: center;
  height: 168rpx;
  margin-bottom: 32rpx;
  &:last-child {
    margin-bottom: 0;
  }
  .price {
    width: 180rpx;
    height: 168rpx;
    line-height: 168rpx;
    display: flex;
    align-items: baseline;
    justify-content: center;
    font-size: 48rpx;
    color: #ff5500;
    font-weight: 600;
    border: 2rpx solid #ffe2db;
    box-sizing: border-box;
    background: #fdf4f5;
    border-radius: 16rpx;
    position: relative;
    padding: 0 10rpx;
    .text {
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;
    }

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      right: -6rpx;
      transform: translate(50%, -50%);
      width: 8rpx;
      height: 140rpx;
      background: #fdf4f5;
      border-left: 3rpx dashed #ffccc0;
    }
  }
  .info-box {
    height: 100%;
    flex: 1;
    overflow: hidden;
    display: flex;
    align-items: center;
    padding: 0 20rpx 0 16rpx;
    border: 2rpx solid #ffe2db;
    box-sizing: border-box;
    background: #fdf4f5;
    border-radius: 16rpx;
    .info {
      flex: 1;
      overflow: hidden;
      margin-right: 10rpx;
      .title {
        font-size: 28rpx;
        color: #1f1f1f;
        font-weight: 600;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        white-space: nowrap;
      }
      .time {
        font-size: 24rpx;
        color: #9a9a9a;
        margin-top: 12rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        white-space: nowrap;
      }
    }
    .btn {
      width: 112rpx;
      height: 56rpx;
      border-radius: 12rpx;
      font-size: 24rpx;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .check-box {
      .icon {
        display: block;
        width: 36rpx;
        height: 36rpx;
      }
    }
  }
}
.coupon-item-0 {
  .btn {
    color: #fff;
    background: #ff6948;
  }
}
.coupon-item-1,
.coupon-item-2 {
  .price {
    color: #9a9a9a;
    background: #f4f5f7;
    border-color: #e9e9e9;
    &::after {
      border-left-color: #cdcfce;
      background: #f4f5f7;
    }
  }
  .info-box {
    background: #f4f5f7;
    border-color: #e9e9e9;
    .info {
      .title {
        color: #9a9a9a;
      }
    }
    .btn {
      color: #9a9a9a;
    }
  }
}
</style>
