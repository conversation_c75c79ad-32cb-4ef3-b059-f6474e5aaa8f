<template>
  <view :style="boxStyle" class="down_wrap">
    <image
      class="logo-pic"
      src="../../assets/imgs/logo-text2.svg"
      mode="widthFix"
    ></image>

    <!-- 背景板 -->
    <image
      class="down_pic_bg"
      src="../../assets/imgs/download_phone.png"
      mode="widthFix"
    ></image>


    <!-- 底部固定 -->
    <view class="down_bottom">
      <view class="pre">
        <image
          class="down_pic_logo"
          src="../../assets/imgs/home_download_text.png"
          mode="widthFix"
        ></image>
        <view class="solgan-txt">{{SOLGAN_TXT}}</view>
      </view>


      <view class="kk-btn primary download-btn" @click="downLoadApp"
        ><IconFont :size="22" icon="anzhuo" style="margin-right:24rpx"/> Android下载</view
      >

      <view> 易游盾V1.1.1 权限说明丨隐私政策 | 功能个绍 </view>
      <view> 开发者：杭州易游盾网络科技有限公司 </view>
    </view>

    <!-- 微信提示 -->
    <view v-if="isWeixin" class="wechatGuide">
      <image
        class="guideImg"
        src="../../assets/imgs/wechatGuide.jpg"
        mode="widthFix"
      />
    </view>
  </view>
</template>

<script>
import { getVerionInfo2 } from '@/config/api/versionInfo.js';
import {SOLGAN_TXT} from '@/utils/const'

export default {
  data() {
    return {
      SOLGAN_TXT,
      
      urlApp: '',
      boxStyle: '',

      isWeixin: false,
    };
  },
  mounted() {
    var u = navigator.userAgent;
    // IOS
    var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);

    if (isiOS) {
      uni.redirectTo({ url: '/pages/iosWebAppGuide/iosWebAppGuide' });
      return;
    }

     // #ifdef H5
    const ua = navigator.userAgent;
    this.isWeixin = /MicroMessenger\/[\d.]+/.test(ua);
    // #endif


    this.initVersion();

    // uni.onWindowResize((res) => {
    //   this.resize();
    // });
    // this.resize();
  },
  methods: {
    resize() {
      uni.getSystemInfo({
        success: (info) => {
          // 获取当前屏幕的宽度
          var windowWidth = info.windowWidth;
          // 获取当前屏幕的高度
          var windowHeight = info.windowHeight;
          // 当前一屏的高度可以通过屏幕高度和屏幕宽度的比例来计算
          var screenHeight = (windowHeight / windowWidth) * info.screenWidth;

          console.log('当前屏幕的高度：' + screenHeight);
          this.boxStyle = `height:${screenHeight}px;`;
        },
      });
    },
    initVersion() {
      let action = 'update';
      let version;
      if (this.$route.query.action) {
        action = this.$route.query.action;
      }
      if (this.$route.query.version) {
        version = this.$route.query.version;
      }
      getVerionInfo2({
        version,
        action,
      }).then((res) => {
        if (res.code == 200) {
          const { data = {} } = res;
          if (action == 'back') {
            this.urlApp = data.back_verion_url;
          } else {
            this.urlApp = data.latest_verion_url;
          }
        }
      });
    },
    downLoadApp() {
      window.open(this.urlApp);
    },
  },
};
</script>

<style lang="scss" scoped>
.down_wrap {
  width: 100%;
  height: 100vh;
  background: url('../../assets/imgs/download_bg.png') center top no-repeat
    #fff2e6;
  position: relative;
  box-sizing: border-box;
  padding-top: 28rpx;
  background-size: cover;

  color: #969696;
  font-size: 20rpx;
  font-weight: 400;
  line-height: 32rpx;
  letter-spacing: 0.2px;

  .logo-pic {
    width: 242rpx;
    margin: 0 auto;
    display: block;
  }

  .down_pic_bg {
    width: 80%;
    margin: 0 auto;
    display: block;
    margin-top: 70rpx;
  }
}

.down_bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 2;

  padding: 66rpx 130rpx 22rpx;
  text-align: center;
  flex-shrink: 0;

  border-top-left-radius: 80rpx;
  border-top-right-radius: 80rpx;

  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.95) 42.58%,
    rgba(255, 255, 255, 0.76) 84.57%
  );
  stroke-width: 2px;
  stroke: #fff;
  box-shadow:(10px 10px 50px rgba(255, 107, 0, 0.1));
  backdrop-filter: blur(50px);

  .down_pic_logo {
    width: 100%;
    margin: 0 auto;
    display: block;
  }

  .download-btn {
    font-size: 28rpx;
    font-weight: 500;
    // letter-spacing: 0.28px;
    margin: 24rpx auto 30rpx;
  }
}

.solgan-txt {
  color: #969696;
  text-align: center;
  font-family: 'PingFang SC';
  font-size:24rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 0%; /* 0px */
  letter-spacing: 1.32px;

  position: absolute;
  left: 140rpx;
  top: 92rpx;
}

.wechatGuide {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index:99;
  background-color: rgba(0, 0, 0, 0.7);
  .guideImg {
    position: fixed;
    right: 0;
  }
}
</style>
