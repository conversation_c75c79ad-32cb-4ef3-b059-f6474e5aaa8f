<template>
  <view
    style="background-color: #fdf5ed; box-sizing: border-box; min-height: 100vh"
  >
    <PageNav title="足迹" background-color="#fff" theme="black" shadow>
      <view slot="right" class="clearFoot" @click="clearFoot">清空足迹</view>
    </PageNav>
    <!-- <SearchMenu :list-data="PRODUCT_STATUS" @change="statusSearch" /> -->

    <view style="margin-top: 24rpx" class="pageCommon">
      <view v-if="collectList.length > 0">
        <view v-for="item in collectList" :key="item.id">
          <OrderCard
            v-bind="{
              time: item.createTime,
              name: item.productCategoryName,
              desc: item.productName,
              pic: item.productPic,
              price: item.newProductPrice,
              historyPrice:
                item.productPrice !== item.newProductPrice
                  ? item.productPrice
                  : 0,
            }"
            @toPage="accountDetail(item)"
          >
            <view
              slot="head-right"
              :class="(PRODUCT_STATUS_OBJ[item.productStatus] || {}).class"
              >{{ getProductStatus(item) }}</view
            >
          </OrderCard>
        </view>

        <view>
          <uniLoadMore :status="status" icon-type="snow" color="#666666" />
        </view>
      </view>

      <KKEmpty v-else style="margin:40rpx" />

      <!-- 二次询问弹窗 -->
      <TipPanel
        v-if="showConfirmPop"
        @cancel="showConfirmPop = false"
        @confirm="active"
      >
        <view class="second-tip-panel-txt"
          >您确认是否要清空？确认后不可撤销。
        </view>
      </TipPanel>
    </view>
  </view>
</template>

<script>
import uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue';
import {
  getReadHistoryList,
  readHistoryDelete,
  readHistoryClear,
} from '@/config/api/accountDetail.js';
import { mapState } from 'vuex';
import { PRODUCT_STATUS, PRODUCT_STATUS_OBJ } from '@/utils/const';
import OrderCard from '@/components/product/OrderCard.vue'

export default {
  components: {
    uniLoadMore,
    OrderCard,
  },
  data() {
    return {
      status: 'more',
      page: 1,
      totalPage: 1,
      collectList: [],
      PRODUCT_STATUS_OBJ,
      PRODUCT_STATUS,
      showConfirmPop: false,
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo,
    }),
  },
  onReachBottom() {
    if (this.page < this.totalPage) {
      this.status = 'loading';
      this.page++;
      this.getCollectList('add');
    } else {
      this.status = 'loading';
      setTimeout(() => {
        this.status = 'noMore';
        // uni.showToast({
        //   title: '已全部加载完毕',
        //   icon: 'none',
        // });
      }, 1000);
    }
  },
  // 下拉刷新监听
  onPullDownRefresh() {
    setTimeout(() => {
      uni.stopPullDownRefresh();
    }, 1000);
    this.page = 1;
    this.getCollectList();
  },
  onShow() {
    this.$store.dispatch('setUserInfoStore');
    this.refresh();
  },
  methods: {
    getProductStatus(item) {
      if (item.productStatus == 1 || item.productStatus == -1) {
        return item.productStatusTxt;
      } else {
        return '已售';
      }
    },
    statusSearch(status) {
      this.status = status;
      this.refresh();
    },
    refresh() {
      this.page = 1;
      this.totalPage = 1;
      this.getCollectList();
    },
    // 清空足迹
    clearFoot() {
      this.showConfirmPop = true;
      this.ativeFlag = 'clear';
    },
    // 删除单个足迹
    deletCollect(item) {
      this.showConfirmPop = true;
      this.ativeFlag = 'del';
      this.activeItem = item
    },
    active() {
      if (this.ativeFlag === 'clear') {
        readHistoryClear()
          .then((res) => {
            if (res.code == 200) {
              this.refresh();
            }
          })
          .finally(() => {
            this.showConfirmPop = false;
          });
      } else if (this.ativeFlag === 'del') {
        uni.showLoading({
          title: '拼命加载中',
        });
        readHistoryDelete({
          ids: [this.activeItem.id],
        })
          .then((res) => {
            if (res.code == 200) {
              uni.showToast({
                title: '删除成功！',
                icon: 'none',
              });
              this.page = 1;
              this.getCollectList();
            }
          })
          .finally(() => {
            this.showConfirmPop = false;
            uni.hideLoading();
          });
      }
    },
    // 初始化列表
    getCollectList(str) {
      uni.showLoading({
        title: '拼命加载中',
      });
      getReadHistoryList({
        pageSize: 10,
        pageNumber: this.page,
        status: this.status,
      })
        .then((res) => {
          if (res.code == 200) {
            this.totalPage = res.data.totalPage;
            if (str == 'add') {
              this.collectList = this.collectList.concat(res.data.list);
            } else {
              this.collectList = res.data.list;
            }
            if (this.page == this.totalPage) {
              this.status = 'noMore';
            }
          }
        })
        .finally(() => {
          uni.hideLoading();
        });
    },
    // 商品详情
    accountDetail(date) {
      uni.navigateTo({
        url: '/pages/accountDetail/accountDetail?productId=' + date.productId,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.clearFoot {
  font-size: 22rpx;
  color: #969696;
  padding: 40rpx 0;
}
</style>
