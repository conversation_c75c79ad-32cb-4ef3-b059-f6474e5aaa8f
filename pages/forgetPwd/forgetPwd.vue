<template>
  <view class="pageCommon">
    <PageNav v-if="isEditPage" title="修改登录密码" background-color="#fff" theme="black" />
    <PageNav v-else title="忘记密码" background-color="#FDF5ED" />
    <view style="padding-top: 40rpx">
      <view v-if="!isEditPage" >
        <image
          src="../../assets/imgs/logo_banner.svg"
          mode="widthFix"
          style="width: 100%;margin-bottom:40rpx"
        />
      </view>
      <view style="background: #fff" class="form_wrap">
      <view v-if="isEditPage" class="form-title">修改密码</view>
        <MyForm
          :label-width="88"
          :data-json="codeFormDataJson"
          submit-text="修改密码"
          @submit="loginNow"
          @sendCode="sendCode"
        >
          <LoginAgreement slot="agreement" ref="agreement" :agree.sync="checkAgree"  style="margin-top:80rpx"/>
        </MyForm>
        <view v-if="errorText" class="error_msg">
          {{ errorText }}
        </view>
      </view>
      <view
        style="
          color: rgb(255, 183, 74);
          font-size: 24rpx;
          text-align: center;
          margin-top: 20rpx;
        "
        >如登录遇到问题，请联系官方<a
          href="https://work.weixin.qq.com/kfid/kfce65b2a89d0b1efb7"
          target="_black"
          style="text-decoration: underline; color: rgb(255, 183, 74)"
          >微信客服</a
        ></view
      >
    </view>
  </view>
</template>

<script>
import { updatePwdApi } from '@/config/api/forget.js';
import InitCaptcha from '@/common/mixins/initCaptcha';
import LoginAgreement from '@/components/toolbar/LoginAgreement.vue';

export default {
  components:{LoginAgreement},
  mixins: [InitCaptcha],
  data() {
    return {
      isEditPage:false,

      checkAgree: false,
      errorText:'',

      codeFormDataJson: [
        {
          name: 'telephone',
          type: 'input',
          placeholder: '请输入手机号码',
          label: '手机号码',
          required: true,
          rules: [
            {
              required: true,
              errorMessage: '请输入手机号',
            },
            {
              pattern: /^1[3456789]\d{9}$/,
              errorMessage: '请输入正确的手机号码',
            },
          ],
        },
        {
          name: 'authCode',
          type: 'input',
          placeholder: '请输入验证码',
          label: '短信验证码',
          hasCode: true,
          phoneField: 'telephone',
          required: true,
          itemProps: { maxlength: 6 },
          rules: [
            {
              required: true,
              errorMessage: '请输入验证码',
            },
          ],
        },
        {
          name: 'password',
          type: 'input',
          placeholder: '请输入密码',
          label: '密码',
          required: true,
          itemProps: { type: 'password' },
          rules: [
            {
              required: true,
              errorMessage: '请输入密码',
            },
          ],
        },
        {
          name: 'cpassword',
          type: 'input',
          placeholder: '请输入验证码',
          label: '确认密码',
          itemProps: { type: 'password' },
          required: true,
          rules: [
            {
              required: true,
              errorMessage: '请输入确认密码',
            },
            {
              validateFunction: (rule, value, data) => data.password === value,
              errorMessage: '两次密码不一致',
            },
          ],
        },
      ],
    };
  },
  mounted() {},
  onLoad(e){
    if(e.isEdit){
      this.isEditPage = true
    }

  },
  methods: {
    // 修改密码
    loginNow(formData) {
      this.errorText = ''

      if (!this.checkAgree) {
        this.$refs.agreement.open()
        // this.errorText = '请阅读并同意登录注册协议';
        return false;
      }

      uni.showLoading({
        title: '拼命加载中',
      });
      updatePwdApi({
        ...formData,
        username: formData.telephone,
      }).then((res) => {
        if (res.code == 200) {
          uni.showToast({
            title: '密码修改成功！',
            icon: 'none',
          });
          // uni.removeStorageSync('token');
          // uni.removeStorageSync('yximtoken');
          // uni.$UIKitNIM && uni.$UIKitNIM.destroy();
          // Vue.prototype.$store.dispatch('setImUnreadCount', 0);
          setTimeout(() => {
            uni.navigateBackCustom();
          }, 500);
        }else{
          this.errorText = res.message
        }
        uni.hideLoading();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.form_wrap {
  background: #fff;
  border-radius: 60rpx;
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  padding: 40rpx;

  .form-title{
    color: var(--Black, #1B1B1B);
    font-family: YouSheBiaoTiHei;
    font-size: 36rpx;
    font-weight: 400;
    line-height: 24px; /* 133.333% */
    letter-spacing: 0.36px;
    margin-bottom: 32rpx;
  }
}
.error_msg {
  color: #ff002e;
  text-align: center;
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.24px;
  padding: 8rpx 0;
}
</style>
