<template>
  <view :class="isMobile() ? '' : 'pc'" class="done-inner">
    <view class="done-tips">
      <img src="../../static/old/done.png" class="done" /> 提交成功
    </view>
    <view class="my-order" @click="goOrder">点击查看订单信息</view>
    <view class="chat" @click="goChat"></view>
  </view>
</template>
<script>
import isIMToken from '@/common/mixins/isIMToken';
import isLogin from '@/common/mixins/isLogin';
import { m2kfTalk, getMemberHisKFList } from '@/config/api/kf.js';
export default {
  name: 'DoneForm',
  components: {},
  props: {},
  data() {
    return {
      orderNo: '',
      imUniLoading: false,
    };
  },
  onLoad(e) {
    this.orderNo = e.orderNo || '';
  },
  methods: {
    isMobile() {
      var sw = false;
      let systemInfo = uni.getSystemInfoSync();
      let platform = systemInfo.platform;

      if (platform === 'ios') {
        sw = true;
        // 执行 iOS 相关的代码
      } else if (platform === 'android') {
        sw = true;
        // 执行 Android 相关的代码
      }

      return sw;
    },
    goChat() {
      if (!this.imUniLoading) {
        this.imUniLoading = true;
        uni.showLoading({
          title: '加载中',
          mask: true,
        });
      } else {
        return;
      }
      Promise.all([getMemberHisKFList()])
        .then((values) => {
          const res = values[0];
          if (res.code == 200) {
            const findKf = res.data;
            if (findKf) {
              const imcode = findKf;
              let sessionId = `p2p-${imcode}`;
              // m2kfTalk({
              //   orderSn: this.orderNo,
              //   kfIM: imcode,
              // });
              if (uni.$UIKitStore.sessionStore.sessions.get(sessionId)) {
                uni.$UIKitStore.uiStore.selectSession(sessionId);
              } else {
                uni.$UIKitStore.sessionStore.insertSessionActive('p2p', imcode);
              }
              uni.navigateTo({
                url: `/pages/NEUIKit/pages/Chat/index?&sessionId=${sessionId}`,
              });
            } else {
              uni.switchTab({ url: '/pages/tabBar/news/news' });
            }
          }
        })
        .finally(() => {
          uni.hideLoading();
          setTimeout(() => {
            this.imUniLoading = false;
          }, 34);
        });
    },
    goOrder() {
      uni.navigateTo({
        url: '/pages/myPost/myPost',
      });
    },
  },
};
</script>
<style scoped>
.chat {
  cursor: pointer;
  position: fixed;
  bottom: 100rpx;
  right: 10rpx;
  width: 100rpx;
  height: 100rpx;
  background: url(../../static/old/h1.png) no-repeat right top;
  background-size: cover;
  border-radius: 50rpx;
}
.done-inner {
  padding: 60rpx;
  min-height: 800rpx;
  text-align: center;
  font-size: 52rpx;
}

.done-inner .done-tips {
  margin: 200rpx auto 60rpx auto;
}

.done-inner .done-tips .done {
  display: inline-block;
  vertical-align: middle;
}

.my-order {
  margin-bottom: 120rpx;
  display: block;
  font-size: 36rpx;
}
.pc .chat {
  right: 400rpx;
  bottom: 400rpx;
  height: 200rpx;
  width: 200rpx;
}
</style>
