<template>
  <view :class="mobileSw ? '' : 'pc'" class="form-inner">
    <view class="createPost-container">
      <view v-if="begin" ref="postForm" class="form-container">
        <view v-loading="loading" class="box-card">
          <view v-if="orderbankLink">
            <view class="title2">
              卖家{{ seller_name ? '「' + seller_name + '」' : '' }}收款信息填写
            </view>
            <view class="line">
              <view class="title spaceStart">
                <view class="label"
                  ><view class="required_red">*</view>卖家姓名</view
                >
                <input
                  v-model="postForm.account_name"
                  class="uni-input submit_ipt"
                  maxlength="100"
                  placeholder="请输入银行卡账户名"
                />
              </view>
              <view class="title spaceStart">
                <view class="label">
                  <view class="required_red">*</view>出售账号
                </view>
                <input
                  v-model="postForm.account_info"
                  class="uni-input submit_ipt"
                  maxlength="100"
                  placeholder="请输入出售账号"
                />
              </view>
            </view>

            <view class="line">
              <view class="title spaceStart">
                <view class="label">
                  <view class="required_red">*</view>联系电话
                </view>
                <input
                  v-model="postForm.phone"
                  class="uni-input submit_ipt"
                  maxlength="11"
                  placeholder="请输入联系电话"
                />
              </view>
              <view class="title spaceStart">
                <view class="label">
                  <view v-if="isRequire('wx_account')" class="required_red"
                    >*</view
                  >微信账号
                </view>
                <input
                  v-model="postForm.wx_account"
                  class="uni-input submit_ipt"
                  maxlength="100"
                  placeholder="请输入微信账号"
                />
              </view>
            </view>

            <view class="line">
              <view class="title spaceStart">
                <view class="label">
                  <view v-if="isRequire('relation_name')" class="required_red"
                    >*</view
                  >亲属姓名
                </view>
                <input
                  v-model="postForm.relation_name"
                  class="uni-input submit_ipt"
                  maxlength="100"
                  placeholder="请输入亲属姓名"
                />
              </view>
              <view class="title spaceStart">
                <view class="label">
                  <view v-if="isRequire('relationship')" class="required_red"
                    >*</view
                  >亲属关系
                </view>
                <input
                  v-model="postForm.relationship"
                  class="uni-input submit_ipt"
                  maxlength="100"
                  placeholder="请输入亲属姓名"
                />
              </view>
            </view>

            <view class="line">
              <view class="title spaceStart">
                <view class="label">
                  <view v-if="isRequire('relation_phone')" class="required_red"
                    >*</view
                  >亲属手机
                </view>
                <input
                  v-model="postForm.relation_phone"
                  class="uni-input submit_ipt"
                  maxlength="100"
                  placeholder="请输入亲属手机号码"
                />
              </view>
              <view class="title spaceStart">
                <view class="label">
                  <view class="required_red">*</view>银行名称
                </view>
                <input
                  v-model="postForm.bankname"
                  class="uni-input submit_ipt"
                  maxlength="100"
                  placeholder="请输入银行名称"
                />
              </view>
            </view>

            <view class="line">
              <view class="title spaceStart">
                <view class="label">
                  <view class="required_red">*</view>银行卡号
                </view>
                <input
                  v-model="postForm.account_no"
                  class="uni-input submit_ipt"
                  maxlength="50"
                  placeholder="请输入银行卡号"
                />
              </view>
            </view>

            <view class="line">
              <view :class="ensure === 1 ? 'is-required' : ''" class="idcard">
                <view class="title3 spaceStart">
                  <view class="label">
                    <view class="required_red">*</view>身份证正面
                  </view>
                  <view class="picUpload_wrap">
                    <upLoadSingle
                      :need-water="false"
                      class="upLoadSingle"
                      name-key="idcard_pic_top"
                      @upSuccsessSingle="picUpLoadSuc"
                    />
                    <view class="el-icon-plus cover-uploader-icon"
                      >添加身份证正面</view
                    >
                  </view>
                </view>
                <img
                  v-if="idcard_pic_top"
                  :src="idcard_pic_top"
                  class="idcard_pic"
                  fit="cover"
                />
              </view>

              <view :class="ensure === 1 ? 'is-required' : ''" class="idcard">
                <view class="title3 spaceStart">
                  <view class="label">
                    <view class="required_red">*</view>身份证反面
                  </view>
                  <view class="picUpload_wrap">
                    <upLoadSingle
                      :need-water="false"
                      class="upLoadSingle"
                      name-key="idcard_pic_bottom"
                      @upSuccsessSingle="picUpLoadSuc"
                    />
                    <view class="el-icon-plus cover-uploader-icon"
                      >添加身份证反面</view
                    >
                  </view>
                </view>
                <img
                  v-if="idcard_pic_bottom"
                  :src="idcard_pic_bottom"
                  class="idcard_pic"
                  fit="cover"
                />
              </view>
            </view>

            <view :class="ensure === 1 ? 'is-required' : ''">
              <view class="title3 spaceStart">
                <view class="label">
                  <view class="required_red">*</view>外卖地址截图
                </view>

                <view class="picUpload_wrap">
                  <upLoadSingle
                    :need-water="false"
                    class="upLoadSingle"
                    name-key="address_pic"
                    @upSuccsessSingle="picUpLoadSuc"
                  />
                  <view class="el-icon-plus cover-uploader-icon"
                    >添加外卖地址截图</view
                  >
                </view>
              </view>
              <img
                v-if="address_pic"
                :src="address_pic"
                class="address_pic"
                fit="cover"
              />
            </view>
          </view>
          <view class="chat" @click="goChat"></view>
          <view>
            <uni-button
              v-if="orderbankLink"
              type="primary"
              style="width: 400rpx; margin: 20rpx auto"
              @click="submitForm('postForm')"
              >确认提交</uni-button
            >
            <view v-else style="text-align: center; line-height: 80rpx"
              >链接已过期，请联系客服</view
            >
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import upLoadSingle from '@/components/imgUpload/upLoadSingle.vue';
import isIMToken from '@/common/mixins/isIMToken';
import isLogin from '@/common/mixins/isLogin';
import {
  getSellerForminfo,
  savaSellerForm,
  m2kfTalk,
  getMemberHisKFList,
} from '@/config/api/kf.js';

const defaultForm = {
  account_name: '',
  account_info: '',
  phone: '',
  wx_account: '',
  relation_name: '',
  relationship: '',
  relation_phone: '',
  bankname: '',
  account_no: '',
};

export default {
  name: 'UserForm',
  components: {
    upLoadSingle,
  },
  props: {},
  data() {
    return {
      orderbankLink: true,
      iscover: 1,
      seller_name: '',
      token: '',
      ensure: '',
      begin: false,
      upLoadUrl: '',
      postForm: Object.assign({}, defaultForm),
      loading: false,
      idcard_pic_top: '',
      idcard_pic_bottom: '',
      address_pic: '',
      mobileSw: false,
      rules: {},
      imUniLoading: false,
    };
  },
  onShow() {
    this.token = decodeURIComponent(this.$route.query.token || '');
    if (this.token) {
      this.orderBankInfo();
    }

    this.postForm = Object.assign({}, defaultForm);
    // this.initOssConfig();
    if (this.isMobile()) {
      this.mobileSw = true;
    }
  },
  methods: {
    goChat() {
      if (!isLogin()) {
        uni.navigateTo({
          url: '/pages/login/login?isBack=1',
        });
        return;
      }
      if (!this.imUniLoading) {
        this.imUniLoading = true;
        uni.showLoading({
          title: '加载中',
          mask: true,
        });
      } else {
        return;
      }
      Promise.all([getMemberHisKFList()])
        .then((values) => {
          const res = values[0];
          if (res.code == 200) {
            const findKf = res.data;
            if (findKf) {
              const imcode = findKf;
              let sessionId = `p2p-${imcode}`;
              // m2kfTalk({
              //   orderSn: this.orderNo,
              //   kfIM: imcode,
              // });
              if (uni.$UIKitStore.sessionStore.sessions.get(sessionId)) {
                uni.$UIKitStore.uiStore.selectSession(sessionId);
              } else {
                uni.$UIKitStore.sessionStore.insertSessionActive('p2p', imcode);
              }
              uni.navigateTo({
                url: `/pages/NEUIKit/pages/Chat/index?&sessionId=${sessionId}`,
              });
            } else {
              uni.switchTab({ url: '/pages/tabBar/news/news' });
            }
          }
        })
        .finally(() => {
          uni.hideLoading();
          setTimeout(() => {
            this.imUniLoading = false;
          }, 34);
        });
    },
    checkForm() {
      return new Promise((resolve, reject) => {
        let flag = true;
        Object.keys(this.rules).forEach((key) => {
          const ele = this.rules[key][0];
          if (ele.required) {
            if (!this.postForm[key] && this.postForm[key] !== 0) {
              alert(ele.message);
              flag = false;
            }
          }
        });
        flag ? resolve(flag) : reject(flag);
      });
    },
    submitForm() {
      this.checkForm().then((valid) => {
        if (this.ensure === 1) {
          if (
            valid &&
            this.idcard_pic_top &&
            this.idcard_pic_bottom &&
            this.address_pic
          ) {
            this.submitData();
          } else {
            alert('请填写完整');
            return false;
          }
        } else {
          if (valid) {
            this.submitData();
          } else {
            alert('请填写完整');
            return false;
          }
        }
      });
    },

    submitData() {
      this.loading = true;
      let data = {};
      data.accountName = this.postForm.account_name;
      data.accountInfo = this.postForm.account_info;
      data.phone = this.postForm.phone;
      data.wxAccount = this.postForm.wx_account;
      data.relationName = this.postForm.relation_name;
      data.relationship = this.postForm.relationship;
      data.relationPhone = this.postForm.relation_phone;
      data.bankname = this.postForm.bankname;
      data.accountNo = this.postForm.account_no;
      data.idcardPicTop = this.idcard_pic_top;
      data.idcardPicBottom = this.idcard_pic_bottom;
      data.addressPic = this.address_pic;
      //   data.sellerName = this.seller_name;
      data.token = this.token;
      data.orderNo = this.orderNo;
      data.userId = this.userId;
      data.iscover = this.iscover;
      data.ensure = this.ensure;
      savaSellerForm(data).then((res) => {
        if (res.code === 200) {
          this.loading = false;
          uni.navigateTo({
            url: '/pages/form/done?orderNo=' + this.orderNo,
          });
        }
      });
    },
    picUpLoadSuc(url, key) {
      this[key] = url;
    },

    orderBankInfo() {
      getSellerForminfo(this.token).then((res) => {
        if (res.code === 200) {
          const { userName, userPhone, ensure, iscover, userId, orderNo } =
            res.data;
          this.seller_name = `${userName}(${userPhone})`;
          this.ensure = ensure;
          this.iscover = iscover;
          this.userId = userId;
          this.orderNo = orderNo;
          if (this.ensure === 1) {
            this.rules = {
              account_name: [
                {
                  required: true,
                  message: '请输入银行卡账户名',
                  trigger: 'blur',
                },
              ],
              account_info: [
                {
                  required: true,
                  message: '请输入出售账号',
                  trigger: 'blur',
                },
              ],
              phone: [
                {
                  required: true,
                  message: '请输入联系电话',
                  trigger: 'blur',
                },
              ],
              wx_account: [
                {
                  required: true,
                  message: '请输入微信账号',
                  trigger: 'blur',
                },
              ],
              relation_name: [
                {
                  required: true,
                  message: '请输入亲属姓名',
                  trigger: 'blur',
                },
              ],
              relationship: [
                {
                  required: true,
                  message: '请输入亲属关系',
                  trigger: 'blur',
                },
              ],
              relation_phone: [
                {
                  required: true,
                  message: '请输入亲属电话号码',
                  trigger: 'blur',
                },
              ],
              bankname: [
                {
                  required: true,
                  message: '请输入银行名称',
                  trigger: 'blur',
                },
              ],
              account_no: [
                {
                  required: true,
                  message: '请输入银行卡号',
                  trigger: 'blur',
                },
              ],
            };
          } else {
            this.rules = {
              account_name: [
                {
                  required: true,
                  message: '请输入银行卡账户名',
                  trigger: 'blur',
                },
              ],
              account_info: [
                {
                  required: true,
                  message: '请输入出售账号',
                  trigger: 'blur',
                },
              ],
              phone: [
                {
                  required: true,
                  message: '请输入联系电话',
                  trigger: 'blur',
                },
              ],
              wx_account: [
                {
                  required: false,
                  message: '请输入微信账号',
                  trigger: 'blur',
                },
              ],
              relation_name: [
                {
                  required: false,
                  message: '请输入亲属姓名',
                  trigger: 'blur',
                },
              ],
              relationship: [
                {
                  required: false,
                  message: '请输入亲属关系',
                  trigger: 'blur',
                },
              ],
              bankname: [
                {
                  required: true,
                  message: '请输入银行名称',
                  trigger: 'blur',
                },
              ],
              account_no: [
                {
                  required: true,
                  message: '请输入银行卡号',
                  trigger: 'blur',
                },
              ],
            };
          }
          setTimeout(() => {
            this.begin = true;
          }, 300);
          const data = res.data;
          if (data.id) {
            this.idcard_pic_top = data.idcardPicTop;
            this.idcard_pic_bottom = data.idcardPicBottom;
            this.address_pic = data.addressPic;
            this.postForm.account_name = data.accountName;
            this.postForm.account_info = data.accountInfo;
            this.postForm.phone = data.phone;
            this.postForm.wx_account = data.wxAccount;
            this.postForm.relation_name = data.relationName;
            this.postForm.relationship = data.relationship;
            this.postForm.relation_phone = data.relationPhone;
            this.postForm.bankname = data.bankname;
            this.postForm.account_no = data.accountNo;
          }
        } else {
          this.begin = true;
          this.orderbankLink = false;
        }
      });
    },
    isRequire(key) {
      return this.rules[key] && this.rules[key][0].required;
    },

    isMobile() {
      var sw = false;
      let systemInfo = uni.getSystemInfoSync();
      let platform = systemInfo.platform;

      if (platform === 'ios') {
        sw = true;
        // 执行 iOS 相关的代码
      } else if (platform === 'android') {
        sw = true;
        // 执行 Android 相关的代码
      }

      return sw;
    },
  },
};
</script>
<style scoped lang="scss">
@import url(../submitAccount/push.css);
.form-container {
  padding: 40rpx;
}
.submit_ipt {
  flex: 1;
  text-align: left;
}
.form-inner {
  width: auto;
  margin: 0.5rem auto;
  max-width: 2000rpx;
}

.view {
  box-shadow: none;
  border: 0;
}

.picUpload_btn {
  position: absolute;
  z-index: 4;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  opacity: 0;
  cursor: pointer;
}

.picUpload_wrap {
  width: 360rpx;
  border: 2rpx dashed #d9d9d9;
  position: relative;
  text-align: center;
  line-height: 360rpx;
  overflow: hidden;
  font-size: 32rpx;
  height: 80rpx;
  line-height: 80rpx;
  color: #409eff;
  background: #ecf5ff;
  border-color: #b3d8ff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  cursor: pointer;
  .cover-uploader-icon {
    display: block;
    position: relative;
    z-index: 9;
  }
  .upLoadSingle {
    position: absolute;
    height: 80rpx;
    overflow: hidden;
    z-index: 10;
    opacity: 0;
  }
}

.address_pic {
  width: 400rpx;
  height: 600rpx;
}

.idcard_pic {
  width: 460rpx;
  height: 300rpx;
}

.line {
  overflow: hidden;
}

.pc .line {
  display: flex;
}
.pc .line .idcard {
  width: 50%;
}
.pc .line .title {
  width: 50%;
}

.mobile .line .view {
  float: inherit;
  width: auto;
}

.line .view {
  float: left;
  width: 50%;
}

.title2 {
  margin: 0 0 20rpx 0;
  font-size: 32rpx;
  font-weight: bold;
  color: #ff7e14;
  text-align: center;
}
.title {
  margin: 30rpx 0 0 0;
  font-size: 32rpx;
  text-align: center;
}
.title3 {
  margin: 30rpx 0 0 0;
  font-size: 32rpx;
  text-align: center;
}
.label {
  display: flex;
  justify-content: flex-start;
  width: 220rpx;
  margin-right: 20rpx;
}

.chat {
  position: fixed;
  bottom: 100rpx;
  right: 10rpx;
  width: 100rpx;
  height: 100rpx;
  background: url(../../static/old/h1.png) no-repeat right top;
  background-size: cover;
  border-radius: 50rpx;
  cursor: pointer;
}
.pc .chat {
  right: 400rpx;
  bottom: 400rpx;
  height: 200rpx;
  width: 200rpx;
}
</style>
