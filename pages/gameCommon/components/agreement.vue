<template>
  <view class="agreeWay_wrap">
    <!-- 标题栏 -->
    <view class="agree_head spaceBetween">
      <text>卖家须知</text>
      <IconFont :size="20" icon="close" @click="close" />
    </view>
    <!-- 内容 -->
    <!-- <view  > -->
      <scroll-view class="agree_content" scroll-y >
        <view class="line_title">交易必看</view>
        <view class="line_text">
          <text class="number">1.</text>
          <text>
            卖家在<text class="c-primary"
              >易游盾平台出售游戏账号必须签署电子合同</text
            >才能进行放款（签署合同时需要提供相关个人信息材料，包括但不限于：身份证信息）。
          </text>
        </view>
        <view class="line_text">
          <text class="number">2.</text>
          <text>
            <text class="c-primary"
              >出售游戏账号发生恶意找回，易游盾平台有权提供相关资料，协助有关部门追回应损失；</text
            >若非您本人找回情形，您有义务联系前任号主，若您谢绝配合将按本人找回处理。
          </text>
        </view>
        <view class="line_text">
          <text class="number">3.</text>
          <text
            >买家下单进行<text class="c-primary"
              >账号交易时需支付全额价款，不接受预定金或口头预留等方式。</text
            >
          </text>
        </view>

        <view class="line_text">
          <text class="number">4.</text>
          <text
            >交易过程中，若出现恶意辱骂、恶意行骗、引导站外等现象，吾则，平台有权限制您的账号权限，并视情况封禁账号。</text
          >
        </view>

        <view class="line_text">
          <text class="number">5.</text>
          <text
            >交易完成后，买家若购买包赔，卖家需保证账号正常使用（包括后续账号的人脸验证、游戏内手机号更换等）。<text
              class="c-primary"
              >若您谢绝配合，将按本人找回处理。若您无法解决账号发生的问题，需跟买家进行协商处理，账号若无法正常使用需退还账号号款。</text
            ></text
          >
        </view>

        <view class="line_text">
          <text class="number">6.</text>
          <text
            >交易前有任何疑问，请提前咨询客服，<text class="c-primary"
              >开始交易即视为认同易游盾平台所有规则，最终解释权归杭州市易游盾网络科技有限公司所有。</text
            ></text
          >
        </view>

        <view class="border"></view>

        <view class="line_title">交易须知</view>
        <view class="line_text">
          <text class="number">1.</text>
          <text
            ><text class="c-primary"
              >验号无误前双方都可无责取消，确认验号无误后即为确认交易不接受任何理由取消。</text
            >若因个人原因取消交易，需支付订单金额<text class="c-primary"
              >4%违约金</text
            >，守约方、平台各半收取。
          </text>
        </view>
        <view class="line_text">
          <text class="number">2.</text>
          <text>
            <text class="c-primary"
              >账号进入换绑交接验证码及进入审核后（包含游戏换绑审核期、挂IP等情况），不接受单方面陪付违约金取消，需买卖双方协商处理，</text
            >平台不参与判定责任，最终处理以双方协商一致结果为准。<text
              class="c-primary"
              >若因游戏厂商原因首次换绑失败，买卖双方需配合平台引导进行账号交付，以顺利交付为主，若确实无法进行换绑交付，将按平台方案进行取消订单。</text
            >
          </text>
        </view>
        <view class="line_text">
          <text class="number">3.</text>
          <text>
            <text class="c-primary">
              下单后订单（账号换绑审核期，具体等待交易时长可由双方协定），</text
            >如双方无法协定一致将平台判定方案进行，<text class="c-primary"
              >需要在等待交易时长内响应配合交付。如超出论时长或协定时长，按违约处理。</text
            >
          </text>
        </view>
        <view class="line_text">
          <text class="number">4.</text>
          <text>
            交易期间发现黑号记录，经证实后，可取消交易，各 方均不承担任何责任。
          </text>
        </view>
        <view class="line_text">
          <text class="number">5.</text>
          <text class="c-primary"
            >交易期间出现毁号，行骗等一切不利于账号的行为，
            属于侵权行为，若出现纠纷，按买卖双方协商一致结果
            处理，无法达成一致可通过司法介入，平台配合协助处
            理，不参与判定责任。
          </text>
        </view>
        <view class="line_text">
          <text class="number">6.</text>
          <text
            >双方个人承诺等情形，不在平台担保范围之内请理性
            判断，出现任何风险与损失由您自己承担，平台只针对
            账号找回，人脸包赔等增值业务进行担保。
          </text>
        </view>

        <view class="border"></view>

        <view class="line_title">卖家须知</view>
        <view class="line_text">
          <text class="number">1.</text>
          <text>
            若您的游戏账号在多个平台寄售，请保持寄售价格ㄧ致，否则账号会被下架；若同一账号多次下单后无理由不交付，默认您有违诚信，平台会下架并联系其他平台拉黑处理该账号。若您寄售的游戏账号在其他地方已出或者不再继续寄售，请务必告知，避免不必要的纠纷。
          </text>
        </view>
        <view class="line_text">
          <text class="number">2.</text>
          <text>
            您若拿不合法账号(骗取或本人恶意找回)来易游盾平台，易游盾平台会对该账号进行拉黑下架处理，并保留追究法律责任的权利。
          </text>
        </view>
        <view class="line_text">
          <text class="number">3.</text>
          <text class="c-primary">
            如账号有无法换绑，无法改密等情况交易前或进入交昜组后及时告知客服，交付过程出现无法改密等情况导致交付失败，账号安全问题由卖家自行承担。
          </text>
        </view>

        <image
          src="../../../assets/imgs/sale_agreement_tip.svg"
          mode="widthFix"
          class="agreement_tip"
        />
      </scroll-view>
    <!-- </view> -->
    <view class="agree_foot">
      <view class="kk-btn primary" style="width: 50%" @click="confirm"
        >我已阅读并同意</view
      >
    </view>
  </view>
</template>
<script>
export default {
  data() {
    return {};
  },
  methods: {
    close() {
      this.$emit('close');
    },
    confirm() {
      this.$emit('confirm');
    },
  },
};
</script>
<style lang="scss" scoped>
.agreeWay_wrap {
  width: 100%;
  height: 70vh;
  box-sizing: border-box;
  background-color: #fff;

  display: flex;
  flex-direction: column;
}

.agree_head {
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  padding: 30rpx 40rpx;

  color: #1b1b1b;
  font-family: YouSheBiaoTiHei;
  font-size: 36rpx;
  font-weight: 400;
  line-height: 40rpx;
  letter-spacing: 0.36px;
}

.agree_content {
  padding: 40rpx;

  color: #969696;
  font-family: 'PingFang SC';
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 36rpx; /* 150% */
  letter-spacing: 0.24px;
  box-sizing: border-box;

  flex: auto;
  height: 80%;

  &::-webkit-scrollbar {
    display: none;
    width: 0 !important;
    height: 0 !important;
    -webkit-appearance: none;
    background: transparent;
  }
}

.agree_foot {
  padding: 20rpx;
  box-shadow: -1px -2px 3px 0px rgba(0, 0, 0, 0.05);

  .kk-btn {
    margin: 0 auto;
  }
}

.line_title {
  color: $uni-color-primary;
  font-family: YouSheBiaoTiHei;
  font-size: 32rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 40rpx; /* 125% */
  letter-spacing: 0.32px;

  margin-bottom: 24rpx;
}
.line_text {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40rpx;

  .number {
    margin-right: 8rpx;
  }
}
.border {
  height: 1rpx;
  width: 100%;
  background: #969696;
  margin-bottom: 40rpx;
}

.agreement_tip {
  display: block;
  width: 100%;
  margin-top: 40rpx;
}
</style>
